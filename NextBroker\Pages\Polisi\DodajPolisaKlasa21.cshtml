@page
@model NextBroker.Pages.Polisi.DodajPolisaKlasa21Model
@{
    ViewData["Title"] = "Додај полиса - Класа 21";
}

<h1 style='text-align: center;'>@ViewData["Title"]</h1>

<div class="container-fluid mt-4 px-4">

    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle"></i> @TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle"></i> @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <form method="post" id="polisaForm" class="dodaj-polisa-form">
        @Html.AntiForgeryToken()

        <!-- Basic Information Card -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center" role="button" data-bs-toggle="collapse" data-bs-target="#collapseBasicInfo" aria-expanded="true" aria-controls="collapseBasicInfo">
                <h5 class="mb-0">Основни информации</h5>
                <i class="fas fa-chevron-down"></i>
            </div>
            <div id="collapseBasicInfo" class="collapse show">
                <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Осигурител <span class="text-danger">*</span></label>
                        <select asp-for="Input.KlientiIdOsiguritel"
                                asp-items="Model.Osiguriteli"
                                class="form-select">
                            <option value="">-- Избери осигурител --</option>
                        </select>
                        <span asp-validation-for="Input.KlientiIdOsiguritel" class="text-danger"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Класа на осигурување <span class="text-danger">*</span></label>
                        <select asp-for="Input.KlasiOsiguruvanjeIdKlasa"
                                asp-items="Model.KlasiOsiguruvanje"
                                class="form-select">
                            <option value="">-- Избери класа --</option>
                        </select>
                        <span asp-validation-for="Input.KlasiOsiguruvanjeIdKlasa" class="text-danger"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Продукт</label>
                        <select asp-for="Input.ProduktiIdProizvod"
                                asp-items="Model.Produkti"
                                class="form-select">
                            <option value="">-- Избери продукт --</option>
                        </select>
                        <span asp-validation-for="Input.ProduktiIdProizvod" class="text-danger"></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.BrojNaPolisa" class="form-label">Број на полиса <span class="text-danger">*</span></label>
                        <input asp-for="Input.BrojNaPolisa" class="form-control" type="text" required />
                        <span asp-validation-for="Input.BrojNaPolisa" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.BrojNaPonuda" class="form-label">Број на понуда</label>
                        <input asp-for="Input.BrojNaPonuda" class="form-control" type="number" />
                        <span asp-validation-for="Input.BrojNaPonuda" class="text-danger"></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-10 mb-3">
                        <label class="form-label">Договорувач (пребарувај по МБ, ЕДБ, ЕМБГ, име или назив)</label>
                        <div class="input-group">
                            <button type="button" class="btn btn-outline-secondary btn-sm clear-field" data-target="dogovoruvac" style="width: 30px; height: 30px; padding: 0; margin-right: 5px;">
                                <i class="fas fa-eraser" style="font-size: 0.7rem;"></i>
                            </button>
                            <input type="text" id="dogovoruvacMBSearch" class="form-control"
                                   autocomplete="off"
                                   placeholder="Пребарувај по МБ, ЕДБ, ЕМБГ, име/назив..." />
                            <input type="hidden" asp-for="Input.KlientiIdDogovoruvac" id="KlientiIdDogovoruvac" />
                        </div>
                        <div id="dogovoruvacSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1"
                             style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                        </div>
                        <span asp-validation-for="Input.KlientiIdDogovoruvac" class="text-danger"></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-10 mb-3">
                        <label class="form-label">Осигуреник (пребарувај по МБ, ЕДБ, ЕМБГ, име или назив) <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <button type="button" class="btn btn-outline-secondary btn-sm clear-field" data-target="osigurenik" style="width: 30px; height: 30px; padding: 0; margin-right: 5px;">
                                <i class="fas fa-eraser" style="font-size: 0.7rem;"></i>
                            </button>
                            <input type="text" id="osigurenikMBSearch" class="form-control"
                                   autocomplete="off"
                                   placeholder="Пребарувај по МБ, ЕДБ, ЕМБГ, име/назив..." required />
                            <input type="hidden" asp-for="Input.KlientiIdOsigurenik" id="KlientiIdOsigurenik" required />
                        </div>
                        <div id="osigurenikSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1"
                             style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                        </div>
                        <span class="text-danger field-validation-valid" data-valmsg-for="Input.KlientiIdOsigurenik" data-valmsg-replace="true"></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-10 mb-3">
                        <label class="form-label">Соработник/вработен (пребарувај по МБ, ЕДБ, ЕМБГ, име или назив)</label>
                        <div class="input-group">
                            <button type="button" class="btn btn-outline-secondary btn-sm clear-field" data-target="sorabotnik" style="width: 30px; height: 30px; padding: 0; margin-right: 5px;">
                                <i class="fas fa-eraser" style="font-size: 0.7rem;"></i>
                            </button>
                            <input type="text" id="sorabotnikMBSearch" class="form-control"
                                   autocomplete="off"
                                   placeholder="Пребарувај по МБ, ЕДБ, ЕМБГ, име/назив..." />
                            <input type="hidden" asp-for="Input.KlientiIdSorabotnik" id="KlientiIdSorabotnik" />
                        </div>
                        <div id="sorabotnikSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1"
                             style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.DatumVaziOd" class="form-label">Датум важи од</label>
                        <input asp-for="Input.DatumVaziOd" class="form-control datepicker" type="date" />
                        <span asp-validation-for="Input.DatumVaziOd" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.DatumVaziDo" class="form-label">Датум важи до</label>
                        <input asp-for="Input.DatumVaziDo" class="form-control datepicker" type="date" />
                        <span asp-validation-for="Input.DatumVaziDo" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.DatumNaIzdavanje" class="form-label">Датум на издавање</label>
                        <input asp-for="Input.DatumNaIzdavanje" class="form-control datepicker" type="date" />
                        <span asp-validation-for="Input.DatumNaIzdavanje" class="text-danger"></span>
                    </div>
                </div>
                </div>
            </div>
        </div>

        <!-- Financial Information -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center" role="button" data-bs-toggle="collapse" data-bs-target="#collapseFinancialInfo" aria-expanded="false" aria-controls="collapseFinancialInfo">
                <h5 class="mb-0">Финансиски информации</h5>
                <i class="fas fa-chevron-down"></i>
            </div>
            <div id="collapseFinancialInfo" class="collapse">
                <div class="card-body">


                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.SifrarnikValutiIdValuta" class="form-label">Валута <span class="text-danger">*</span></label>
                        <select asp-for="Input.SifrarnikValutiIdValuta"
                                asp-items="Model.Valuti"
                                class="form-select">
                            <option value="">-- Избери валута --</option>
                        </select>
                        <span asp-validation-for="Input.SifrarnikValutiIdValuta" class="text-danger"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label asp-for="Input.SifrarnikNacinNaPlakjanjeId" class="form-label">Начин на плаќање <span class="text-danger">*</span></label>
                        <select asp-for="Input.SifrarnikNacinNaPlakjanjeId"
                                asp-items="Model.NaciniNaPlakanje"
                                class="form-select">
                            <option value="">-- Избери начин на плаќање --</option>
                        </select>
                        <span asp-validation-for="Input.SifrarnikNacinNaPlakjanjeId" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.TipNaFaktura" class="form-label">Тип на фактура</label>
                        <select asp-for="Input.TipNaFaktura"
                                asp-items="Model.TipoviNaFaktura"
                                class="form-select">
                            <option value="">-- Избери тип на фактура --</option>
                        </select>
                        <span asp-validation-for="Input.TipNaFaktura" class="text-danger"></span>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="form-check mb-3">
                            <input type="checkbox" class="form-check-input" asp-for="Input.Faktoring" id="faktoring">
                            <label class="form-check-label" asp-for="Input.Faktoring">Факторинг</label>
                        </div>
                        <div class="form-check mb-3" style="display: none;">
                            <input type="checkbox" class="form-check-input" asp-for="Input.GeneriranaFakturaIzlezna" id="generiranaFakturaIzlezna">
                            <label class="form-check-label" asp-for="Input.GeneriranaFakturaIzlezna">Генерирана излезна фактура</label>
                        </div>
                        <!-- Add hidden fields to ensure values are still submitted -->
                        <div style="display: none;">
                            <input asp-for="Input.Storno" type="hidden" value="false" />
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label asp-for="Input.SifrarnikValutiIdFranshizaValuta" class="control-label">Валута за франшиза</label>
                            <select asp-for="Input.SifrarnikValutiIdFranshizaValuta" class="form-select"
                                    asp-items="Model.Valuti">
                                <option value="">-- Изберете валута --</option>
                            </select>
                            <span asp-validation-for="Input.SifrarnikValutiIdFranshizaValuta" class="text-danger"></span>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.ProcentFranshiza" class="form-label">Процент франшиза</label>
                        <div class="input-group">
                            <input asp-for="Input.ProcentFranshiza"
                                   class="form-control decimal-input"
                                   type="number"
                                   step="0.01"
                                   min="0"
                                   max="100" />
                            <span class="input-group-text">%</span>
                        </div>
                        <span asp-validation-for="Input.ProcentFranshiza" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.FranshizaIznos" class="form-label">Франшиза износ</label>
                        <div class="input-group">
                            <input asp-for="Input.FranshizaIznos"
                                   class="form-control decimal-input"
                                   type="number"
                                   step="0.0001" />
                        </div>
                        <span asp-validation-for="Input.FranshizaIznos" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.KoregiranaStapkaNaProvizija" class="form-label">Корегирана стапка на провизија</label>
                        <div class="input-group">
                            <input asp-for="Input.KoregiranaStapkaNaProvizija"
                                   class="form-control decimal-input"
                                   type="number"
                                   step="0.0001"
                                   min="0"
                                   max="100" />
                            <span class="input-group-text">%</span>
                        </div>
                        <span asp-validation-for="Input.KoregiranaStapkaNaProvizija" class="text-danger"></span>
                    </div>
                </div>
                </div>
            </div>
        </div>

        <!-- Класа 21 - Животно осигурување Section -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center" role="button" data-bs-toggle="collapse" data-bs-target="#collapseKlasa21" aria-expanded="false" aria-controls="collapseKlasa21">
                <h5 class="mb-0">Класа 21 - Животно осигурување</h5>
                <i class="fas fa-chevron-down"></i>
            </div>
            <div id="collapseKlasa21" class="collapse">
                <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label asp-for="Input.ShifrarnikInvesticiskiFondIdInvesticiskiFond" class="form-label">Инвестициски фонд <span class="text-danger">*</span></label>
                        <select asp-for="Input.ShifrarnikInvesticiskiFondIdInvesticiskiFond"
                                asp-items="Model.InvesticiskiFondoviSelectList"
                                class="form-select">
                            <option value="">-- Избери инвестициски фонд --</option>
                        </select>
                        <span asp-validation-for="Input.ShifrarnikInvesticiskiFondIdInvesticiskiFond" class="text-danger"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label asp-for="Input.NazivNaInvesticiskoDrushtvo" class="form-label">Назив на инвестициско друштво</label>
                        <input asp-for="Input.NazivNaInvesticiskoDrushtvo" class="form-control" type="text" readonly style="background-color: #f8f9fa;" />
                        <small class="form-text text-muted">Автоматски се пополнува врз основа на избраниот инвестициски фонд</small>
                        <span asp-validation-for="Input.NazivNaInvesticiskoDrushtvo" class="text-danger"></span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label asp-for="Input.IdentifikaciskiBrojaNaInvesticiskiFond" class="form-label">Идентификациски број на инвестициски фонд</label>
                        <input asp-for="Input.IdentifikaciskiBrojaNaInvesticiskiFond" class="form-control" type="text" readonly style="background-color: #f8f9fa;" />
                        <small class="form-text text-muted">Автоматски се пополнува врз основа на избраниот инвестициски фонд</small>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.ProcentOdPremijaPoIFGodishnoEdnokratno" class="form-label">Процент од премија по ИФ годишно/еднократно</label>
                        <div class="input-group">
                            <input asp-for="Input.ProcentOdPremijaPoIFGodishnoEdnokratno" class="form-control decimal-input" type="number" step="0.0001" />
                            <span class="input-group-text">%</span>
                        </div>
                        <span asp-validation-for="Input.ProcentOdPremijaPoIFGodishnoEdnokratno" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.ProcentOdPremijaVoOsiguruvanjeGodishnoEdnokratno" class="form-label">Процент од премија во осигурување годишно/еднократно</label>
                        <div class="input-group">
                            <input asp-for="Input.ProcentOdPremijaVoOsiguruvanjeGodishnoEdnokratno" class="form-control decimal-input" type="number" step="0.0001" />
                            <span class="input-group-text">%</span>
                        </div>
                        <span asp-validation-for="Input.ProcentOdPremijaVoOsiguruvanjeGodishnoEdnokratno" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.IznosOdPremijaPoIFGodishnoEdnokratno" class="form-label">Износ од премија по ИФ годишно/еднократно</label>
                        <input asp-for="Input.IznosOdPremijaPoIFGodishnoEdnokratno" class="form-control decimal-input" type="number" step="0.0001" />
                        <span asp-validation-for="Input.IznosOdPremijaPoIFGodishnoEdnokratno" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.IznosOdPremijaVoOsiguruvanjeGodishnoEdnokratno" class="form-label">Износ од премија во осигурување годишно/еднократно</label>
                        <input asp-for="Input.IznosOdPremijaVoOsiguruvanjeGodishnoEdnokratno" class="form-control decimal-input" type="number" step="0.0001" />
                        <span asp-validation-for="Input.IznosOdPremijaVoOsiguruvanjeGodishnoEdnokratno" class="text-danger"></span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.VkupenIznosNaPremijaZaOsiguruvanje" class="form-label">Вкупен износ на премија за осигурување</label>
                        <input asp-for="Input.VkupenIznosNaPremijaZaOsiguruvanje" class="form-control decimal-input" type="number" step="0.0001" />
                        <span asp-validation-for="Input.VkupenIznosNaPremijaZaOsiguruvanje" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.VkupenIznosNaPremijaZaIF" class="form-label">Вкупен износ на премија за ИФ</label>
                        <input asp-for="Input.VkupenIznosNaPremijaZaIF" class="form-control decimal-input" type="number" step="0.0001" />
                        <span asp-validation-for="Input.VkupenIznosNaPremijaZaIF" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.VkupenIznosNaTroshociOdPremija" class="form-label">Вкупен износ на трошоци од премија</label>
                        <input asp-for="Input.VkupenIznosNaTroshociOdPremija" class="form-control decimal-input" type="number" step="0.0001" />
                        <span asp-validation-for="Input.VkupenIznosNaTroshociOdPremija" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.BrojNaUdeliPoIF" class="form-label">Број на удели по ИФ</label>
                        <input asp-for="Input.BrojNaUdeliPoIF" class="form-control" type="number" min="0" />
                        <span asp-validation-for="Input.BrojNaUdeliPoIF" class="text-danger"></span>
                    </div>
                </div>
                </div>
            </div>
        </div>

        <!-- Попусти Section -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center" role="button" data-bs-toggle="collapse" data-bs-target="#collapsePopust" aria-expanded="false" aria-controls="collapsePopust">
                <h5 class="mb-0">Попусти</h5>
                <i class="fas fa-chevron-down"></i>
            </div>
            <div id="collapsePopust" class="collapse">
                <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.ProcentNaPopustZaFakturaVoRok" class="form-label">Процент на попуст за фактура во рок</label>
                        <div class="input-group">
                            <input asp-for="Input.ProcentNaPopustZaFakturaVoRok" class="form-control decimal-input" type="number" step="0.01" min="0" max="100" />
                            <span class="input-group-text">%</span>
                        </div>
                        <span asp-validation-for="Input.ProcentNaPopustZaFakturaVoRok" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.IznosZaPlakjanjeVoRok" class="form-label">Износ за плаќање во рок</label>
                        <input asp-for="Input.IznosZaPlakjanjeVoRok" class="form-control decimal-input" type="number" step="0.0001" />
                        <span asp-validation-for="Input.IznosZaPlakjanjeVoRok" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.ProcentKomercijalenPopust" class="form-label">Процент комерцијален попуст</label>
                        <div class="input-group">
                            <input asp-for="Input.ProcentKomercijalenPopust" class="form-control decimal-input" type="number" step="0.01" min="0" max="100" />
                            <span class="input-group-text">%</span>
                        </div>
                        <span asp-validation-for="Input.ProcentKomercijalenPopust" class="text-danger"></span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.ProcentFinansiski" class="form-label">Процент финансиски</label>
                        <div class="input-group">
                            <input asp-for="Input.ProcentFinansiski" class="form-control decimal-input" type="number" step="0.01" min="0" max="100" />
                            <span class="input-group-text">%</span>
                        </div>
                        <span asp-validation-for="Input.ProcentFinansiski" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.PremijaZaNaplata" class="form-label">Премија за наплата</label>
                        <input asp-for="Input.PremijaZaNaplata" class="form-control decimal-input" type="number" step="0.0001" />
                        <span asp-validation-for="Input.PremijaZaNaplata" class="text-danger"></span>
                    </div>
                </div>
                </div>
            </div>
        </div>

        <!-- Осигурени суми и премии Section -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center" role="button" data-bs-toggle="collapse" data-bs-target="#collapseOsigureniSumi" aria-expanded="false" aria-controls="collapseOsigureniSumi">
                <h5 class="mb-0">Осигурени суми и премии</h5>
                <i class="fas fa-chevron-down"></i>
            </div>
            <div id="collapseOsigureniSumi" class="collapse">
                <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label asp-for="Input.OsigurenaSumaZaDozhivuvanje" class="form-label">Осигурена сума за доживување</label>
                        <input asp-for="Input.OsigurenaSumaZaDozhivuvanje" class="form-control" type="text" />
                        <span asp-validation-for="Input.OsigurenaSumaZaDozhivuvanje" class="text-danger"></span>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label asp-for="Input.OsigurenaSumaZaSmrtOdBolest" class="form-label">Осигурена сума за смрт од болест</label>
                        <input asp-for="Input.OsigurenaSumaZaSmrtOdBolest" class="form-control" type="text" />
                        <span asp-validation-for="Input.OsigurenaSumaZaSmrtOdBolest" class="text-danger"></span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label asp-for="Input.OsigurenaSumaZaSmrtOdNezgoda" class="form-label">Осигурена сума за смрт од незгода</label>
                        <input asp-for="Input.OsigurenaSumaZaSmrtOdNezgoda" class="form-control" type="text" />
                        <span asp-validation-for="Input.OsigurenaSumaZaSmrtOdNezgoda" class="text-danger"></span>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label asp-for="Input.PrivremenaOsiguritelnaZastita" class="form-label">Привремена осигурителна заштита</label>
                        <input asp-for="Input.PrivremenaOsiguritelnaZastita" class="form-control" type="text" />
                        <span asp-validation-for="Input.PrivremenaOsiguritelnaZastita" class="text-danger"></span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label asp-for="Input.PremijaGodishnaEdnokratna" class="form-label">Премија годишна/еднократна</label>
                        <input asp-for="Input.PremijaGodishnaEdnokratna" class="form-control" type="text" />
                        <span asp-validation-for="Input.PremijaGodishnaEdnokratna" class="text-danger"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label asp-for="Input.VkupnaPremijaGodishna" class="form-label">Вкупна премија годишна</label>
                        <input asp-for="Input.VkupnaPremijaGodishna" class="form-control decimal-input" type="number" step="0.0001" />
                        <span asp-validation-for="Input.VkupnaPremijaGodishna" class="text-danger"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label asp-for="Input.VkupnaPremijaEdnokratna" class="form-label">Вкупна премија еднократна</label>
                        <input asp-for="Input.VkupnaPremijaEdnokratna" class="form-control decimal-input" type="number" step="0.0001" />
                        <span asp-validation-for="Input.VkupnaPremijaEdnokratna" class="text-danger"></span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label asp-for="Input.DoplatociZaPodgodishnoPlakjanje" class="form-label">Доплатоци за подгодишно плаќање</label>
                        <input asp-for="Input.DoplatociZaPodgodishnoPlakjanje" class="form-control decimal-input" type="number" step="0.0001" />
                        <span asp-validation-for="Input.DoplatociZaPodgodishnoPlakjanje" class="text-danger"></span>
                    </div>
                </div>
                </div>
            </div>
        </div>

        <!-- Дополнително осигурување - Класа 1 (Незгоди) Section -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center" role="button" data-bs-toggle="collapse" data-bs-target="#collapseKlasa1" aria-expanded="false" aria-controls="collapseKlasa1">
                <h5 class="mb-0">Дополнително осигурување - Класа 1 (Незгоди)</h5>
                <i class="fas fa-chevron-down"></i>
            </div>
            <div id="collapseKlasa1" class="collapse">
                <div class="card-body">
                <div id="klasa1Entries">
                    <!-- Klasa 1 entries will be added here dynamically -->
                </div>
                <div class="text-muted text-center" id="klasa1EmptyMessage">
                    <em>Нема додадени записи за дополнително осигурување од незгоди.</em>
                </div>
                <div class="text-center mt-3">
                    <button type="button" class="btn btn-success" id="addKlasa1Entry">
                        <i class="bi bi-plus"></i> Додај запис
                    </button>
                </div>
                </div>
            </div>
        </div>

        <!-- Дополнително осигурување - Класа 2 (Здравствено) Section -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center" role="button" data-bs-toggle="collapse" data-bs-target="#collapseKlasa2" aria-expanded="false" aria-controls="collapseKlasa2">
                <h5 class="mb-0">Дополнително осигурување - Класа 2 (Здравствено)</h5>
                <i class="fas fa-chevron-down"></i>
            </div>
            <div id="collapseKlasa2" class="collapse">
                <div class="card-body">
                <div id="klasa2Entries">
                    <!-- Klasa 2 entries will be added here dynamically -->
                </div>
                <div class="text-muted text-center" id="klasa2EmptyMessage">
                    <em>Нема додадени записи за дополнително здравствено осигурување.</em>
                </div>
                <div class="text-center mt-3">
                    <button type="button" class="btn btn-success" id="addKlasa2Entry">
                        <i class="bi bi-plus"></i> Додај запис
                    </button>
                </div>
                </div>
            </div>
        </div>

        <!-- Submit Button -->
        <div class="row mt-4">
            <div class="col-12">
                <button type="submit" class="btn btn-primary btn-lg">
                    <i class="bi bi-check-circle"></i> Зачувај полиса
                </button>
                <a href="/Polisi/ListaPolisi" class="btn btn-secondary btn-lg ms-2">
                    <i class="bi bi-arrow-left"></i> Назад
                </a>
            </div>
        </div>
    </form>

    <!-- Инвестициски Фондови и Друштва Management Section (Outside main form) -->
    <div class="accordion mt-4" id="investiciskiFondoviAccordion">
        <div class="accordion-item">
            <h2 class="accordion-header" id="headingInvesticiskiFondovi">
                <button class="accordion-button collapsed fs-5 fw-bold" type="button" data-bs-toggle="collapse" data-bs-target="#collapseInvesticiskiFondovi" aria-expanded="false" aria-controls="collapseInvesticiskiFondovi" style="background-color: #f8f9fa;">
                    Инвестициски Фондови и Друштва
                </button>
            </h2>
            <div id="collapseInvesticiskiFondovi" class="accordion-collapse collapse" aria-labelledby="headingInvesticiskiFondovi" data-bs-parent="#investiciskiFondoviAccordion">
                <div class="accordion-body p-4">
                    <!-- Nested Accordion for Investment Funds and Companies -->
                    <div class="accordion" id="nestedInvesticiskiAccordion">

                        <!-- Друштво за инвестициски фонд Section -->
                        <div class="accordion-item mb-3">
                            <h2 class="accordion-header" id="headingDrushtvo">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseDrushtvo" aria-expanded="false" aria-controls="collapseDrushtvo">
                                    Друштво за инвестициски фонд
                                </button>
                            </h2>
                            <div id="collapseDrushtvo" class="accordion-collapse collapse" aria-labelledby="headingDrushtvo" data-bs-parent="#nestedInvesticiskiAccordion">
                                <div class="accordion-body">
                                    <form method="post" asp-page-handler="AddInvesticiskoDrushtvo">
                                        <div class="row mb-3">
                                            <div class="col-md-8">
                                                <label class="form-label">Назив на друштвото</label>
                                                <input type="text" class="form-control" name="naziv" required placeholder="Внесете назив на друштвото за инвестициски фонд" />
                                            </div>
                                            <div class="col-md-4 d-flex align-items-end">
                                                <button type="submit" class="btn btn-success">
                                                    <i class="bi bi-plus-circle"></i> Додади друштво
                                                </button>
                                            </div>
                                        </div>
                                    </form>

                                    <!-- Display existing companies -->
                                    @if (Model.InvesticiskaDrushtva != null && Model.InvesticiskaDrushtva.Any())
                                    {
                                        <div class="mt-4">
                                            <h6>Постоечки друштва:</h6>
                                            <div class="table-responsive">
                                                <table class="table table-sm table-striped">
                                                    <thead>
                                                        <tr>
                                                            <th>ID</th>
                                                            <th>Назив</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        @foreach (var drushtvo in Model.InvesticiskaDrushtva)
                                                        {
                                                            <tr>
                                                                <td>@drushtvo.Id</td>
                                                                <td>@drushtvo.Naziv</td>
                                                            </tr>
                                                        }
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>

                        <!-- Инвестициски фонд Section -->
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingFond">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFond" aria-expanded="false" aria-controls="collapseFond">
                                    Инвестициски фонд
                                </button>
                            </h2>
                            <div id="collapseFond" class="accordion-collapse collapse" aria-labelledby="headingFond" data-bs-parent="#nestedInvesticiskiAccordion">
                                <div class="accordion-body">
                                    <form method="post" asp-page-handler="AddInvesticiskiFond">
                                        <div class="row mb-3">
                                            <div class="col-md-4">
                                                <label class="form-label">Назив на фондот</label>
                                                <input type="text" class="form-control" name="naziv" required placeholder="Внесете назив на инвестицискиот фонд" />
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">Идентификациски број</label>
                                                <input type="text" class="form-control" name="identifikaciskiBroj" required placeholder="Внесете идентификациски број" />
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">Друштво</label>
                                                <select class="form-control" name="drushtvoId" required>
                                                    <option value="">-- Изберете друштво --</option>
                                                    @if (Model.InvesticiskaDrushtva != null)
                                                    {
                                                        @foreach (var drushtvo in Model.InvesticiskaDrushtva)
                                                        {
                                                            <option value="@drushtvo.Id">@drushtvo.Naziv</option>
                                                        }
                                                    }
                                                </select>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-12">
                                                <button type="submit" class="btn btn-success">
                                                    <i class="bi bi-plus-circle"></i> Додади фонд
                                                </button>
                                            </div>
                                        </div>
                                    </form>

                                    <!-- Display existing funds -->
                                    @if (Model.InvesticiskiFondovi != null && Model.InvesticiskiFondovi.Any())
                                    {
                                        <div class="mt-4">
                                            <h6>Постоечки фондови:</h6>
                                            <div class="table-responsive">
                                                <table class="table table-sm table-striped">
                                                    <thead>
                                                        <tr>
                                                            <th>ID</th>
                                                            <th>Назив</th>
                                                            <th>Идентификациски број</th>
                                                            <th>Друштво</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        @foreach (var fond in Model.InvesticiskiFondovi)
                                                        {
                                                            <tr>
                                                                <td>@fond.Id</td>
                                                                <td>@fond.Naziv</td>
                                                                <td>@fond.IdentifikaciskiBrojaNaInvesticiskiFond</td>
                                                                <td>@fond.DrushtvoNaziv</td>
                                                            </tr>
                                                        }
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Handle collapse icon rotation for nested accordions
            $('.accordion-collapse').on('show.bs.collapse hide.bs.collapse', function() {
                $(this)
                    .parent()
                    .find('.accordion-header')
                    .find('.accordion-button')
                    .toggleClass('collapsed');
            });

            // Auto-dismiss alerts after 5 seconds
            setTimeout(function() {
                $('.alert').fadeOut('slow');
            }, 5000);

            // Form validation for investment company
            $('form[asp-page-handler="AddInvesticiskoDrushtvo"]').on('submit', function(e) {
                var naziv = $(this).find('input[name="naziv"]').val().trim();
                if (!naziv) {
                    e.preventDefault();
                    alert('Внесете назив на друштвото.');
                    return false;
                }
            });

            // Form validation for investment fund
            $('form[asp-page-handler="AddInvesticiskiFond"]').on('submit', function(e) {
                var naziv = $(this).find('input[name="naziv"]').val().trim();
                var identifikaciskiBroj = $(this).find('input[name="identifikaciskiBroj"]').val().trim();
                var drushtvoId = $(this).find('select[name="drushtvoId"]').val();

                if (!naziv) {
                    e.preventDefault();
                    alert('Внесете назив на фондот.');
                    return false;
                }

                if (!identifikaciskiBroj) {
                    e.preventDefault();
                    alert('Внесете идентификациски број.');
                    return false;
                }

                if (!drushtvoId) {
                    e.preventDefault();
                    alert('Изберете друштво.');
                    return false;
                }
            });
        });

        // Client search functionality
        function setupClientSearch(searchInputId, resultsId, hiddenInputId) {
            const searchInput = document.getElementById(searchInputId);
            const resultsDiv = document.getElementById(resultsId);
            const hiddenInput = document.getElementById(hiddenInputId);

            if (!searchInput) return;

            searchInput.addEventListener('input', function() {
                const query = this.value.trim();
                if (query.length < 2) {
                    resultsDiv.style.display = 'none';
                    return;
                }

                fetch(`/api/Klienti/Search?query=${encodeURIComponent(query)}`)
                    .then(response => response.json())
                    .then(data => {
                        resultsDiv.innerHTML = '';
                        if (data && data.length > 0) {
                            data.forEach(client => {
                                const div = document.createElement('div');
                                div.className = 'p-2 border-bottom cursor-pointer hover-bg-light';
                                div.innerHTML = `
                                    <strong>${client.naziv || client.ime + ' ' + client.prezime}</strong><br>
                                    <small class="text-muted">
                                        ${client.mb ? 'МБ: ' + client.mb : ''}
                                        ${client.edb ? ' ЕДБ: ' + client.edb : ''}
                                        ${client.embg ? ' ЕМБГ: ' + client.embg : ''}
                                    </small>
                                `;
                                div.addEventListener('click', function() {
                                    searchInput.value = client.naziv || (client.ime + ' ' + client.prezime);
                                    hiddenInput.value = client.id;
                                    resultsDiv.style.display = 'none';
                                });
                                resultsDiv.appendChild(div);
                            });
                            resultsDiv.style.display = 'block';
                        } else {
                            resultsDiv.style.display = 'none';
                        }
                    })
                    .catch(error => {
                        console.error('Error searching clients:', error);
                        resultsDiv.style.display = 'none';
                    });
            });

            // Hide results when clicking outside
            document.addEventListener('click', function(e) {
                if (!searchInput.contains(e.target) && !resultsDiv.contains(e.target)) {
                    resultsDiv.style.display = 'none';
                }
            });
        }

        // Clear field functionality
        document.querySelectorAll('.clear-field').forEach(button => {
            button.addEventListener('click', function() {
                const target = this.getAttribute('data-target');
                if (target === 'dogovoruvac') {
                    document.getElementById('dogovoruvacMBSearch').value = '';
                    document.getElementById('KlientiIdDogovoruvac').value = '';
                } else if (target === 'osigurenik') {
                    document.getElementById('osigurenikMBSearch').value = '';
                    document.getElementById('KlientiIdOsigurenik').value = '';
                } else if (target === 'sorabotnik') {
                    document.getElementById('sorabotnikMBSearch').value = '';
                    document.getElementById('KlientiIdSorabotnik').value = '';
                }
            });
        });

        // Setup client searches
        setupClientSearch('dogovoruvacMBSearch', 'dogovoruvacSearchResults', 'KlientiIdDogovoruvac');
        setupClientSearch('osigurenikMBSearch', 'osigurenikSearchResults', 'KlientiIdOsigurenik');
        setupClientSearch('sorabotnikMBSearch', 'sorabotnikSearchResults', 'KlientiIdSorabotnik');

        // Dynamic entries management
        let klasa1Counter = 0;
        let klasa2Counter = 0;

        // Add Klasa 1 entry
        document.getElementById('addKlasa1Entry').addEventListener('click', function() {
            const container = document.getElementById('klasa1Entries');
            const emptyMessage = document.getElementById('klasa1EmptyMessage');

            const entryHtml = `
                <div class="border rounded p-3 mb-3 klasa1-entry" data-index="${klasa1Counter}">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0">Запис за незгоди #${klasa1Counter + 1}</h6>
                        <button type="button" class="btn btn-danger btn-sm remove-klasa1-entry">
                            <i class="bi bi-trash"></i> Отстрани
                        </button>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Осигурена сума за смрт од незгода</label>
                            <input name="Input.Klasa1Entries[${klasa1Counter}].OsigurenaSumaZaSmrtOdNezgoda"
                                   class="form-control decimal-input" type="number" step="0.01" />
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Осигурена сума за 100% траен инвалидитет</label>
                            <input name="Input.Klasa1Entries[${klasa1Counter}].OsigurenaSumaZa100ProcTraenInvaliditet"
                                   class="form-control decimal-input" type="number" step="0.01" />
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Осигурена сума за траен инвалидитет</label>
                            <input name="Input.Klasa1Entries[${klasa1Counter}].OsigurenaSumaZaTraenInvaliditet"
                                   class="form-control decimal-input" type="number" step="0.01" />
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Дневен надомест</label>
                            <input name="Input.Klasa1Entries[${klasa1Counter}].DnevenNadomest"
                                   class="form-control decimal-input" type="number" step="0.01" />
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Премија незгода годишна</label>
                            <input name="Input.Klasa1Entries[${klasa1Counter}].PremijaNezgodaGodishna"
                                   class="form-control decimal-input" type="number" step="0.01" />
                        </div>
                    </div>
                </div>
            `;

            container.insertAdjacentHTML('beforeend', entryHtml);
            emptyMessage.style.display = 'none';
            klasa1Counter++;
        });

        // Add Klasa 2 entry
        document.getElementById('addKlasa2Entry').addEventListener('click', function() {
            const container = document.getElementById('klasa2Entries');
            const emptyMessage = document.getElementById('klasa2EmptyMessage');

            const entryHtml = `
                <div class="border rounded p-3 mb-3 klasa2-entry" data-index="${klasa2Counter}">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0">Запис за здравствено осигурување #${klasa2Counter + 1}</h6>
                        <button type="button" class="btn btn-danger btn-sm remove-klasa2-entry">
                            <i class="bi bi-trash"></i> Отстрани
                        </button>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Осигурена сума за тешко болни состојби</label>
                            <input name="Input.Klasa2Entries[${klasa2Counter}].OsigurenaSumaZaTeskoBolniSostojbi"
                                   class="form-control decimal-input" type="number" step="0.01" />
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Премија тешко болни состојби годишна</label>
                            <input name="Input.Klasa2Entries[${klasa2Counter}].PremijaTeshkoBolniSostojbiGodishna"
                                   class="form-control decimal-input" type="number" step="0.01" />
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Осигурена сума за операции</label>
                            <input name="Input.Klasa2Entries[${klasa2Counter}].OsigurenaSumaZaOperacii"
                                   class="form-control decimal-input" type="number" step="0.01" />
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Премија за операции годишна</label>
                            <input name="Input.Klasa2Entries[${klasa2Counter}].PremijaZaOperaciiGodishna"
                                   class="form-control decimal-input" type="number" step="0.01" />
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Осигурена сума за трајна неспособност</label>
                            <input name="Input.Klasa2Entries[${klasa2Counter}].OsigurenaSumaZaTrajnaNesposobnost"
                                   class="form-control decimal-input" type="number" step="0.01" />
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Премија за трајна неспособност годишна</label>
                            <input name="Input.Klasa2Entries[${klasa2Counter}].PremijaZaTrajnaNesposobnostGodishna"
                                   class="form-control decimal-input" type="number" step="0.01" />
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Осигурена сума за хируршки интервенции</label>
                            <input name="Input.Klasa2Entries[${klasa2Counter}].OsigurenaSumaZaHirushkiIntervencii"
                                   class="form-control decimal-input" type="number" step="0.01" />
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Премија за хируршки интервенции годишна</label>
                            <input name="Input.Klasa2Entries[${klasa2Counter}].PremijaZaHirushkiIntervenciiGodishna"
                                   class="form-control decimal-input" type="number" step="0.01" />
                        </div>
                    </div>
                </div>
            `;

            container.insertAdjacentHTML('beforeend', entryHtml);
            emptyMessage.style.display = 'none';
            klasa2Counter++;
        });

        // Remove entries event delegation
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('remove-klasa1-entry') || e.target.closest('.remove-klasa1-entry')) {
                const entry = e.target.closest('.klasa1-entry');
                entry.remove();

                // Show empty message if no entries left
                const container = document.getElementById('klasa1Entries');
                const emptyMessage = document.getElementById('klasa1EmptyMessage');
                if (container.children.length === 0) {
                    emptyMessage.style.display = 'block';
                }

                // Renumber remaining entries
                renumberKlasa1Entries();
            }

            if (e.target.classList.contains('remove-klasa2-entry') || e.target.closest('.remove-klasa2-entry')) {
                const entry = e.target.closest('.klasa2-entry');
                entry.remove();

                // Show empty message if no entries left
                const container = document.getElementById('klasa2Entries');
                const emptyMessage = document.getElementById('klasa2EmptyMessage');
                if (container.children.length === 0) {
                    emptyMessage.style.display = 'block';
                }

                // Renumber remaining entries
                renumberKlasa2Entries();
            }
        });

        // Renumber functions
        function renumberKlasa1Entries() {
            const entries = document.querySelectorAll('.klasa1-entry');
            entries.forEach((entry, index) => {
                entry.setAttribute('data-index', index);
                entry.querySelector('h6').textContent = `Запис за незгоди #${index + 1}`;

                // Update input names
                const inputs = entry.querySelectorAll('input');
                inputs.forEach(input => {
                    const name = input.getAttribute('name');
                    if (name) {
                        const newName = name.replace(/Klasa1Entries\[\d+\]/, `Klasa1Entries[${index}]`);
                        input.setAttribute('name', newName);
                    }
                });
            });
        }

        function renumberKlasa2Entries() {
            const entries = document.querySelectorAll('.klasa2-entry');
            entries.forEach((entry, index) => {
                entry.setAttribute('data-index', index);
                entry.querySelector('h6').textContent = `Запис за здравствено осигурување #${index + 1}`;

                // Update input names
                const inputs = entry.querySelectorAll('input');
                inputs.forEach(input => {
                    const name = input.getAttribute('name');
                    if (name) {
                        const newName = name.replace(/Klasa2Entries\[\d+\]/, `Klasa2Entries[${index}]`);
                        input.setAttribute('name', newName);
                    }
                });
            });
        }

        // Form validation
        document.getElementById('polisaForm').addEventListener('submit', function(e) {
            const requiredFields = [
                { id: 'Input_KlientiIdOsiguritel', name: 'Осигурител' },
                { id: 'Input_KlasiOsiguruvanjeIdKlasa', name: 'Класа на осигурување' },
                { id: 'Input_BrojNaPolisa', name: 'Број на полиса' },
                { id: 'KlientiIdDogovoruvac', name: 'Договорувач' },
                { id: 'Input_SifrarnikValutiIdValuta', name: 'Валута' },
                { id: 'Input_SifrarnikNacinNaPlakjanjeId', name: 'Начин на плаќање' },
                { id: 'Input_ShifrarnikInvesticiskiFondIdInvesticiskiFond', name: 'Инвестициски фонд' }
            ];

            for (let field of requiredFields) {
                const element = document.getElementById(field.id);
                if (!element || !element.value.trim()) {
                    alert(`Полето "${field.name}" е задолжително.`);
                    if (element) element.focus();
                    e.preventDefault();
                    return false;
                }
            }
        });

        // Handle collapse icon rotation
        $('.collapse').on('show.bs.collapse hide.bs.collapse', function() {
            $(this)
                .parent()
                .find('.card-header')
                .find('.fas')
                .toggleClass('rotated');
        });

        // Client search functionality - matches AO page exactly
        function createSearchFunctionality(searchInputId, resultsContainerId, hiddenInputId, searchHandler = 'SearchKlienti') {
            let searchTimeout;
            const antiForgeryToken = $('input[name="__RequestVerificationToken"]').val();

            $(`#${searchInputId}`).on('input', function() {
                clearTimeout(searchTimeout);
                const searchTerm = $(this).val();
                const resultsDiv = $(`#${resultsContainerId}`);

                if (searchTerm.length < 1) {
                    resultsDiv.hide();
                    return;
                }

                searchTimeout = setTimeout(function() {
                    $.ajax({
                        url: `?handler=${searchHandler}`,
                        type: 'GET',
                        data: { mb: searchTerm },
                        headers: {
                            "RequestVerificationToken": antiForgeryToken
                        },
                        success: function(data) {
                            if (data && data.length > 0) {
                                let html = '<div class="list-group">';
                                data.forEach(item => {
                                    let displayText = '';
                                    if (item.tip === 'P') {
                                        displayText = `${item.naziv}`;
                                        let identifiers = [];
                                        if (item.mb) identifiers.push(`МБ: ${item.mb}`);
                                        if (item.edb) identifiers.push(`ЕДБ: ${item.edb}`);
                                        if (identifiers.length > 0) {
                                            displayText += ` (${identifiers.join(', ')})`;
                                        }
                                    } else {
                                        displayText = `${item.ime} ${item.prezime}`;
                                        let identifiers = [];
                                        if (item.embg) identifiers.push(`ЕМБГ: ${item.embg}`);
                                        if (item.mb) identifiers.push(`МБ: ${item.mb}`);
                                        if (identifiers.length > 0) {
                                            displayText += ` (${identifiers.join(', ')})`;
                                        }
                                    }
                                    html += `<a href="#" class="list-group-item list-group-item-action" data-id="${item.id}">
                                              ${displayText}
                                           </a>`;
                                });
                                html += '</div>';
                                resultsDiv.html(html).show();
                            } else {
                                resultsDiv.html(`
                                    <div class="list-group">
                                        <div class="list-group-item text-center">
                                            <p class="mb-2">Нема пронајдени резултати</p>
                                            <button type="button" class="btn btn-primary" onclick="openAddClientWindow('${
                                                searchInputId === 'dogovoruvacMBSearch' ? 'dogovoruvac' :
                                                searchInputId === 'osigurenikMBSearch' ? 'osigurenik' : 'sorabotnik'
                                            }')">
                                                <i class="fas fa-plus"></i> Додај клиент
                                            </button>
                                        </div>
                                    </div>
                                `).show();
                            }
                        },
                        error: function() {
                            resultsDiv.html(`
                                <div class="list-group">
                                    <div class="list-group-item text-center">
                                        <p class="text-danger mb-2">Грешка при пребарување</p>
                                        <button type="button" class="btn btn-primary" onclick="openAddClientWindow('${
                                            searchInputId === 'dogovoruvacMBSearch' ? 'dogovoruvac' :
                                            searchInputId === 'osigurenikMBSearch' ? 'osigurenik' : 'sorabotnik'
                                        }')">
                                            <i class="fas fa-plus"></i> Додај клиент
                                        </button>
                                    </div>
                                </div>
                            `).show();
                        }
                    });
                }, 300);
            });

            // Handle selection
            $(document).on('click', `#${resultsContainerId} .list-group-item`, function(e) {
                e.preventDefault();
                const id = $(this).data('id');
                const displayText = $(this).text();
                $(`#${searchInputId}`).val(displayText.trim());
                $(`#${hiddenInputId}`).val(id);
                $(`#${resultsContainerId}`).hide();
            });

            // Hide results when clicking outside
            $(document).click(function(e) {
                if (!$(e.target).closest(`#${searchInputId}, #${resultsContainerId}`).length) {
                    $(`#${resultsContainerId}`).hide();
                }
            });
        }

        // Initialize search for all fields
        createSearchFunctionality('dogovoruvacMBSearch', 'dogovoruvacSearchResults', 'KlientiIdDogovoruvac');
        createSearchFunctionality('osigurenikMBSearch', 'osigurenikSearchResults', 'KlientiIdOsigurenik');
        createSearchFunctionality('sorabotnikMBSearch', 'sorabotnikSearchResults', 'KlientiIdSorabotnik', 'SearchSorabotnici');

        // Add click handler for clear field buttons
        $('.clear-field').click(function() {
            const target = $(this).data('target');
            switch(target) {
                case 'dogovoruvac':
                    $('#dogovoruvacMBSearch').val('');
                    $('#KlientiIdDogovoruvac').val('');
                    $('#dogovoruvacSearchResults').hide();
                    break;
                case 'osigurenik':
                    $('#osigurenikMBSearch').val('');
                    $('#KlientiIdOsigurenik').val('');
                    $('#osigurenikSearchResults').hide();
                    break;
                case 'sorabotnik':
                    $('#sorabotnikMBSearch').val('');
                    $('#KlientiIdSorabotnik').val('');
                    $('#sorabotnikSearchResults').hide();
                    break;
            }
        });

        // Function to open add client window
        function openAddClientWindow(source) {
            const url = '/Klienti/DodajKlient';
            const popup = window.open(url, 'AddClient', 'width=800,height=600,scrollbars=yes,resizable=yes');

            // Store the source for later use
            window.clientSource = source;

            // Focus the popup
            if (popup) {
                popup.focus();
            }
        }

        // Listen for messages from the popup
        window.addEventListener('message', function(event) {
            if (event.data.type === 'clientAdded') {
                // Clear the search field based on the source
                if (event.data.source === 'dogovoruvac') {
                    $('#dogovoruvacMBSearch').val('');
                    $('#KlientiIdDogovoruvac').val('');
                    $('#dogovoruvacSearchResults').hide();
                } else if (event.data.source === 'osigurenik') {
                    $('#osigurenikMBSearch').val('');
                    $('#KlientiIdOsigurenik').val('');
                    $('#osigurenikSearchResults').hide();
                } else if (event.data.source === 'sorabotnik') {
                    $('#sorabotnikMBSearch').val('');
                    $('#KlientiIdSorabotnik').val('');
                    $('#sorabotnikSearchResults').hide();
                }
            }
        });

        // Handle investment fund selection to auto-populate identification number and company name
        $('#Input_ShifrarnikInvesticiskiFondIdInvesticiskiFond').change(function() {
            const fondId = $(this).val();
            const identifikaciskiBrojField = $('#Input_IdentifikaciskiBrojaNaInvesticiskiFond');
            const drushtvoField = $('#Input_NazivNaInvesticiskoDrushtvo');

            if (fondId) {
                // Make AJAX call to get fund details including company name
                $.ajax({
                    url: '?handler=GetInvestmentFundDetails',
                    type: 'GET',
                    data: { fondId: fondId },
                    headers: {
                        "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(data) {
                        if (data && data.identifikaciskiBroj) {
                            identifikaciskiBrojField.val(data.identifikaciskiBroj);
                        } else {
                            identifikaciskiBrojField.val('');
                        }

                        if (data && data.drushtvoNaziv) {
                            drushtvoField.val(data.drushtvoNaziv);
                        } else {
                            drushtvoField.val('');
                        }
                    },
                    error: function() {
                        identifikaciskiBrojField.val('');
                        drushtvoField.val('');
                        console.error('Грешка при добивање на податоци за инвестицискиот фонд');
                    }
                });
            } else {
                identifikaciskiBrojField.val('');
                drushtvoField.val('');
            }
        });

        // Optional: Add one entry of each type by default for testing
        // Uncomment the lines below if you want to start with sample entries
        // document.getElementById('addKlasa1Entry').click();
        // document.getElementById('addKlasa2Entry').click();
    </script>
}

@section Styles {
    <style>
        .dodaj-polisa-form .card {
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .dodaj-polisa-form .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .dodaj-polisa-form .form-label {
            font-weight: 500;
            color: #495057;
        }

        .cursor-pointer {
            cursor: pointer;
        }

        .hover-bg-light:hover {
            background-color: #f8f9fa;
        }

        .decimal-input::-webkit-outer-spin-button,
        .decimal-input::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        .decimal-input[type=number] {
            -moz-appearance: textfield;
        }

        .klasa1-entry, .klasa2-entry {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            transition: all 0.3s ease;
        }

        .klasa1-entry:hover, .klasa2-entry:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .remove-klasa1-entry, .remove-klasa2-entry {
            transition: all 0.2s ease;
        }

        .remove-klasa1-entry:hover, .remove-klasa2-entry:hover {
            transform: scale(1.05);
        }

        #klasa1EmptyMessage, #klasa2EmptyMessage {
            padding: 2rem;
            text-align: center;
            background-color: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 0.375rem;
        }

        .card-header[data-bs-toggle="collapse"] {
            cursor: pointer;
        }

        .card-header[data-bs-toggle="collapse"]:hover {
            background-color: rgba(0,0,0,.03);
        }

        .fas {
            transition: transform 0.3s ease-in-out;
        }

        .fas.rotated {
            transform: rotate(180deg);
        }

        /* Search results styling - matches AO page */
        #dogovoruvacSearchResults .list-group-item.text-center,
        #osigurenikSearchResults .list-group-item.text-center,
        #sorabotnikSearchResults .list-group-item.text-center {
            padding: 1rem;
        }

        #dogovoruvacSearchResults .list-group-item.text-center p,
        #osigurenikSearchResults .list-group-item.text-center p,
        #sorabotnikSearchResults .list-group-item.text-center p {
            color: #6c757d;
            margin-bottom: 0.75rem;
        }

        #dogovoruvacSearchResults .btn-primary,
        #osigurenikSearchResults .btn-primary,
        #sorabotnikSearchResults .btn-primary {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
        }
    </style>
}
