@page
@model NextBroker.Pages.Provizija.PresmetkaProvizijaSintetikiModel
@{
    ViewData["Title"] = "Пресметка провизија синтетики";
    Layout = "_Layout";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Пресметка провизија синтетики</h3>
                    <div class="card-tools">
                        <span class="badge badge-info">Вкупно: @Model.BatchData.Rows.Count пресметки</span>
                    </div>
                </div>
                <div class="card-body">
                    @Html.AntiForgeryToken()
                    @if (Model.BatchData.Rows.Count > 0)
                    {
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped table-hover">
                                <thead class="thead-dark">
                                    <tr>
                                        <th>Batch ID</th>
                                        <th>Број на спецификација</th>
                                        <th>Селектирана пресметка од</th>
                                        <th>Селектирана пресметка до</th>
                                        <th>Датум на креирање</th>
                                        <th>Акции</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @for (int i = 0; i < Model.BatchData.Rows.Count; i++)
                                    {
                                        var row = Model.BatchData.Rows[i];
                                        string batchId = $"batch_{i}";
                                        <tr>
                                            <td>
                                                <span class="badge badge-light text-dark">@row["BatchId"]</span>
                                            </td>
                                            <td>@row["BrojNaSpecifikacijaProvizija"]</td>
                                            <td>
                                                @if (row["SelektiranaPresmetkaOd"] != DBNull.Value)
                                                {
                                                    @Convert.ToDateTime(row["SelektiranaPresmetkaOd"]).ToString("dd.MM.yyyy")
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                            <td>
                                                @if (row["SelektiranaPresmetkaDo"] != DBNull.Value)
                                                {
                                                    @Convert.ToDateTime(row["SelektiranaPresmetkaDo"]).ToString("dd.MM.yyyy")
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                            <td>
                                                @if (row["DateCreated"] != DBNull.Value)
                                                {
                                                    @Convert.ToDateTime(row["DateCreated"]).ToString("dd.MM.yyyy HH:mm")
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <button type="button" class="btn btn-info btn-sm" onclick="toggleVrabotenSintetika('@batchId')" title="Вработен синтетика">
                                                        <i class="fas fa-users"></i> Вработен
                                                    </button>
                                                    <button type="button" class="btn btn-warning btn-sm" onclick="toggleSorabotnikSintetika('@batchId')" title="Соработник синтетика">
                                                        <i class="fas fa-user-tie"></i> Соработник
                                                    </button>
                                                    @if (row["SelektiranaPresmetkaOd"] != DBNull.Value && row["SelektiranaPresmetkaDo"] != DBNull.Value)
                                                    {
                                                        <a href="@Url.Page("PresmetkaProvizijaSintetiki", "ExportBatch", new { datumOd = Convert.ToDateTime(row["SelektiranaPresmetkaOd"]).ToString("yyyy-MM-dd"), datumDo = Convert.ToDateTime(row["SelektiranaPresmetkaDo"]).ToString("yyyy-MM-dd") })"
                                                           class="btn btn-success btn-sm"
                                                           title="Извези Excel">
                                                            <i class="fas fa-file-excel"></i> Excel
                                                        </a>
                                                    }
                                                </div>
                                            </td>
                                        </tr>
                                        <!-- Expandable row for Vraboten Sintetika -->
                                        <tr id="@batchId" class="collapse">
                                            <td colspan="6">
                                                <div class="card">
                                                    <div class="card-header">
                                                        <h6 class="mb-0">
                                                            <i class="fas fa-users"></i> Вработен синтетика за Batch @row["BatchId"]
                                                        </h6>
                                                    </div>
                                                    <div class="card-body">
                                                        <div id="vrabotenContainer_@batchId">
                                                            <div class="text-center">
                                                                <i class="fas fa-spinner fa-spin"></i> Се вчитуваат податоците...
                                                            </div>
                                                        </div>
                                                        <div class="text-right mt-3">
                                                            <button type="button" class="btn btn-secondary" onclick="toggleVrabotenSintetika('@batchId')">
                                                                <i class="fas fa-times"></i> Затвори
                                                            </button>
                                                            <button type="button" class="btn btn-primary" onclick="saveAllVraboten('@batchId')" style="display: none;" id="saveBtn_@batchId">
                                                                <i class="fas fa-check"></i> Зачувај сè
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="card mt-3" id="sorabotnikCard_@batchId" style="display: none;">
                                                    <div class="card-header">
                                                        <h6 class="mb-0">
                                                            <i class="fas fa-user-tie"></i> Соработник синтетика за Batch @row["BatchId"]
                                                        </h6>
                                                    </div>
                                                    <div class="card-body">
                                                        <div id="sorabotnikContainer_@batchId">
                                                            <div class="text-center">
                                                                <i class="fas fa-spinner fa-spin"></i> Се вчитуваат податоците...
                                                            </div>
                                                        </div>
                                                        <div class="text-right mt-3">
                                                            <button type="button" class="btn btn-secondary" onclick="toggleSorabotnikSintetika('@batchId')">
                                                                <i class="fas fa-times"></i> Затвори
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="alert alert-info" role="alert">
                            <i class="fas fa-info-circle"></i>
                            Нема пронајдени пресметки на провизија.
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleVrabotenSintetika(batchId) {
    console.log('Toggle vraboten sintetika for batch:', batchId);

    var row = document.getElementById(batchId);
    var sorabotnikCard = document.getElementById('sorabotnikCard_' + batchId);

    // Always close sorabotnik card when opening vraboten
    if (sorabotnikCard) {
        sorabotnikCard.style.display = 'none';
    }

    if (row.classList.contains('show')) {
        row.classList.remove('show');
    } else {
        // Close all other expanded rows first
        var allRows = document.querySelectorAll('tr.collapse');
        allRows.forEach(function(r) {
            r.classList.remove('show');
        });

        // Hide all sorabotnik cards
        document.querySelectorAll('[id^="sorabotnikCard_"]').forEach(function(card) {
            card.style.display = 'none';
        });

        // Open this row
        row.classList.add('show');

        // Make sure vraboten card is visible when row is expanded
        var vrabotenContainer = document.getElementById('vrabotenContainer_' + batchId);
        var vrabotenCard = vrabotenContainer ? vrabotenContainer.closest('.card') : null;
        if (vrabotenCard) {
            vrabotenCard.style.display = 'block';
        }

        // Load data for this batch
        loadVrabotenData(batchId);
    }
}

function loadVrabotenData(batchId) {

    // Get the batch data from the row
    var row = document.getElementById(batchId);
    var container = document.getElementById('vrabotenContainer_' + batchId);

    // Get the batch info from the main table row
    var mainRow = row.previousElementSibling;
    var cells = mainRow.querySelectorAll('td');
    var brojNaSpecifikacija = cells[1].textContent.trim();
    var datumOd = cells[2].textContent.trim();
    var datumDo = cells[3].textContent.trim();



    // Check for CSRF token
    var tokenElement = document.querySelector('input[name="__RequestVerificationToken"]');
    if (!tokenElement) {

        container.innerHTML = '<div class="alert alert-danger">CSRF token not found.</div>';
        return;
    }

    // For vraboteni, we need to check with the "2/*" pattern instead of "1/*"
    // Convert the BrojNaSpecifikacija to the vraboteni pattern
    var vrabotenBrojNaSpecifikacija = brojNaSpecifikacija;
    if (brojNaSpecifikacija.startsWith('1/')) {
        vrabotenBrojNaSpecifikacija = brojNaSpecifikacija.replace('1/', '2/');
    }



    // First, check if saved data exists for this BrojNaSpecifikacija

    var checkFormData = new FormData();
    checkFormData.append('brojNaSpecifikacija', vrabotenBrojNaSpecifikacija);
    checkFormData.append('__RequestVerificationToken', tokenElement.value);

    fetch('@Url.Page("PresmetkaProvizijaSintetiki", "LoadSavedVrabotenData")', {
        method: 'POST',
        body: checkFormData
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok: ' + response.status);
        }
        return response.json();
    })
    .then(data => {
        if (data.success && data.savedData && data.savedData.length > 0) {
            displaySavedVrabotenData(container, data.savedData);
        } else if (data.success) {
            loadEditableVrabotenData(container, datumOd, datumDo, tokenElement, batchId);
        } else {
            container.innerHTML = '<div class="alert alert-danger">Грешка при проверка на зачувани податоци: ' + (data.message || 'Непозната грешка') + '</div>';
        }
    })
    .catch(error => {
        container.innerHTML = '<div class="alert alert-danger">Грешка при проверка на зачувани податоци: ' + error.message + '</div>';
    });
}

function loadEditableVrabotenData(container, datumOd, datumDo, tokenElement, batchId) {
    console.log('=== loadEditableVrabotenData called ===');
    console.log('Parameters:', { datumOd, datumDo, tokenElement: !!tokenElement });

    try {
        // Convert dates to yyyy-MM-dd format
        datumOd = convertDateFormat(datumOd);
        datumDo = convertDateFormat(datumDo);

        console.log('Converted dates:', datumOd, datumDo);

    // Make AJAX call to load the data using FormData
    var formData = new FormData();
    formData.append('datumOd', datumOd);
    formData.append('datumDo', datumDo);
    formData.append('__RequestVerificationToken', tokenElement.value);

    fetch('@Url.Page("PresmetkaProvizijaSintetiki", "LoadVrabotenData")', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        console.log('Response status:', response.status);
        if (!response.ok) {
            throw new Error('Network response was not ok: ' + response.status);
        }
        return response.json();
    })
    .then(data => {
        console.log('Received data:', data);
        console.log('Container element:', container);

        if (data.success && data.vrabotenData && data.vrabotenData.length > 0) {
            console.log('Processing', data.vrabotenData.length, 'records');
            // Create table showing vraboteni records grouped by EMBGMBPrimacProvizija
            var html = '<div class="table-responsive"><table class="table table-sm table-bordered">';
            html += '<thead class="thead-light">';
            html += '<tr>';
            html += '<th>Број на спецификација</th>';
            html += '<th>Селектирана пресметка од</th>';
            html += '<th>Селектирана пресметка до</th>';
            html += '<th>Примач на провизија</th>';
            html += '<th>ЕМБГ/МБ примач провизија</th>';
            html += '<th>Датум на договор</th>';
            html += '<th>Износ провизија бруто</th>';
            html += '<th>Износ провизија нето</th>';
            html += '<th>Проидонеси</th>';
            html += '<th>Датум исплата</th>';
            html += '</tr>';
            html += '</thead><tbody>';

            data.vrabotenData.forEach(function(row, index) {
                html += '<tr>';
                html += '<td>' + (row.brojNaSpecifikacija || '') + '</td>';
                html += '<td>' + (row.selektiranaPresmetkaOd || '') + '</td>';
                html += '<td>' + (row.selektiranaPresmetkaDo || '') + '</td>';
                html += '<td>' + (row.primacNaProvizija || '') + '</td>';
                html += '<td>' + (row.embgMbPrimacProvizija || '') + '</td>';
                html += '<td>' + (row.datumNaDogovor || '-') + '</td>';
                html += '<td>' + (row.iznosProvizijaBruto || '0') + '</td>';
                html += '<td><input type="number" class="form-control form-control-sm" step="0.01" value="' + (row.iznosProvizijaNeto || '0') + '" data-embg="' + (row.embgMbPrimacProvizija || '') + '" data-field="neto" style="width: 120px;" /></td>';
                html += '<td><input type="number" class="form-control form-control-sm" step="0.01" value="0.00" data-embg="' + (row.embgMbPrimacProvizija || '') + '" data-field="proisonesi" style="width: 120px;" placeholder="0.00" /></td>';
                html += '<td><input type="text" class="form-control form-control-sm" value="" data-embg="' + (row.embgMbPrimacProvizija || '') + '" data-field="datumIsplata" style="width: 140px; background-color: white;" readonly placeholder="" /></td>';
                html += '</tr>';
            });

            html += '</tbody></table></div>';
            html += '<p class="mt-2"><strong>Вкупно записи:</strong> ' + data.vrabotenData.length + '</p>';

            // Add action buttons
            html += '<div class="mt-3 text-center">';
            html += '<button type="button" class="btn btn-success me-2" onclick="potvrdiVraboten(\'' + batchId + '\')">Потврди</button>';
            html += '<button type="button" class="btn btn-secondary" onclick="otkaziVraboten(\'' + batchId + '\')">Откажи</button>';
            html += '</div>';

            console.log('Setting container HTML, length:', html.length);
            container.innerHTML = html;
            console.log('Container HTML set successfully');
        } else {
            console.log('No data found or error:', data);
            container.innerHTML = '<div class="alert alert-info">Нема пронајдени записи за овој batch.</div>';
        }
    })
    .catch(error => {
        console.error('Error in loadEditableVrabotenData:', error);
        container.innerHTML = '<div class="alert alert-danger">Грешка при вчитување на податоците: ' + error.message + '</div>';
    });

    } catch (error) {
        console.error('Exception in loadEditableVrabotenData:', error);
        container.innerHTML = '<div class="alert alert-danger">Грешка при обработка: ' + error.message + '</div>';
    }
}

function convertDateFormat(dateStr) {
    // Convert from dd.MM.yyyy to yyyy-MM-dd
    if (dateStr && dateStr.includes('.')) {
        var parts = dateStr.split('.');
        if (parts.length === 3) {
            return parts[2] + '-' + parts[1].padStart(2, '0') + '-' + parts[0].padStart(2, '0');
        }
    }
    return dateStr;
}

function saveAllVraboten(batchId) {
    var container = document.getElementById('vrabotenContainer_' + batchId.replace('batch_', ''));
    var inputs = container.querySelectorAll('input[data-index]');

    // Group inputs by index
    var vrabotenRecords = {};
    inputs.forEach(function(input) {
        var index = input.getAttribute('data-index');
        var field = input.getAttribute('data-field');

        if (!vrabotenRecords[index]) {
            vrabotenRecords[index] = {};
        }
        vrabotenRecords[index][field] = input.value;
    });

    // Get batch info
    var mainRow = document.getElementById(batchId).previousElementSibling;
    var cells = mainRow.querySelectorAll('td');
    var brojNaSpecifikacija = cells[1].textContent.trim();
    var datumOd = convertDateFormat(cells[2].textContent.trim());
    var datumDo = convertDateFormat(cells[3].textContent.trim());

    // Save each record
    var savePromises = [];
    Object.keys(vrabotenRecords).forEach(function(index) {
        var record = vrabotenRecords[index];

        var saveData = {
            BrojNaSpecifikacija: brojNaSpecifikacija,
            SelektiranaPresmetkaOd: datumOd,
            SelektiranaPresmetkaDo: datumDo,
            ImePrezime: record.imePrezime || '',
            EMBG: record.embg || '',
            BrutoIznos: parseFloat(record.brutoIznos) || 0,
            Pridonesi: record.pridonesi ? parseFloat(record.pridonesi) : null,
            NetoIznos: record.netoIznos ? parseFloat(record.netoIznos) : null,
            DataNaDogovor: record.dataNaDogovor || '',
            DatumNaIsplata: record.datumNaIsplata || null
        };

        var formData = new FormData();
        Object.keys(saveData).forEach(key => {
            if (saveData[key] !== null && saveData[key] !== '') {
                formData.append(key, saveData[key]);
            }
        });

        savePromises.push(
            fetch('@Url.Page("PresmetkaProvizijaSintetiki", "SaveVrabotenSintetika")', {
                method: 'POST',
                body: formData
            })
        );
    });

    Promise.all(savePromises)
        .then(() => {
            alert('Сите записи се успешно зачувани!');
            location.reload();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Грешка при зачувување на записите.');
        });
}

// Function to confirm and save vraboten data
function potvrdiVraboten(batchId) {
    if (!confirm('Дали сте сигурни дека сакате да ги потврдите овие записи?')) {
        return;
    }

    console.log('potvrdiVraboten called with batchId:', batchId);

    // Collect all data from the table
    const tableData = [];
    const container = document.getElementById('vrabotenContainer_' + batchId);
    const rows = container.querySelectorAll('table tbody tr');

    console.log('Found rows:', rows.length);

    rows.forEach((row, index) => {
        const cells = row.querySelectorAll('td');
        const netoInput = row.querySelector('input[data-field="neto"]');
        const pridonesiInput = row.querySelector('input[data-field="proisonesi"]');
        const datumIsplataInput = row.querySelector('input[data-field="datumIsplata"]');

        console.log(`Row ${index}: cells=${cells.length}, neto=${netoInput?.value}, pridonesi=${pridonesiInput?.value}`);

        if (cells.length >= 7) {
            const rowData = {
                brojNaSpecifikacija: cells[0].textContent.trim(),
                selektiranaPresmetkaOd: cells[1].textContent.trim(),
                selektiranaPresmetkaDo: cells[2].textContent.trim(),
                imePrezime: cells[3].textContent.trim(),
                embg: cells[4].textContent.trim(),
                datumNaDogovor: cells[5].textContent.trim(),
                brutoIznos: cells[6].textContent.trim(),
                netoIznos: netoInput ? netoInput.value : '0',
                pridonesi: pridonesiInput ? pridonesiInput.value : '0',
                datumNaIsplata: datumIsplataInput ? datumIsplataInput.value : ''
            };
            console.log(`Row ${index} data:`, rowData);
            tableData.push(rowData);
        }
    });

    console.log('Total tableData:', tableData);

    // Send data to server using FormData (same pattern as LoadVrabotenData)
    console.log('Sending data to server:', JSON.stringify(tableData));

    var tokenElement = document.querySelector('input[name="__RequestVerificationToken"]');
    var formData = new FormData();
    formData.append('tableDataJson', JSON.stringify(tableData));
    formData.append('__RequestVerificationToken', tokenElement.value);

    fetch('@Url.Page("PresmetkaProvizijaSintetiki", "SaveVrabotenData")', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Server response:', data);
        if (data.success) {
            alert('Записите се успешно потврдени и зачувани!');
            console.log('Save successful, reloading data for batchId:', batchId);

            // Get the BrojNaSpecifikacija from the saved data (first record)
            var savedBrojNaSpecifikacija = tableData.length > 0 ? tableData[0].brojNaSpecifikacija : null;
            console.log('Using BrojNaSpecifikacija from saved data:', savedBrojNaSpecifikacija);

            // Add a small delay to ensure database transaction is committed
            setTimeout(function() {
                console.log('=== TIMEOUT CALLBACK EXECUTING ===');
                console.log('Reloading vraboten data after save...');
                console.log('batchId:', batchId);
                console.log('savedBrojNaSpecifikacija:', savedBrojNaSpecifikacija);
                loadVrabotenDataWithBrojNaSpecifikacija(batchId, savedBrojNaSpecifikacija);
            }, 500);
        } else {
            alert('Грешка при зачувување: ' + (data.message || 'Непозната грешка'));
        }
    })
    .catch(error => {
        console.error('Fetch error:', error);
        alert('Грешка при зачувување на записите: ' + error.message);
    });
}

// Function to cancel vraboten operation
function otkaziVraboten(batchId) {
    if (confirm('Дали сте сигурни дека сакате да ги откажете промените?')) {
        const container = document.getElementById('vrabotenContainer_' + batchId);
        container.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Се вчитуваат податоците...</div>';
    }
}

// Function to load vraboten data with specific BrojNaSpecifikacija (used after save)
function loadVrabotenDataWithBrojNaSpecifikacija(batchId, brojNaSpecifikacija) {
    console.log('Loading vraboten data with specific BrojNaSpecifikacija:', brojNaSpecifikacija);

    var container = document.getElementById('vrabotenContainer_' + batchId);
    var tokenElement = document.querySelector('input[name="__RequestVerificationToken"]');

    if (!tokenElement) {
        console.error('CSRF token not found');
        container.innerHTML = '<div class="alert alert-danger">CSRF token not found.</div>';
        return;
    }

    // Check for saved data with the specific BrojNaSpecifikacija
    var checkFormData = new FormData();
    checkFormData.append('brojNaSpecifikacija', brojNaSpecifikacija);
    checkFormData.append('__RequestVerificationToken', tokenElement.value);

    fetch('@Url.Page("PresmetkaProvizijaSintetiki", "LoadSavedVrabotenData")', {
        method: 'POST',
        body: checkFormData
    })
    .then(response => {
        console.log('LoadSavedVrabotenData response status:', response.status);
        if (!response.ok) {
            throw new Error('Network response was not ok: ' + response.status);
        }
        return response.json();
    })
    .then(data => {
        console.log('LoadSavedVrabotenData response after save:', data);
        console.log('data.success:', data.success);
        console.log('data.savedData:', data.savedData);
        console.log('data.savedData.length:', data.savedData ? data.savedData.length : 'undefined');

        if (data.success && data.savedData && data.savedData.length > 0) {
            console.log('Found saved data, displaying read-only version');
            console.log('Calling displaySavedVrabotenData with container:', container);
            displaySavedVrabotenData(container, data.savedData);
        } else {
            console.log('No saved data found even after save - this should not happen');
            console.log('data object:', JSON.stringify(data));
            container.innerHTML = '<div class="alert alert-warning">Податоците се зачувани, но не можат да се вчитаат. Освежете ја страницата.</div>';
        }
    })
    .catch(error => {
        console.error('Error loading saved data:', error);
        container.innerHTML = '<div class="alert alert-danger">Грешка при вчитување на зачуваните податоци: ' + error.message + '</div>';
    });
}

// Function to display saved vraboten data as read-only
function displaySavedVrabotenData(container, savedData) {
    console.log('=== displaySavedVrabotenData called ===');
    console.log('Container:', container);
    console.log('SavedData length:', savedData ? savedData.length : 'undefined');
    console.log('SavedData:', savedData);

    // Create table showing saved records (read-only)
    var html = '<div class="alert alert-success"><i class="fas fa-check-circle"></i> <strong>Потврдени записи</strong> - Овие податоци се веќе зачувани и потврдени.</div>';
    html += '<div class="table-responsive"><table class="table table-sm table-bordered">';
    html += '<thead class="thead-light">';
    html += '<tr>';
    html += '<th>Број на спецификација</th>';
    html += '<th>Селектирана пресметка од</th>';
    html += '<th>Селектирана пресметка до</th>';
    html += '<th>Примач на провизија</th>';
    html += '<th>ЕМБГ/МБ примач провизија</th>';
    html += '<th>Датум на договор</th>';
    html += '<th>Износ провизија бруто</th>';
    html += '<th>Износ провизија нето</th>';
    html += '<th>Проидонеси</th>';
    html += '<th>Датум исплата</th>';
    html += '<th>Датум на зачувување</th>';
    html += '</tr>';
    html += '</thead><tbody>';

    savedData.forEach(function(row, index) {
        html += '<tr>';
        html += '<td>' + (row.brojNaSpecifikacija || '') + '</td>';
        html += '<td>' + (row.selektiranaPresmetkaOd || '') + '</td>';
        html += '<td>' + (row.selektiranaPresmetkaDo || '') + '</td>';
        html += '<td>' + (row.imePrezime || '') + '</td>';
        html += '<td>' + (row.embg || '') + '</td>';
        html += '<td>' + (row.datumNaDogovor || '') + '</td>';
        html += '<td>' + (row.brutoIznos || '0') + '</td>';
        html += '<td>' + (row.netoIznos || '0') + '</td>';
        html += '<td>' + (row.pridonesi || '0') + '</td>';
        html += '<td>' + (row.datumNaIsplata || '-') + '</td>';
        html += '<td>' + (row.dateCreated || '-') + '</td>';
        html += '</tr>';
    });

    html += '</tbody></table></div>';
    html += '<p class="mt-2"><strong>Вкупно записи:</strong> ' + savedData.length + '</p>';

    // Add Excel export button
    var brojNaSpecifikacija = savedData.length > 0 ? savedData[0].brojNaSpecifikacija : '';
    html += '<div class="mt-3 text-center">';
    html += '<button type="button" class="btn btn-success" onclick="exportVrabotenExcel(\'' + brojNaSpecifikacija + '\')">';
    html += '<i class="fas fa-file-excel"></i> Експорт во Excel';
    html += '</button>';
    html += '</div>';

    console.log('Setting container HTML for saved data, length:', html.length);
    container.innerHTML = html;
    console.log('Container HTML set successfully for saved data');
}

// Соработник functions
function toggleSorabotnikSintetika(batchId) {
    console.log('Toggle sorabotnik sintetika for batch:', batchId);

    var row = document.getElementById(batchId);
    var sorabotnikCard = document.getElementById('sorabotnikCard_' + batchId);

    // Check if sorabotnik card is currently visible
    var isSorabotnikVisible = sorabotnikCard && sorabotnikCard.style.display === 'block';

    if (isSorabotnikVisible) {
        // Close sorabotnik section and collapse row if no vraboten is showing
        console.log('Closing sorabotnik section');
        sorabotnikCard.style.display = 'none';

        // Check if vraboten section is also hidden, if so collapse the row
        var vrabotenContainer = document.getElementById('vrabotenContainer_' + batchId);
        var vrabotenCard = vrabotenContainer ? vrabotenContainer.closest('.card') : null;
        var isVrabotenVisible = vrabotenCard && vrabotenCard.style.display !== 'none';

        if (!isVrabotenVisible) {
            row.classList.remove('show');
        }
    } else {
        // Close all other rows first
        document.querySelectorAll('.collapse.show').forEach(function(openRow) {
            if (openRow !== row) {
                openRow.classList.remove('show');
            }
        });

        // Hide all other sorabotnik cards
        document.querySelectorAll('[id^="sorabotnikCard_"]').forEach(function(card) {
            card.style.display = 'none';
        });

        // Expand the row and show sorabotnik section
        console.log('Opening sorabotnik section');
        row.classList.add('show');

        // Hide the vraboten section when showing sorabotnik
        var vrabotenContainer = document.getElementById('vrabotenContainer_' + batchId);
        var vrabotenCard = vrabotenContainer ? vrabotenContainer.closest('.card') : null;
        if (vrabotenCard) {
            vrabotenCard.style.display = 'none';
        }

        sorabotnikCard.style.display = 'block';

        // Load data for this batch
        loadSorabotnikData(batchId);
    }
}

function loadSorabotnikData(batchId) {
    // Get the batch data from the row
    var row = document.getElementById(batchId);
    var container = document.getElementById('sorabotnikContainer_' + batchId);

    // Get the batch info from the main table row
    var mainRow = row.previousElementSibling;
    var cells = mainRow.querySelectorAll('td');
    var brojNaSpecifikacija = cells[1].textContent.trim();
    var datumOd = cells[2].textContent.trim();
    var datumDo = cells[3].textContent.trim();

    // Check for CSRF token
    var tokenElement = document.querySelector('input[name="__RequestVerificationToken"]');
    if (!tokenElement) {
        container.innerHTML = '<div class="alert alert-danger">CSRF token not found.</div>';
        return;
    }

    // First, check if saved data exists for this BrojNaSpecifikacija

    var checkFormData = new FormData();
    checkFormData.append('brojNaSpecifikacija', brojNaSpecifikacija);
    checkFormData.append('__RequestVerificationToken', tokenElement.value);

    fetch('@Url.Page("PresmetkaProvizijaSintetiki", "LoadSavedSorabotnikData")', {
        method: 'POST',
        body: checkFormData
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok: ' + response.status);
        }
        return response.json();
    })
    .then(data => {
        if (data.success && data.savedData && data.savedData.length > 0) {
            displaySavedSorabotnikData(container, data.savedData);
        } else if (data.success) {
            loadEditableSorabotnikData(container, datumOd, datumDo, tokenElement, batchId);
        } else {
            container.innerHTML = '<div class="alert alert-danger">Грешка при проверка на зачувани податоци за соработници: ' + (data.message || 'Непозната грешка') + '</div>';
        }
    })
    .catch(error => {
        container.innerHTML = '<div class="alert alert-danger">Грешка при проверка на зачувани податоци за соработници: ' + error.message + '</div>';
    });
}

function loadEditableSorabotnikData(container, datumOd, datumDo, tokenElement, batchId) {

    try {
        // For Соработник, we want records that start with "1" instead of "2"
        // Convert dates to yyyy-MM-dd format
        datumOd = convertDateFormat(datumOd);
        datumDo = convertDateFormat(datumDo);

        console.log('Converted dates:', datumOd, datumDo);

        // Make AJAX call to load the data using FormData (same as vraboten but filter for "1/*")
        var formData = new FormData();
        formData.append('datumOd', datumOd);
        formData.append('datumDo', datumDo);
        formData.append('__RequestVerificationToken', tokenElement.value);

        fetch('@Url.Page("PresmetkaProvizijaSintetiki", "LoadSorabotnikData")', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            console.log('Sorabotnik Response status:', response.status);
            if (!response.ok) {
                throw new Error('Network response was not ok: ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            console.log('Received sorabotnik data:', data);
            console.log('Container element:', container);

            if (data.success && data.sorabotnikData && data.sorabotnikData.length > 0) {
                console.log('Processing', data.sorabotnikData.length, 'sorabotnik records');
                displaySorabotnikData(container, data.sorabotnikData, batchId);
            } else {
                console.log('No sorabotnik data found or error:', data);
                container.innerHTML = '<div class="alert alert-info">Нема пронајдени записи за соработници во овој batch.</div>';
            }
        })
        .catch(error => {
            console.error('Error in loadEditableSorabotnikData:', error);
            container.innerHTML = '<div class="alert alert-danger">Грешка при вчитување на податоците за соработници: ' + error.message + '</div>';
        });

    } catch (error) {
        console.error('Exception in loadEditableSorabotnikData:', error);
        container.innerHTML = '<div class="alert alert-danger">Грешка при обработка на соработници: ' + error.message + '</div>';
    }
}

function displaySorabotnikData(container, sorabotnikData, batchId) {
    console.log('Displaying sorabotnik data:', sorabotnikData);

    // Create table showing sorabotnik records (different structure from vraboten)
    var html = '<div class="table-responsive"><table class="table table-sm table-bordered">';
    html += '<thead class="thead-light">';
    html += '<tr>';
    html += '<th>Број на спецификација</th>';
    html += '<th>Селектирана пресметка од</th>';
    html += '<th>Селектирана пресметка до</th>';
    html += '<th>Примач на провизија</th>';
    html += '<th>ЕМБГ/МБ примач провизија</th>';
    html += '<th>Тип примач провизија</th>';
    html += '<th>Датум на договор</th>';
    html += '<th>Сметка</th>';
    html += '<th>Износ провизија бруто</th>';
    html += '<th>Износ провизија нето</th>';
    html += '<th>Данок</th>';
    html += '<th>Датум исплата</th>';
    html += '</tr>';
    html += '</thead><tbody>';

    sorabotnikData.forEach(function(row, index) {
        html += '<tr>';
        html += '<td>' + (row.brojNaSpecifikacija || '') + '</td>';
        html += '<td>' + (row.selektiranaPresmetkaOd || '') + '</td>';
        html += '<td>' + (row.selektiranaPresmetkaDo || '') + '</td>';
        html += '<td>' + (row.primacNaProvizija || '') + '</td>';
        html += '<td>' + (row.embgMbPrimacProvizija || '') + '</td>';
        html += '<td>' + (row.tipPrimacProvizija || 'Непознато') + '</td>';
        html += '<td>' + (row.datumNaDogovor || '-') + '</td>';
        html += '<td>' + (row.plateznaSmetka || '-') + '</td>';
        html += '<td>' + (row.iznosProvizijaBruto || '0') + '</td>';
        html += '<td><input type="number" class="form-control form-control-sm" step="0.01" value="' + (row.iznosProvizijaNeto || '0') + '" data-embg="' + (row.embgMbPrimacProvizija || '') + '" data-field="neto" style="width: 120px;" /></td>';
        html += '<td><input type="number" class="form-control form-control-sm" step="0.01" value="' + (row.procentDanok || '0') + '" data-embg="' + (row.embgMbPrimacProvizija || '') + '" data-field="danok" style="width: 120px;" /></td>';
        html += '<td><input type="text" class="form-control form-control-sm" value="" data-embg="' + (row.embgMbPrimacProvizija || '') + '" data-field="datumIsplata" style="width: 140px; background-color: white;" readonly placeholder="" /></td>';
        html += '</tr>';
    });

    html += '</tbody></table></div>';
    html += '<p class="mt-2"><strong>Вкупно записи:</strong> ' + sorabotnikData.length + '</p>';

    // Add action buttons (same as vraboten)
    html += '<div class="mt-3 text-center">';
    html += '<button type="button" class="btn btn-success me-2" onclick="potvrdiSorabotnik(\'' + batchId + '\')">Потврди</button>';
    html += '<button type="button" class="btn btn-secondary" onclick="otkaziSorabotnik(\'' + batchId + '\')">Откажи</button>';
    html += '</div>';

    console.log('Setting sorabotnik container HTML, length:', html.length);
    container.innerHTML = html;
    console.log('Sorabotnik container HTML set successfully');
}

// Function to save sorabotnik data
function potvrdiSorabotnik(batchId) {
    console.log('=== potvrdiSorabotnik called ===');
    console.log('BatchId:', batchId);

    var container = document.getElementById('sorabotnikContainer_' + batchId);
    if (!container) {
        console.error('Container not found for batchId:', batchId);
        alert('Грешка: не можам да го најдам контејнерот.');
        return;
    }

    // Collect data from the table
    var tableData = [];
    var rows = container.querySelectorAll('tbody tr');

    console.log('Found', rows.length, 'rows to process');

    rows.forEach(function(row, index) {
        var cells = row.querySelectorAll('td');
        if (cells.length >= 12) {
            var netoInput = cells[9].querySelector('input[data-field="neto"]');
            var danokInput = cells[10].querySelector('input[data-field="danok"]');
            var datumIsplataInput = cells[11].querySelector('input[data-field="datumIsplata"]');

            var rowData = {
                brojNaSpecifikacija: cells[0].textContent.trim(),
                selektiranaPresmetkaOd: cells[1].textContent.trim(),
                selektiranaPresmetkaDo: cells[2].textContent.trim(),
                imePrezime: cells[3].textContent.trim(),
                embg: cells[4].textContent.trim(),
                tipPrimacProvizija: cells[5].textContent.trim(),
                datumNaDogovor: cells[6].textContent.trim(),
                smetka: cells[7].textContent.trim(),
                brutoIznos: cells[8].textContent.trim(),
                netoIznos: netoInput ? netoInput.value : '0',
                danok: danokInput ? danokInput.value : '0',
                datumNaIsplata: datumIsplataInput ? datumIsplataInput.value : ''
            };

            console.log('Row', index + 1, 'data:', rowData);
            tableData.push(rowData);
        }
    });

    if (tableData.length === 0) {
        alert('Нема податоци за зачувување.');
        return;
    }

    console.log('Collected', tableData.length, 'records for saving');

    // Get CSRF token
    var tokenElement = document.querySelector('input[name="__RequestVerificationToken"]');
    if (!tokenElement) {
        console.error('CSRF token not found');
        alert('CSRF token не е пронајден.');
        return;
    }

    // Prepare form data
    var formData = new FormData();
    formData.append('tableDataJson', JSON.stringify(tableData));
    formData.append('__RequestVerificationToken', tokenElement.value);

    fetch('@Url.Page("PresmetkaProvizijaSintetiki", "SaveSorabotnikData")', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        console.log('Response status:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Server response:', data);
        if (data.success) {
            alert('Записите за соработници се успешно потврдени и зачувани!');
            console.log('Save successful, reloading data for batchId:', batchId);

            // Get the BrojNaSpecifikacija from the saved data (first record)
            var savedBrojNaSpecifikacija = tableData.length > 0 ? tableData[0].brojNaSpecifikacija : null;
            console.log('Using BrojNaSpecifikacija from saved data:', savedBrojNaSpecifikacija);

            // Add a small delay to ensure database transaction is committed
            setTimeout(function() {
                console.log('=== TIMEOUT CALLBACK EXECUTING FOR SORABOTNIK ===');
                console.log('Reloading sorabotnik data after save...');
                console.log('batchId:', batchId);
                console.log('savedBrojNaSpecifikacija:', savedBrojNaSpecifikacija);
                loadSorabotnikDataWithBrojNaSpecifikacija(batchId, savedBrojNaSpecifikacija);
            }, 500);
        } else {
            alert('Грешка при зачувување: ' + (data.message || 'Непозната грешка'));
        }
    })
    .catch(error => {
        console.error('Fetch error:', error);
        alert('Грешка при зачувување на записите за соработници: ' + error.message);
    });
}

function otkaziSorabotnik(batchId) {
    if (confirm('Дали сте сигурни дека сакате да ги откажете промените?')) {
        const container = document.getElementById('sorabotnikContainer_' + batchId);
        container.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Се вчитуваат податоците...</div>';
    }
}

// Function to load sorabotnik data with specific BrojNaSpecifikacija (used after save)
function loadSorabotnikDataWithBrojNaSpecifikacija(batchId, brojNaSpecifikacija) {
    console.log('Loading sorabotnik data with specific BrojNaSpecifikacija:', brojNaSpecifikacija);

    var container = document.getElementById('sorabotnikContainer_' + batchId);
    var tokenElement = document.querySelector('input[name="__RequestVerificationToken"]');

    if (!tokenElement) {
        console.error('CSRF token not found');
        container.innerHTML = '<div class="alert alert-danger">CSRF token not found.</div>';
        return;
    }

    // Check for saved data with the specific BrojNaSpecifikacija
    var checkFormData = new FormData();
    checkFormData.append('brojNaSpecifikacija', brojNaSpecifikacija);
    checkFormData.append('__RequestVerificationToken', tokenElement.value);

    fetch('@Url.Page("PresmetkaProvizijaSintetiki", "LoadSavedSorabotnikData")', {
        method: 'POST',
        body: checkFormData
    })
    .then(response => {
        console.log('LoadSavedSorabotnikData response status:', response.status);
        if (!response.ok) {
            throw new Error('Network response was not ok: ' + response.status);
        }
        return response.json();
    })
    .then(data => {
        console.log('LoadSavedSorabotnikData response after save:', data);
        console.log('data.success:', data.success);
        console.log('data.savedData:', data.savedData);
        console.log('data.savedData.length:', data.savedData ? data.savedData.length : 'undefined');

        if (data.success && data.savedData && data.savedData.length > 0) {
            console.log('Found saved sorabotnik data, displaying read-only version');
            console.log('Calling displaySavedSorabotnikData with container:', container);
            displaySavedSorabotnikData(container, data.savedData);
        } else {
            console.log('No saved sorabotnik data found even after save - this should not happen');
            console.log('data object:', JSON.stringify(data));
            container.innerHTML = '<div class="alert alert-warning">Податоците за соработници се зачувани, но не можат да се вчитаат. Освежете ја страницата.</div>';
        }
    })
    .catch(error => {
        console.error('Error loading saved sorabotnik data:', error);
        container.innerHTML = '<div class="alert alert-danger">Грешка при вчитување на зачуваните податоци за соработници: ' + error.message + '</div>';
    });
}

// Function to display saved sorabotnik data as read-only
function displaySavedSorabotnikData(container, savedData) {
    console.log('=== displaySavedSorabotnikData called ===');
    console.log('Container:', container);
    console.log('SavedData length:', savedData ? savedData.length : 'undefined');
    console.log('SavedData:', savedData);

    // Create table showing saved records (read-only)
    var html = '<div class="alert alert-success"><i class="fas fa-check-circle"></i> <strong>Потврдени записи за соработници</strong> - Овие податоци се веќе зачувани и потврдени.</div>';
    html += '<div class="table-responsive"><table class="table table-sm table-bordered">';
    html += '<thead class="thead-light">';
    html += '<tr>';
    html += '<th>Број на спецификација</th>';
    html += '<th>Селектирана пресметка од</th>';
    html += '<th>Селектирана пресметка до</th>';
    html += '<th>Примач на провизија</th>';
    html += '<th>ЕМБГ/МБ примач провизија</th>';
    html += '<th>Тип примач провизија</th>';
    html += '<th>Датум на договор</th>';
    html += '<th>Сметка</th>';
    html += '<th>Износ провизија бруто</th>';
    html += '<th>Износ провизија нето</th>';
    html += '<th>Данок</th>';
    html += '<th>Датум исплата</th>';
    html += '<th>Датум на зачувување</th>';
    html += '</tr>';
    html += '</thead><tbody>';

    savedData.forEach(function(row, index) {
        html += '<tr>';
        html += '<td>' + (row.brojNaSpecifikacija || '') + '</td>';
        html += '<td>' + (row.selektiranaPresmetkaOd || '') + '</td>';
        html += '<td>' + (row.selektiranaPresmetkaDo || '') + '</td>';
        html += '<td>' + (row.imePrezime || '') + '</td>';
        html += '<td>' + (row.embg || '') + '</td>';
        html += '<td>' + (row.tipPrimacProvizija || '') + '</td>';
        html += '<td>' + (row.datumNaDogovor || '') + '</td>';
        html += '<td>' + (row.smetka || '-') + '</td>';
        html += '<td>' + (row.brutoIznos || '0') + '</td>';
        html += '<td>' + (row.netoIznos || '0') + '</td>';
        html += '<td>' + (row.danok || '0') + '</td>';
        html += '<td>' + (row.datumNaIsplata || '-') + '</td>';
        html += '<td>' + (row.dateCreated || '-') + '</td>';
        html += '</tr>';
    });

    html += '</tbody></table></div>';
    html += '<p class="mt-2"><strong>Вкупно записи:</strong> ' + savedData.length + '</p>';

    // Add Excel export button
    var brojNaSpecifikacija = savedData.length > 0 ? savedData[0].brojNaSpecifikacija : '';
    html += '<div class="mt-3 text-center">';
    html += '<button type="button" class="btn btn-success" onclick="exportSorabotnikExcel(\'' + brojNaSpecifikacija + '\')">';
    html += '<i class="fas fa-file-excel"></i> Експорт во Excel';
    html += '</button>';
    html += '</div>';

    console.log('Setting container HTML for saved sorabotnik data, length:', html.length);
    container.innerHTML = html;
    console.log('Container HTML set successfully for saved sorabotnik data');
}

// Excel export functions
function exportVrabotenExcel(brojNaSpecifikacija) {
    console.log('Exporting Vraboten Excel for:', brojNaSpecifikacija);
    var url = '/Provizija/PresmetkaProvizijaSintetiki?handler=ExportVrabotenExcel&brojNaSpecifikacija=' + encodeURIComponent(brojNaSpecifikacija);
    window.open(url, '_blank');
}

function exportSorabotnikExcel(brojNaSpecifikacija) {
    console.log('Exporting Sorabotnik Excel for:', brojNaSpecifikacija);
    var url = '/Provizija/PresmetkaProvizijaSintetiki?handler=ExportSorabotnikExcel&brojNaSpecifikacija=' + encodeURIComponent(brojNaSpecifikacija);
    window.open(url, '_blank');
}
</script>