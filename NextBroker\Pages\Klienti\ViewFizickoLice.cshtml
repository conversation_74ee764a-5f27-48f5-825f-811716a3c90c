@page "{id:long}"
@model NextBroker.Pages.Klienti.ViewFizickoLiceModel
@{
    ViewData["Title"] = "Преглед на физичко лице";
}

<style>
    .data-field {
        background-color: #f8f9fa;
        padding: 8px 12px;
        border-radius: 6px;
        border: 1px solid #e9ecef;
        min-height: 38px;
        display: flex;
        align-items: center;
    }

    .data-field:hover {
        background-color: #e9ecef;
    }

    .card {
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border: none;
        margin-bottom: 1.5rem;
    }

    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
        padding: 1rem 1.25rem;
    }

    .card-header h5 {
        margin: 0;
        color: #495057;
        font-weight: 600;
    }

    .form-label {
        color: #6c757d;
        font-weight: 500;
        margin-bottom: 0.5rem;
    }

    .boolean-value {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .boolean-value i {
        font-size: 1rem;
    }

    .true-value {
        color: #198754;
    }

    .false-value {
        color: #dc3545;
    }
</style>

<div class="container-fluid mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>@ViewData["Title"]</h1>
        <div>
             <!--  <a href="/Klienti/EditFizickoLice/@Model.Input.Id" class="btn btn-primary me-2">
              <i class="fas fa-edit"></i> Измени 
            </a>   -->
            <a href="/Klienti/ListaKlienti" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Назад
            </a>
        </div>
    </div>

    <!-- Basic Information Card -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">Основни податоци</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label class="form-label">Име</label>
                    <div class="data-field">@Model.Input?.Ime</div>
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Презиме</label>
                    <div class="data-field">@Model.Input?.Prezime</div>
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">ЕМБГ</label>
                    <div class="data-field">@Model.Input?.EMBG</div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label class="form-label">Пол</label>
                    <div class="data-field">@Model.Input?.KlientPol</div>
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Вработен</label>
                    <div class="data-field">
                        <span class="boolean-value @(Model.Input?.KlientVraboten == true ? "true-value" : "false-value")">
                            <i class="fas @(Model.Input?.KlientVraboten == true ? "fa-check-circle" : "fa-times-circle")"></i>
                            @(Model.Input?.KlientVraboten == true ? "Да" : "Не")
                        </span>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Соработник</label>
                    <div class="data-field">
                        <span class="boolean-value @(Model.Input?.KlientSorabotnik == true ? "true-value" : "false-value")">
                            <i class="fas @(Model.Input?.KlientSorabotnik == true ? "fa-check-circle" : "fa-times-circle")"></i>
                            @(Model.Input?.KlientSorabotnik == true ? "Да" : "Не")
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Document Information Card -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">Документ за идентификација</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label class="form-label">Број на пасош/лична карта</label>
                    <div class="data-field">@Model.Input?.BrojPasosLicnaKarta</div>
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Важи од</label>
                    <div class="data-field">@(Model.Input?.DatumVaziOdPasosLicnaKarta?.ToString("dd.MM.yyyy"))</div>
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Важи до</label>
                    <div class="data-field">@(Model.Input?.DatumVaziDoPasosLicnaKarta?.ToString("dd.MM.yyyy"))</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Address Information Card -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">Адресни податоци</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label class="form-label">Општина од документ за идентификација</label>
                    <div class="data-field">
                        @(Model.Opstini.FirstOrDefault(o => o.Value == Model.Input?.ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija?.ToString())?.Text)
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Улица од документ за идентификација</label>
                    <div class="data-field">@Model.Input?.UlicaOdDokumentZaIdentifikacija</div>
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Број од документ за идентификација</label>
                    <div class="data-field">@Model.Input?.BrojOdDokumentZaIdentifikacija</div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label class="form-label">Општина за комуникација</label>
                    <div class="data-field">
                        @(Model.Opstini.FirstOrDefault(o => o.Value == Model.Input?.ListaOpstiniIdOpstinaZaKomunikacija?.ToString())?.Text)
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Улица за комуникација</label>
                    <div class="data-field">@Model.Input?.UlicaZaKomunikacija</div>
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Број за комуникација</label>
                    <div class="data-field">@Model.Input?.BrojZaKomunikacija</div>
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Забелешка</label>
                    <div class="data-field">@Model.Input?.Zabeleska</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Contact Information Card -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">Контакт информации</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label class="form-label">Email</label>
                    <div class="data-field">@Model.Input?.Email</div>
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Телефон</label>
                    <div class="data-field">@Model.Input?.Tel</div>
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Веб страна</label>
                    <div class="data-field">@Model.Input?.Webstrana</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Consent Information Card -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">Согласности</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label class="form-label">Согласност за директен маркетинг</label>
                    <div class="data-field">
                        <span class="boolean-value @(Model.Input?.SoglasnostZaDirektenMarketing == true ? "true-value" : "false-value")">
                            <i class="fas @(Model.Input?.SoglasnostZaDirektenMarketing == true ? "fa-check-circle" : "fa-times-circle")"></i>
                            @(Model.Input?.SoglasnostZaDirektenMarketing == true ? "Да" : "Не")
                        </span>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Согласност за email комуникација</label>
                    <div class="data-field">
                        <span class="boolean-value @(Model.Input?.SoglasnostZaEmailKomunikacija == true ? "true-value" : "false-value")">
                            <i class="fas @(Model.Input?.SoglasnostZaEmailKomunikacija == true ? "fa-check-circle" : "fa-times-circle")"></i>
                            @(Model.Input?.SoglasnostZaEmailKomunikacija == true ? "Да" : "Не")
                        </span>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Согласност за телефонска комуникација</label>
                    <div class="data-field">
                        <span class="boolean-value @(Model.Input?.SoglasnostZaTelKomunikacija == true ? "true-value" : "false-value")">
                            <i class="fas @(Model.Input?.SoglasnostZaTelKomunikacija == true ? "fa-check-circle" : "fa-times-circle")"></i>
                            @(Model.Input?.SoglasnostZaTelKomunikacija == true ? "Да" : "Не")
                        </span>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label class="form-label">Датум на повлечена согласност за директен маркетинг</label>
                    <div class="data-field">@(Model.Input?.DatumNaPovlecenaSoglasnostZaDirektenMarketing?.ToString("dd.MM.yyyy"))</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Risk Level Card -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">Ниво на ризик</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label class="form-label">Носител на јавна функција</label>
                    <div class="data-field">
                        <span class="boolean-value @(Model.Input?.NositelNaJF == true ? "true-value" : "false-value")">
                            <i class="fas @(Model.Input?.NositelNaJF == true ? "fa-check-circle" : "fa-times-circle")"></i>
                            @(Model.Input?.NositelNaJF == true ? "Да" : "Не")
                        </span>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Основ за носител на јавна функција</label>
                    <div class="data-field">@Model.Input?.OsnovZaNositelNaJF</div>
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Засилена анализа</label>
                    <div class="data-field">
                        <span class="boolean-value @(Model.Input?.ZasilenaAnaliza == true ? "true-value" : "false-value")">
                            <i class="fas @(Model.Input?.ZasilenaAnaliza == true ? "fa-check-circle" : "fa-times-circle")"></i>
                            @(Model.Input?.ZasilenaAnaliza == true ? "Да" : "Не")
                        </span>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label class="form-label">Ниво на ризик</label>
                    <div class="data-field">
                        @(Model.NivoaNaRizik.FirstOrDefault(n => n.Value == Model.Input?.NivoaNaRizikIdNivoNaRizik?.ToString())?.Text)
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Contract Information Card -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">Договор</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label class="form-label">Датум на овластување</label>
                    <div class="data-field">@(Model.Input?.DatumNaOvlastuvanje?.ToString("dd.MM.yyyy"))</div>
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Датум на договор</label>
                    <div class="data-field">@(Model.Input?.DaumNaDogovor?.ToString("dd.MM.yyyy"))</div>
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Договор важи до</label>
                    <div class="data-field">@(Model.Input?.DogovorVaziDo?.ToString("dd.MM.yyyy"))</div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label class="form-label">Број на договор</label>
                    <div class="data-field">@Model.Input?.BrojNaDogovor</div>
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Договор определено/неопределено</label>
                    <div class="data-field">@Model.Input?.DogovorOpredelenoNeopredeleno</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bank Information Card -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">Банкарски податоци</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label class="form-label">Платежна сметка</label>
                    <div class="data-field">@Model.Input?.PlateznaSmetka</div>
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Банка</label>
                    <div class="data-field">
                        @(Model.Banki.FirstOrDefault(b => b.Value == Model.Input?.SifrarnikBankiIdBanka?.ToString())?.Text)
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Рок на плаќање за фактури кон клиентот (денови)</label>
                    <div class="data-field">@Model.Input?.RokNaPlakanjeDenovi</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Position Information Card -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">Работна позиција</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label class="form-label">Работна позиција</label>
                    <div class="data-field">
                        @(Model.RabotniPozicii.FirstOrDefault(r => r.Value == Model.Input?.SifrarnikRabotniPoziciiIdRabotnaPozicija?.ToString())?.Text)
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Организациона единица</label>
                    <div class="data-field">
                        @(Model.OrganizacioniEdinici.FirstOrDefault(o => o.Value == Model.Input?.SifrarnikOrganizacioniEdiniciIdOrganizacionaEdinica?.ToString())?.Text)
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Надреден</label>
                    <div class="data-field">@Model.Input?.NadredenImePrezime</div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label class="form-label">Надреден важи од</label>
                    <div class="data-field">@(Model.Input?.NadredenOd?.ToString("dd.MM.yyyy"))</div>
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Надреден важи до</label>
                    <div class="data-field">@(Model.Input?.NadredenDo?.ToString("dd.MM.yyyy"))</div>
                </div>         
                <div class="col-md-4 mb-3">
                    <label class="form-label">Експозитура</label>
                    <div class="data-field">
                        @(Model.EkspozituriIdEkspozitura.FirstOrDefault(e => e.Value == Model.Input?.EkspozituriIdEkspozitura?.ToString())?.Text)
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- License Information Card -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">Лиценца</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label class="form-label">Задолжителна лиценца</label>
                    <div class="data-field">
                        <span class="boolean-value @(Model.Input?.ZadolzitelnoLicenca == true ? "true-value" : "false-value")">
                            <i class="fas @(Model.Input?.ZadolzitelnoLicenca == true ? "fa-check-circle" : "fa-times-circle")"></i>
                            @(Model.Input?.ZadolzitelnoLicenca == true ? "Да" : "Не")
                        </span>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Број на решение од АСО за лиценца</label>
                    <div class="data-field">@Model.Input?.BrojNaResenieOdASOZaLicenca</div>
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Датум на решение од АСО за лиценца</label>
                    <div class="data-field">@(Model.Input?.DatumNaResenieOdASOZaLicenca?.ToString("dd.MM.yyyy"))</div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label class="form-label">Датум на одземена лиценца</label>
                    <div class="data-field">@(Model.Input?.DatumNaOdzemenaLicenca?.ToString("dd.MM.yyyy"))</div>
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Број на дозвола за вршење осигурително брокерски работи</label>
                    <div class="data-field">@Model.Input?.BrojNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti</div>
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Датум на дозвола за вршење осигурително брокерски работи</label>
                    <div class="data-field">@(Model.Input?.DatumNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti?.ToString("dd.MM.yyyy"))</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Files Card -->
    @if (Model.Files != null && Model.Files.Any())
    {
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Прикачени документи</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Име на документ</th>
                                <th>Датум на прикачување</th>
                                <th>Прикачил</th>
                                <th>Акции</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var file in Model.Files)
                            {
                                <tr>
                                    <td>@file.FileName</td>
                                    <td>@file.DateCreated.ToString("dd.MM.yyyy HH:mm")</td>
                                    <td>@file.UsernameCreated</td>
                                    <td>
                                        <a asp-page-handler="DownloadFile" asp-route-fileId="@file.Id" class="btn btn-primary btn-sm">
                                            <i class="fas fa-download"></i> Преземи
                                        </a>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    }
</div> 