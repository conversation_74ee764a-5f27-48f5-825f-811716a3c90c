using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using RazorPortal.Services;

namespace NextBroker.Pages.AdministrationPages
{
    public class ListaPolisiPrivilegiiModel : SecurePageModel
    {
        public ListaPolisiPrivilegiiModel(IConfiguration configuration)
            : base(configuration)
        {
        }

        public class PrivilegijViewModel
        {
            public long Id { get; set; }
            public DateTime? DateCreated { get; set; }
            public string? UsernameCreated { get; set; }
            public long? PraviloZa { get; set; }
            public string? PraviloZaIme { get; set; }
            public long? GledaPolisiOd { get; set; }
            public string? GledaPolisiOdIme { get; set; }
        }

        public class KlientViewModel
        {
            public long Id { get; set; }
            public string? Ime { get; set; }
            public string? Prezime { get; set; }
            public string DisplayName => $"{Ime} {Prezime}".Trim();
        }

        public List<PrivilegijViewModel> Privilegii { get; set; } = new();
        public List<KlientViewModel> Klienti { get; set; } = new();

        public async Task<IActionResult> OnGetAsync()
        {
            if (!await HasPageAccess("ListaPolisiPrivilegii"))
            {
                return RedirectToAccessDenied();
            }

            await LoadPrivilegii();
            await LoadKlienti();

            return Page();
        }

        public async Task<IActionResult> OnPostCreateAsync(long praviloZa, long[] gledaPolisiOd)
        {
            if (!await HasPageAccess("ListaPolisiPrivilegii"))
            {
                return new JsonResult(new { success = false, message = "Немате дозвола за пристап." });
            }

            try
            {
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    var currentUsername = HttpContext.Session.GetString("Username") ?? "System";
                    var createdCount = 0;
                    var duplicateCount = 0;

                    foreach (var gledaOd in gledaPolisiOd)
                    {
                        // Check for duplicates
                        string checkSql = @"
                            SELECT COUNT(*)
                            FROM ListaPolisiPrivilegii
                            WHERE PraviloZa = @PraviloZa AND GledaPolisiOd = @GledaPolisiOd";

                        using (SqlCommand checkCmd = new SqlCommand(checkSql, connection))
                        {
                            checkCmd.Parameters.AddWithValue("@PraviloZa", praviloZa);
                            checkCmd.Parameters.AddWithValue("@GledaPolisiOd", gledaOd);

                            var exists = (int)await checkCmd.ExecuteScalarAsync() > 0;
                            if (exists)
                            {
                                duplicateCount++;
                                continue;
                            }
                        }

                        // Insert new rule
                        string insertSql = @"
                            INSERT INTO ListaPolisiPrivilegii (DateCreated, UsernameCreated, PraviloZa, GledaPolisiOd)
                            VALUES (GETDATE(), @UsernameCreated, @PraviloZa, @GledaPolisiOd)";

                        using (SqlCommand insertCmd = new SqlCommand(insertSql, connection))
                        {
                            insertCmd.Parameters.AddWithValue("@UsernameCreated", currentUsername);
                            insertCmd.Parameters.AddWithValue("@PraviloZa", praviloZa);
                            insertCmd.Parameters.AddWithValue("@GledaPolisiOd", gledaOd);

                            await insertCmd.ExecuteNonQueryAsync();
                            createdCount++;
                        }
                    }

                    var message = $"Успешно креирани {createdCount} правила.";
                    if (duplicateCount > 0)
                    {
                        message += $" {duplicateCount} правила веќе постоеја.";
                    }

                    return new JsonResult(new { success = true, message = message });
                }
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = $"Грешка: {ex.Message}" });
            }
        }

        public async Task<IActionResult> OnPostDeleteAsync(long id)
        {
            if (!await HasPageAccess("ListaPolisiPrivilegii"))
            {
                return new JsonResult(new { success = false, message = "Немате дозвола за пристап." });
            }

            try
            {
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    string deleteSql = "DELETE FROM ListaPolisiPrivilegii WHERE Id = @Id";
                    using (SqlCommand cmd = new SqlCommand(deleteSql, connection))
                    {
                        cmd.Parameters.AddWithValue("@Id", id);
                        var rowsAffected = await cmd.ExecuteNonQueryAsync();

                        if (rowsAffected > 0)
                        {
                            return new JsonResult(new { success = true, message = "Правилото е успешно избришано." });
                        }
                        else
                        {
                            return new JsonResult(new { success = false, message = "Правилото не е пронајдено." });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = $"Грешка: {ex.Message}" });
            }
        }

        private async Task LoadPrivilegii()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                string sql = @"
                    SELECT
                        lpp.Id,
                        lpp.DateCreated,
                        lpp.UsernameCreated,
                        lpp.PraviloZa,
                        COALESCE(klntza.Ime,'') + ' ' + COALESCE(klntza.Prezime,'') as PraviloZaIme,
                        lpp.GledaPolisiOd,
                        COALESCE(klntod.Ime,'') + ' ' + COALESCE(klntod.Prezime,'') as GledaPolisiOdIme
                    FROM ListaPolisiPrivilegii lpp
                    LEFT JOIN Klienti klntza ON klntza.Id = lpp.PraviloZa
                    LEFT JOIN Klienti klntod ON klntod.Id = lpp.GledaPolisiOd
                    ORDER BY lpp.DateCreated DESC";

                using (SqlCommand cmd = new SqlCommand(sql, connection))
                {
                    using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            var privilegij = new PrivilegijViewModel
                            {
                                Id = reader.GetInt64(reader.GetOrdinal("Id")),
                                DateCreated = reader.IsDBNull(reader.GetOrdinal("DateCreated")) ? null : reader.GetDateTime(reader.GetOrdinal("DateCreated")),
                                UsernameCreated = reader.IsDBNull(reader.GetOrdinal("UsernameCreated")) ? null : reader.GetString(reader.GetOrdinal("UsernameCreated")),
                                PraviloZa = reader.IsDBNull(reader.GetOrdinal("PraviloZa")) ? null : reader.GetInt64(reader.GetOrdinal("PraviloZa")),
                                PraviloZaIme = reader.IsDBNull(reader.GetOrdinal("PraviloZaIme")) ? null : reader.GetString(reader.GetOrdinal("PraviloZaIme")),
                                GledaPolisiOd = reader.IsDBNull(reader.GetOrdinal("GledaPolisiOd")) ? null : reader.GetInt64(reader.GetOrdinal("GledaPolisiOd")),
                                GledaPolisiOdIme = reader.IsDBNull(reader.GetOrdinal("GledaPolisiOdIme")) ? null : reader.GetString(reader.GetOrdinal("GledaPolisiOdIme"))
                            };
                            Privilegii.Add(privilegij);
                        }
                    }
                }
            }
        }

        private async Task LoadKlienti()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                string sql = @"
                    SELECT Id, Ime, Prezime
                    FROM Klienti
                    WHERE KlientVraboten = 1 OR KlientSorabotnik = 1
                    ORDER BY Ime, Prezime";

                using (SqlCommand cmd = new SqlCommand(sql, connection))
                {
                    using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            var klient = new KlientViewModel
                            {
                                Id = reader.GetInt64(reader.GetOrdinal("Id")),
                                Ime = reader.IsDBNull(reader.GetOrdinal("Ime")) ? null : reader.GetString(reader.GetOrdinal("Ime")),
                                Prezime = reader.IsDBNull(reader.GetOrdinal("Prezime")) ? null : reader.GetString(reader.GetOrdinal("Prezime"))
                            };
                            Klienti.Add(klient);
                        }
                    }
                }
            }
        }
    }
}