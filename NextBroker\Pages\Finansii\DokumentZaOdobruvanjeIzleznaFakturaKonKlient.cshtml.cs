using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Data.SqlClient;
using RazorPortal.Services;
using System.ComponentModel.DataAnnotations;

namespace NextBroker.Pages.Finansii
{
    public class DokumentZaOdobruvanjeIzleznaFakturaKonKlientModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;

        public DokumentZaOdobruvanjeIzleznaFakturaKonKlientModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        [BindProperty]
        public DateTime DatumOd { get; set; } = DateTime.Today.AddMonths(-1);

        [BindProperty]
        public DateTime DatumDo { get; set; } = DateTime.Today;

        [BindProperty]
        public OdobruvanjeInputModel OdobruvanjeInput { get; set; } = new();

        public List<FakturaResult> FakturaResults { get; set; } = new();
        public List<OdobruvanjeRecord> OdobruvanjeRecords { get; set; } = new();
        public HashSet<string> ExistingApprovals { get; set; } = new();

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("DokumentZaOdobruvanjeIzleznaFakturaKonKlient"))
            {
                return RedirectToAccessDenied();
            }

            await LoadOdobruvanjeRecords();
            await LoadExistingApprovals();
            return Page();
        }

        public async Task<IActionResult> OnPost()
        {
            if (!await HasPageAccess("DokumentZaOdobruvanjeIzleznaFakturaKonKlient"))
            {
                return RedirectToAccessDenied();
            }

            await LoadFakturaResults();
            await LoadOdobruvanjeRecords();
            await LoadExistingApprovals();
            return Page();
        }

        public async Task<IActionResult> OnPostCreateOdobruvanje()
        {
            if (!await HasPageAccess("DokumentZaOdobruvanjeIzleznaFakturaKonKlient"))
            {
                return RedirectToAccessDenied();
            }

            if (!ModelState.IsValid)
            {
                await LoadFakturaResults();
                await LoadOdobruvanjeRecords();
                await LoadExistingApprovals();
                return Page();
            }

            string username = HttpContext.Session.GetString("Username");
            if (string.IsNullOrEmpty(username))
            {
                TempData["ErrorMessage"] = "Вашата сесија е истечена. Најавете се повторно.";
                return RedirectToPage("/Account/Login");
            }

            try
            {
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    using (SqlCommand cmd = new SqlCommand(@"
                        INSERT INTO OdobruvanjeZaPlakanjeVoRokIzleznaFaktura (
                            DateCreated, UsernameCreated, BrojNaFaktura, BrojNaPolisa,
                            Premija, Odobruvanje, KlientiIdOdobruvanjeKon, BrojNaDokument,
                            DatumNaDokument, Iznos
                        ) VALUES (
                            GETDATE(), @UsernameCreated, @BrojNaFaktura, @BrojNaPolisa,
                            @Premija, @Odobruvanje, @KlientiIdOdobruvanjeKon, @BrojNaDokument,
                            @DatumNaDokument, @Iznos
                        )", connection))
                    {
                        cmd.Parameters.AddWithValue("@UsernameCreated", username);
                        cmd.Parameters.AddWithValue("@BrojNaFaktura", OdobruvanjeInput.BrojNaFaktura ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@BrojNaPolisa", OdobruvanjeInput.BrojNaPolisa ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@Premija", OdobruvanjeInput.Premija ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@Odobruvanje", OdobruvanjeInput.Odobruvanje ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@KlientiIdOdobruvanjeKon", OdobruvanjeInput.KlientiIdOdobruvanjeKon ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@BrojNaDokument", OdobruvanjeInput.BrojNaDokument ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@DatumNaDokument", OdobruvanjeInput.DatumNaDokument ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@Iznos", OdobruvanjeInput.Iznos ?? (object)DBNull.Value);

                        await cmd.ExecuteNonQueryAsync();
                    }
                }

                TempData["SuccessMessage"] = "Одобрувањето е успешно креирано.";
                return RedirectToPage();
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"Грешка при креирање на одобрувањето: {ex.Message}";
                await LoadFakturaResults();
                await LoadOdobruvanjeRecords();
                await LoadExistingApprovals();
                return Page();
            }
        }

        public async Task<IActionResult> OnPostStornoOdobruvanje(long id)
        {
            if (!await HasPageAccess("DokumentZaOdobruvanjeIzleznaFakturaKonKlient"))
            {
                return RedirectToAccessDenied();
            }

            string username = HttpContext.Session.GetString("Username");
            if (string.IsNullOrEmpty(username))
            {
                TempData["ErrorMessage"] = "Вашата сесија е истечена. Најавете се повторно.";
                return RedirectToPage("/Account/Login");
            }

            try
            {
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    using (SqlCommand cmd = new SqlCommand(@"
                        UPDATE OdobruvanjeZaPlakanjeVoRokIzleznaFaktura
                        SET Storno = 1, DateModified = GETDATE(), UsernameModified = @UsernameModified
                        WHERE Id = @Id", connection))
                    {
                        cmd.Parameters.AddWithValue("@Id", id);
                        cmd.Parameters.AddWithValue("@UsernameModified", username);

                        int rowsAffected = await cmd.ExecuteNonQueryAsync();
                        if (rowsAffected > 0)
                        {
                            TempData["SuccessMessage"] = "Одобрувањето е успешно сторнирано.";
                        }
                        else
                        {
                            TempData["ErrorMessage"] = "Не е пронајдено одобрување за сторнирање.";
                        }
                    }
                }

                return RedirectToPage();
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"Грешка при сторнирање на одобрувањето: {ex.Message}";
                return RedirectToPage();
            }
        }

        private async Task LoadFakturaResults()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT
                        pfkk.DatumNaFaktura as [DatumNaFaktura],
                        pfkk.BrojNaFaktura as [BrojNaFaktura],
                        pfkk.BrojNaPolisa as [BrojNaPolisa],
                        pfkk.RokNaPlakanjeFakturaIzlezna as [RokNaPlakanje],
                        -- Concatenate Dogovoruvac fields with NULL handling
                        ISNULL(CAST(pfkk.dogovoruvacime AS VARCHAR(100)), '') + ' ' +
                        ISNULL(CAST(pfkk.dogovoruvacprezime AS VARCHAR(100)), '') + ' ' +
                        ISNULL(CAST(pfkk.dogovoruvacnaziv AS VARCHAR(100)), '')  as [Dogovoruvac],

                        -- Concatenate Osigurenik fields with NULL handling
                        ISNULL(CAST(pfkk.osigurenikime AS VARCHAR(100)), '') + ' ' +
                        ISNULL(CAST(pfkk.osigurenikprezime AS VARCHAR(100)), '') +  ' ' +
                        ISNULL(CAST(pfkk.osigureniknaziv AS VARCHAR(100)), '')  as [Osigurenik],

                        pfkk.Osiguritel  as [Osiguritel],
                        pfkk.Produkt as [Produkt],
                        pfkk.VkupnaPremija as [Iznos],
                        pfkk.PremijaZaNaplata as [IznosSoPopust]
                    FROM dbo.PolisiIzlezniFakturiKonKlient pfkk
                    left join Polisi p on pfkk.BrojNaPolisa = p.BrojNaPolisa
                    WHERE pfkk.DatumNaFaktura between @DatumOd and @DatumDo
                    and pfkk.VkupnaPremija > pfkk.PremijaZaNaplata
                    and pfkk.PremijaZaNaplata > 0
                    and p.Storno != 1
                    and dbo.VratiPolisaPosledenDatumNaUplata(p.ID) <= pfkk.RokNaPlakanjeFakturaIzlezna", connection))
                {
                    cmd.Parameters.AddWithValue("@DatumOd", DatumOd.Date);
                    cmd.Parameters.AddWithValue("@DatumDo", DatumDo.Date);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var results = new List<FakturaResult>();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new FakturaResult
                        {
                            DatumNaFaktura = reader["DatumNaFaktura"] == DBNull.Value ? null : (DateTime?)reader["DatumNaFaktura"],
                            BrojNaFaktura = reader["BrojNaFaktura"] == DBNull.Value ? null : reader["BrojNaFaktura"].ToString(),
                            BrojNaPolisa = reader["BrojNaPolisa"] == DBNull.Value ? null : reader["BrojNaPolisa"].ToString(),
                            RokNaPlakanje = reader["RokNaPlakanje"] == DBNull.Value ? null : (DateTime?)reader["RokNaPlakanje"],
                            Dogovoruvac = reader["Dogovoruvac"] == DBNull.Value ? null : reader["Dogovoruvac"].ToString(),
                            Osigurenik = reader["Osigurenik"] == DBNull.Value ? null : reader["Osigurenik"].ToString(),
                            Osiguritel = reader["Osiguritel"] == DBNull.Value ? null : reader["Osiguritel"].ToString(),
                            Produkt = reader["Produkt"] == DBNull.Value ? null : reader["Produkt"].ToString(),
                            Iznos = reader["Iznos"] == DBNull.Value ? null : (decimal?)reader["Iznos"],
                            IznosSoPopust = reader["IznosSoPopust"] == DBNull.Value ? null : (decimal?)reader["IznosSoPopust"]
                        });
                    }
                    FakturaResults = results;
                }
            }
        }

        private async Task LoadOdobruvanjeRecords()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT
                        ozpv.Id, ozpv.DateCreated, ozpv.UsernameCreated, ozpv.BrojNaFaktura, ozpv.BrojNaPolisa,
                        ozpv.Premija, ozpv.Odobruvanje, ozpv.KlientiIdOdobruvanjeKon,
                         ISNULL(CAST(klnt.Ime AS VARCHAR(100)), '') + ' ' +
    ISNULL(CAST(klnt.Prezime AS VARCHAR(100)), '') +  ' ' +
    ISNULL(CAST(klnt.Naziv AS VARCHAR(100)), '')  as [OdobruvanjeKon],
                         ozpv.BrojNaDokument,
                        ozpv.DatumNaDokument, ozpv.Iznos, ozpv.Storno
                    FROM OdobruvanjeZaPlakanjeVoRokIzleznaFaktura ozpv
                    left join klienti klnt on ozpv.KlientiIdOdobruvanjeKon = klnt.id
                    ORDER BY ozpv.Id DESC", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var records = new List<OdobruvanjeRecord>();
                    while (await reader.ReadAsync())
                    {
                        records.Add(new OdobruvanjeRecord
                        {
                            Id = Convert.ToInt64(reader["Id"]),
                            DateCreated = reader["DateCreated"] == DBNull.Value ? null : (DateTime?)reader["DateCreated"],
                            UsernameCreated = reader["UsernameCreated"] == DBNull.Value ? null : reader["UsernameCreated"].ToString(),
                            BrojNaFaktura = reader["BrojNaFaktura"] == DBNull.Value ? null : reader["BrojNaFaktura"].ToString(),
                            BrojNaPolisa = reader["BrojNaPolisa"] == DBNull.Value ? null : reader["BrojNaPolisa"].ToString(),
                            Premija = reader["Premija"] == DBNull.Value ? null : (decimal?)reader["Premija"],
                            Odobruvanje = reader["Odobruvanje"] == DBNull.Value ? null : (decimal?)reader["Odobruvanje"],
                            KlientiIdOdobruvanjeKon = reader["KlientiIdOdobruvanjeKon"] == DBNull.Value ? null : (long?)reader["KlientiIdOdobruvanjeKon"],
                            OdobruvanjeKon = reader["OdobruvanjeKon"] == DBNull.Value ? null : reader["OdobruvanjeKon"].ToString()?.Trim(),
                            BrojNaDokument = reader["BrojNaDokument"] == DBNull.Value ? null : reader["BrojNaDokument"].ToString(),
                            DatumNaDokument = reader["DatumNaDokument"] == DBNull.Value ? null : (DateTime?)reader["DatumNaDokument"],
                            Iznos = reader["Iznos"] == DBNull.Value ? null : (decimal?)reader["Iznos"],
                            Storno = reader["Storno"] == DBNull.Value ? false : Convert.ToBoolean(reader["Storno"])
                        });
                    }
                    OdobruvanjeRecords = records;
                }
            }
        }

        private async Task LoadExistingApprovals()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT DISTINCT BrojNaPolisa
                    FROM OdobruvanjeZaPlakanjeVoRokIzleznaFaktura
                    WHERE (Storno = 0 OR Storno IS NULL)
                    AND BrojNaPolisa IS NOT NULL", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var existingApprovals = new HashSet<string>();
                    while (await reader.ReadAsync())
                    {
                        var brojNaPolisa = reader["BrojNaPolisa"]?.ToString();
                        if (!string.IsNullOrEmpty(brojNaPolisa))
                        {
                            existingApprovals.Add(brojNaPolisa);
                        }
                    }
                    ExistingApprovals = existingApprovals;
                }
            }
        }

        public async Task<IActionResult> OnGetSearchKlientiAsync(string mb)
        {
            if (string.IsNullOrWhiteSpace(mb))
                return new JsonResult(new List<object>());

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT TOP 10
                        Id,
                        ISNULL(MB, '') as MB,
                        ISNULL(EDB, '') as EDB,
                        ISNULL(EMBG, '') as EMBG,
                        CASE
                            WHEN KlientFizickoPravnoLice = 'P' THEN Naziv
                            ELSE CONCAT(ISNULL(Ime, ''), ' ', ISNULL(Prezime, ''))
                        END as Naziv,
                        KlientFizickoPravnoLice,
                        ISNULL(Ime, '') as Ime,
                        ISNULL(Prezime, '') as Prezime
                    FROM Klienti
                    WHERE MB LIKE @Search + '%'
                       OR EDB LIKE @Search + '%'
                       OR EMBG LIKE @Search + '%'
                       OR Naziv LIKE '%' + @Search + '%'
                       OR Ime LIKE '%' + @Search + '%'
                       OR Prezime LIKE '%' + @Search + '%'
                    ORDER BY
                        CASE
                            WHEN MB LIKE @Search + '%' THEN 1
                            WHEN EDB LIKE @Search + '%' THEN 2
                            WHEN EMBG LIKE @Search + '%' THEN 3
                            ELSE 4
                        END,
                        MB, Naziv", connection))
                {
                    cmd.Parameters.AddWithValue("@Search", mb);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var results = new List<object>();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            id = reader["Id"],
                            mb = reader["MB"],
                            edb = reader["EDB"],
                            embg = reader["EMBG"],
                            naziv = reader["Naziv"],
                            ime = reader["Ime"],
                            prezime = reader["Prezime"],
                            tip = reader["KlientFizickoPravnoLice"].ToString()
                        });
                    }
                    return new JsonResult(results);
                }
            }
        }
    }

    public class FakturaResult
    {
        public DateTime? DatumNaFaktura { get; set; }
        public string? BrojNaFaktura { get; set; }
        public string? BrojNaPolisa { get; set; }
        public DateTime? RokNaPlakanje { get; set; }
        public string? Dogovoruvac { get; set; }
        public string? Osigurenik { get; set; }
        public string? Osiguritel { get; set; }
        public string? Produkt { get; set; }
        public decimal? Iznos { get; set; }
        public decimal? IznosSoPopust { get; set; }
    }

    public class OdobruvanjeRecord
    {
        public long Id { get; set; }
        public DateTime? DateCreated { get; set; }
        public string? UsernameCreated { get; set; }
        public string? BrojNaFaktura { get; set; }
        public string? BrojNaPolisa { get; set; }
        public decimal? Premija { get; set; }
        public decimal? Odobruvanje { get; set; }
        public long? KlientiIdOdobruvanjeKon { get; set; }
        public string? OdobruvanjeKon { get; set; }
        public string? BrojNaDokument { get; set; }
        public DateTime? DatumNaDokument { get; set; }
        public decimal? Iznos { get; set; }
        public bool Storno { get; set; }
    }

    public class OdobruvanjeInputModel
    {
        [Required(ErrorMessage = "Број на фактура е задолжително")]
        public string? BrojNaFaktura { get; set; }

        [Required(ErrorMessage = "Број на полиса е задолжително")]
        public string? BrojNaPolisa { get; set; }

        public decimal? Premija { get; set; }
        public decimal? Odobruvanje { get; set; }

        [Display(Name = "Клиент ID за одобрување")]
        public long? KlientiIdOdobruvanjeKon { get; set; }

        [Display(Name = "Број на документ")]
        public string? BrojNaDokument { get; set; }

        [Required(ErrorMessage = "Датум на документ е задолжително")]
        [Display(Name = "Датум на документ")]
        public DateTime? DatumNaDokument { get; set; }

        public decimal? Iznos { get; set; }
    }
}