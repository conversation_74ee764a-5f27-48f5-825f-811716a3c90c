@page
@model NextBroker.Pages.Pregledi.IzvestajGeneriraniIzlezniFakturiKonKlientModel
@{
    ViewData["Title"] = "Извештај за генерирани излезни фактури кон клиент";
}

<div class="container-fluid">
    <h4 class="mb-3">@ViewData["Title"]</h4>

    <form method="post">
        <div class="row mb-3">
            <div class="col-md-3">
                <div class="form-group">
                    <label asp-for="DatumOd" class="control-label"></label>
                    <input asp-for="DatumOd" class="form-control" />
                    <span asp-validation-for="DatumOd" class="text-danger"></span>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group">
                    <label asp-for="DatumDo" class="control-label"></label>
                    <input asp-for="DatumDo" class="form-control" />
                    <span asp-validation-for="DatumDo" class="text-danger"></span>
                </div>
            </div>
            <div class="col-md-4 d-flex align-items-end gap-2">
                <button type="submit" class="btn btn-primary">Генерирај извештај</button>
                @if (Model.Data.Any())
                {
                    <button type="submit" asp-page-handler="ExportToCsv" class="btn btn-success">
                        <i class="fas fa-file-csv me-1"></i>Експорт во CSV
                    </button>
                }
            </div>
        </div>
    </form>

    @if (!ModelState.IsValid)
    {
        <div class="alert alert-danger">
            <ul>
                @foreach (var modelState in ViewData.ModelState.Values)
                {
                    foreach (var error in modelState.Errors)
                    {
                        <li>@error.ErrorMessage</li>
                    }
                }
            </ul>
        </div>
    }

    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show">
            @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    @if (Model.Data.Any())
    {
        <div class="table-responsive">
            <table class="table table-striped table-bordered">
                <thead>
                    <tr>
                        @foreach (var columnName in Model.ColumnNames)
                        {
                            @if (columnName != "FilePath" && columnName != "FileName")
                            {
                                <th>@columnName</th>
                            }
                        }
                        <th>Акции</th>
                    </tr>
                </thead>
                <tbody>
                    @for (int i = 0; i < Model.Data.Count; i++)
                    {
                        var row = Model.Data[i];
                        <tr>
                            @for (int j = 0; j < row.Count; j++)
                            {
                                var columnName = j < Model.ColumnNames.Count ? Model.ColumnNames[j] : "";
                                @if (columnName != "FilePath" && columnName != "FileName")
                                {
                                    var item = row[j];
                                    <td>
                                        @if (item is DateTime date)
                                        {
                                            @date.ToString("dd/MM/yyyy")
                                        }
                                        else if (item is decimal decimalValue)
                                        {
                                            @decimalValue.ToString("N2")
                                        }
                                        else if (item is double doubleValue)
                                        {
                                            @doubleValue.ToString("N2")
                                        }
                                        else if (item is float floatValue)
                                        {
                                            @floatValue.ToString("N2")
                                        }
                                        else
                                        {
                                            @item
                                        }
                                    </td>
                                }
                            }
                            <td>
                                @{
                                    var filePathIndex = Model.ColumnNames.IndexOf("FilePath");
                                    var fileNameIndex = Model.ColumnNames.IndexOf("FileName");
                                    var filePath = filePathIndex >= 0 ? row[filePathIndex]?.ToString() : "";
                                    var fileName = fileNameIndex >= 0 ? row[fileNameIndex]?.ToString() : "";
                                }
                                @if (!string.IsNullOrEmpty(filePath) && !string.IsNullOrEmpty(fileName))
                                {
                                    <form method="post" style="display: inline;">
                                        <input type="hidden" name="FilePath" value="@filePath" />
                                        <input type="hidden" name="FileName" value="@fileName" />
                                        <button type="submit" asp-page-handler="DownloadFile" class="btn btn-sm btn-outline-primary" title="Преземи фајл">
                                            <i class="fas fa-download"></i>
                                        </button>
                                    </form>
                                }
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    }
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
