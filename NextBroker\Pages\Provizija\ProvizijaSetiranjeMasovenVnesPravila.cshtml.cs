using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System.ComponentModel.DataAnnotations;
using Microsoft.Data.SqlClient;
using System.Data;

namespace NextBroker.Pages.Provizija
{
    public class ProvizijaSetiranjeMasovenVnesPravilaModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;

        [TempData]
        public string StatusMessage { get; set; }

        public ProvizijaSetiranjeMasovenVnesPravilaModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        [BindProperty]
        public long KlientiIdOsiguritel { get; set; }
        
        [BindProperty]
        public long KlientProvizijaId { get; set; }
        
        [BindProperty]
        public List<int> SelectedProduktiIds { get; set; } = new List<int>();
        
        [BindProperty]
        [DisplayFormat(DataFormatString = "{0:N2}")]
        public decimal? ProcentNaProvizijaZaFizickiLica { get; set; }
        
        [BindProperty]
        [DisplayFormat(DataFormatString = "{0:N2}")]
        public decimal? ProcentNaProvizijaZaPravniLica { get; set; }
        
        [BindProperty]
        public int SifrarnikNacinNaPresmetkaProvizijaId { get; set; }
        
        [BindProperty]
        public int SifrarnikBrutoNetoProvizija { get; set; }
        
        [BindProperty]
        [DataType(DataType.Date)]
        [Display(Name = "Важи од")]
        public DateTime? DatumVaziOd { get; set; }
        
        [BindProperty]
        [DataType(DataType.Date)]
        [Display(Name = "Важи до")]
        public DateTime? DatumVaziDo { get; set; }

        [BindProperty]
        public long? KlientiIdPrimacProvizija { get; set; }

        [BindProperty]
        public string PrimacProvizijaDisplayText { get; set; }

        public SelectList Osiguriteli { get; set; }
        public SelectList KlientProvizii { get; set; }
        public List<SelectListItem> Produkti { get; set; }
        public SelectList NaciniNaPresmetka { get; set; }
        public SelectList TipoviProvizija { get; set; }

        // Results tracking
        public List<string> SuccessfullyAdded { get; set; } = new List<string>();
        public List<string> AlreadyExisting { get; set; } = new List<string>();

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("ProvizijaSetiranjeDodajPravilo"))
            {
                return RedirectToAccessDenied();
            }

            await LoadDropdowns();
            return Page();
        }

        private async Task LoadDropdowns()
        {
            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();

                // Load Osiguriteli
                using (var command = new SqlCommand("SELECT Id, Naziv FROM Klienti WHERE Osiguritel = 1", connection))
                {
                    using var reader = await command.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(reader["Naziv"].ToString(), reader["Id"].ToString()));
                    }
                    Osiguriteli = new SelectList(items, "Value", "Text");
                }

                // Load KlientProvizii
                using (var command = new SqlCommand("SELECT Id, KlientProvizija FROM KlientProvizija", connection))
                {
                    using var reader = await command.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(reader["KlientProvizija"].ToString(), reader["Id"].ToString()));
                    }
                    KlientProvizii = new SelectList(items, "Value", "Text");
                }



                // Load All Produkti as List for checkboxes
                using (var command = new SqlCommand(@"
                    SELECT p.Id, p.KlasaOsiguruvanjeId, p.Ime + ' (' + CAST(k.KlasaBroj AS VARCHAR) + ')' AS ProduktDisplay,
                           k.KlasaBroj, k.KlasaIme
                    FROM Produkti p
                    INNER JOIN KlasiOsiguruvanje k ON p.KlasaOsiguruvanjeId = k.Id                    
                    ORDER BY k.KlasaBroj, p.Ime", connection))
                {
                    using var reader = await command.ExecuteReaderAsync();
                    Produkti = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        var item = new SelectListItem(reader["ProduktDisplay"].ToString(), reader["Id"].ToString());
                        item.Group = new SelectListGroup { Name = reader["KlasaBroj"].ToString() + " - " + reader["KlasaIme"].ToString() };
                        Produkti.Add(item);
                    }
                }

                // Load NaciniNaPresmetka
                using (var command = new SqlCommand("SELECT Id, Nacin FROM SifrarnikNacinNaPresmetkaProvizija", connection))
                {
                    using var reader = await command.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(reader["Nacin"].ToString(), reader["Id"].ToString()));
                    }
                    NaciniNaPresmetka = new SelectList(items, "Value", "Text");
                }

                // Load TipoviProvizija
                using (var command = new SqlCommand("SELECT Id, TipNaProvizija FROM SifrarnikBrutoNetoProvizija", connection))
                {
                    using var reader = await command.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(reader["TipNaProvizija"].ToString(), reader["Id"].ToString()));
                    }
                    TipoviProvizija = new SelectList(items, "Value", "Text");
                }

                // Load PrimacProvizija display text if ID exists
                if (KlientiIdPrimacProvizija.HasValue)
                {
                    using var command = new SqlCommand(@"
                        SELECT 
                            CASE 
                                WHEN KlientFizickoPravnoLice = 'P' THEN Naziv
                                ELSE CONCAT(ISNULL(Ime, ''), ' ', ISNULL(Prezime, ''))
                            END as DisplayText
                        FROM Klienti 
                        WHERE Id = @Id", connection);
                    command.Parameters.AddWithValue("@Id", KlientiIdPrimacProvizija.Value);
                    var result = await command.ExecuteScalarAsync();
                    if (result != null)
                    {
                        PrimacProvizijaDisplayText = result.ToString();
                    }
                }
            }
        }

        private async Task<bool> HasOverlappingRule(int klasaId, int produktId)
        {
            using (SqlConnection connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();

                string query = @"
                    SELECT COUNT(*)
                    FROM ProvizijaSetiranje
                    WHERE KlientiIdOsiguritel = @KlientiIdOsiguritel
                    AND KlasiOsiguruvanjeIdKlasa = @KlasiOsiguruvanjeIdKlasa
                    AND ProduktiIdProizvod = @ProduktiIdProizvod
                    AND KlientiIdPrimacProvizija = @KlientiIdPrimacProvizija
                    AND (
                        (DatumVaziDo IS NULL) OR
                        (DatumVaziDo >= @DatumVaziOd)
                    )";

                using (SqlCommand command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@KlientiIdOsiguritel", KlientiIdOsiguritel);
                    command.Parameters.AddWithValue("@KlasiOsiguruvanjeIdKlasa", klasaId);
                    command.Parameters.AddWithValue("@ProduktiIdProizvod", produktId);
                    command.Parameters.AddWithValue("@KlientiIdPrimacProvizija", KlientiIdPrimacProvizija);
                    command.Parameters.AddWithValue("@DatumVaziOd", DatumVaziOd ?? (object)DBNull.Value);

                    int count = (int)await command.ExecuteScalarAsync();
                    return count > 0;
                }
            }
        }

        private async Task<string> GetKlasaDisplayName(int klasaId)
        {
            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();
                using var command = new SqlCommand(@"
                    SELECT CAST(KlasaBroj AS VARCHAR(10)) + ' - ' + KlasaIme AS KlasaDisplay 
                    FROM KlasiOsiguruvanje 
                    WHERE Id = @Id", connection);
                command.Parameters.AddWithValue("@Id", klasaId);
                var result = await command.ExecuteScalarAsync();
                return result?.ToString() ?? $"Klasa ID: {klasaId}";
            }
        }

        private async Task<string> GetProduktDisplayName(int produktId)
        {
            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();
                using var command = new SqlCommand(@"
                    SELECT p.Ime + ' (' + CAST(k.KlasaBroj AS VARCHAR) + ')' AS ProduktDisplay
                    FROM Produkti p
                    INNER JOIN KlasiOsiguruvanje k ON p.KlasaOsiguruvanjeId = k.Id
                    WHERE p.Id = @Id", connection);
                command.Parameters.AddWithValue("@Id", produktId);
                var result = await command.ExecuteScalarAsync();
                return result?.ToString() ?? $"Produkt ID: {produktId}";
            }
        }

        private async Task<int> GetKlasaIdForProdukt(int produktId, SqlConnection connection)
        {
            using var command = new SqlCommand(@"
                SELECT KlasaOsiguruvanjeId
                FROM Produkti
                WHERE Id = @Id", connection);
            command.Parameters.AddWithValue("@Id", produktId);
            var result = await command.ExecuteScalarAsync();
            return result != null ? (int)result : 0;
        }

        public async Task<IActionResult> OnPostAsync()
        {
            // Clear previous results
            SuccessfullyAdded.Clear();
            AlreadyExisting.Clear();

            // Validate date range
            if (DatumVaziOd.HasValue && DatumVaziDo.HasValue && DatumVaziOd > DatumVaziDo)
            {
                ModelState.AddModelError("DatumVaziOd", "Датумот 'Важи од' мора да биде пред или еднаков на датумот 'Важи до'.");
            }

            // Basic validation
            if (SelectedProduktiIds == null || !SelectedProduktiIds.Any())
            {
                ModelState.AddModelError("SelectedProduktiIds", "Мора да изберете најмалку еден производ.");
            }

            if (!ModelState.IsValid)
            {
                await LoadDropdowns();
                return Page();
            }

            try
            {
                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();

                    // Process each selected product
                    foreach (var produktId in SelectedProduktiIds)
                    {
                        // Get the class ID for this product
                        var klasaId = await GetKlasaIdForProdukt(produktId, connection);
                        
                        // Get display names for this product and its class
                        var klasaName = await GetKlasaDisplayName(klasaId);
                        var produktName = await GetProduktDisplayName(produktId);
                        var combinationName = $"{klasaName} - {produktName}";

                        // Check if this combination already exists
                        if (await HasOverlappingRule(klasaId, produktId))
                        {
                            AlreadyExisting.Add(combinationName);
                            continue;
                        }

                        // Insert the new rule
                        using var command = new SqlCommand(@"
                            INSERT INTO ProvizijaSetiranje (
                                UsernameCreated,
                                KlientiIdOsiguritel,
                                KlientProvizijaId,
                                KlasiOsiguruvanjeIdKlasa,
                                ProduktiIdProizvod,
                                ProcentNaProvizijaZaFizickiLica,
                                ProcentNaProvizijaZaPravniLica,
                                SifrarnikNacinNaPresmetkaProvizijaId,
                                SifrarnikBrutoNetoProvizija,
                                DatumVaziOd,
                                DatumVaziDo,
                                KlientiIdPrimacProvizija
                            ) VALUES (
                                @UsernameCreated,
                                @KlientiIdOsiguritel,
                                @KlientProvizijaId,
                                @KlasiOsiguruvanjeIdKlasa,
                                @ProduktiIdProizvod,
                                @ProcentNaProvizijaZaFizickiLica,
                                @ProcentNaProvizijaZaPravniLica,
                                @SifrarnikNacinNaPresmetkaProvizijaId,
                                @SifrarnikBrutoNetoProvizija,
                                @DatumVaziOd,
                                @DatumVaziDo,
                                @KlientiIdPrimacProvizija
                            )", connection);

                        command.Parameters.AddWithValue("@UsernameCreated", HttpContext.Session.GetString("Username"));
                        command.Parameters.AddWithValue("@KlientiIdOsiguritel", KlientiIdOsiguritel);
                        command.Parameters.AddWithValue("@KlientProvizijaId", KlientProvizijaId);
                        command.Parameters.AddWithValue("@KlasiOsiguruvanjeIdKlasa", klasaId);
                        command.Parameters.AddWithValue("@ProduktiIdProizvod", produktId);
                        command.Parameters.AddWithValue("@ProcentNaProvizijaZaFizickiLica", (object)ProcentNaProvizijaZaFizickiLica ?? DBNull.Value);
                        command.Parameters.AddWithValue("@ProcentNaProvizijaZaPravniLica", (object)ProcentNaProvizijaZaPravniLica ?? DBNull.Value);
                        command.Parameters.AddWithValue("@SifrarnikNacinNaPresmetkaProvizijaId", SifrarnikNacinNaPresmetkaProvizijaId);
                        command.Parameters.AddWithValue("@SifrarnikBrutoNetoProvizija", SifrarnikBrutoNetoProvizija);
                        command.Parameters.AddWithValue("@DatumVaziOd", (object)DatumVaziOd ?? DBNull.Value);
                        command.Parameters.AddWithValue("@DatumVaziDo", (object)DatumVaziDo ?? DBNull.Value);
                        command.Parameters.AddWithValue("@KlientiIdPrimacProvizija", (object)KlientiIdPrimacProvizija ?? DBNull.Value);

                        await command.ExecuteNonQueryAsync();
                        SuccessfullyAdded.Add(combinationName);
                    }

                    // Generate status message
                    var messageParts = new List<string>();
                    if (SuccessfullyAdded.Any())
                    {
                        messageParts.Add($"Успешно додадени {SuccessfullyAdded.Count} правила.");
                    }
                    if (AlreadyExisting.Any())
                    {
                        messageParts.Add($"{AlreadyExisting.Count} правила веќе постојат и не се додадени.");
                    }

                    StatusMessage = string.Join(" ", messageParts);
                }
            }
            catch (Exception ex)
            {
                StatusMessage = "Грешка при зачувување на правилата за провизија.";
                ModelState.AddModelError(string.Empty, "Настана грешка при зачувување на податоците.");
            }

            await LoadDropdowns();
            return Page();
        }

        public async Task<JsonResult> OnGetFilteredProdukti(int klasaId)
        {
            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();
                using var command = new SqlCommand(@"
                    SELECT p.Id, p.KlasaOsiguruvanjeId, p.Ime + ' (' + CAST(k.KlasaBroj AS VARCHAR) + ')' AS ProduktDisplay
                    FROM Produkti p
                    INNER JOIN KlasiOsiguruvanje k ON p.KlasaOsiguruvanjeId = k.Id
                    WHERE p.KlasaOsiguruvanjeId = @KlasaId
                    ORDER BY p.Ime", connection);

                command.Parameters.AddWithValue("@KlasaId", klasaId);

                using var reader = await command.ExecuteReaderAsync();
                var items = new List<object>();
                while (await reader.ReadAsync())
                {
                    items.Add(new
                    {
                        id = reader["Id"].ToString(),
                        text = reader["ProduktDisplay"].ToString()
                    });
                }
                return new JsonResult(items);
            }
        }

        public async Task<JsonResult> OnGetSearchKlientiAsync(string mb)
        {
            if (string.IsNullOrWhiteSpace(mb))
                return new JsonResult(new List<object>());

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT TOP 10 
                        Id, 
                        ISNULL(MB, '') as MB,
                        ISNULL(EDB, '') as EDB,
                        ISNULL(EMBG, '') as EMBG,
                        CASE 
                            WHEN KlientFizickoPravnoLice = 'P' THEN Naziv
                            ELSE CONCAT(ISNULL(Ime, ''), ' ', ISNULL(Prezime, ''))
                        END as Naziv,
                        KlientFizickoPravnoLice,
                        ISNULL(Ime, '') as Ime,
                        ISNULL(Prezime, '') as Prezime
                    FROM Klienti 
                    WHERE MB LIKE @Search + '%'
                       OR EDB LIKE @Search + '%'
                       OR EMBG LIKE @Search + '%'
                       OR Naziv LIKE '%' + @Search + '%'
                       OR Ime LIKE '%' + @Search + '%'
                       OR Prezime LIKE '%' + @Search + '%'
                    ORDER BY 
                        CASE 
                            WHEN MB LIKE @Search + '%' THEN 1
                            WHEN EDB LIKE @Search + '%' THEN 2
                            WHEN EMBG LIKE @Search + '%' THEN 3
                            ELSE 4
                        END,
                        MB, Naziv", connection))
                {
                    cmd.Parameters.AddWithValue("@Search", mb);
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var results = new List<object>();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            id = reader["Id"],
                            mb = reader["MB"],
                            edb = reader["EDB"],
                            embg = reader["EMBG"],
                            naziv = reader["Naziv"],
                            ime = reader["Ime"],
                            prezime = reader["Prezime"],
                            tip = reader["KlientFizickoPravnoLice"].ToString()
                        });
                    }
                    return new JsonResult(results);
                }
            }
        }
    }
}
