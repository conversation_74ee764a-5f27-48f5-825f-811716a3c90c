using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Data.SqlClient;
using RazorPortal.Services;
using System.ComponentModel.DataAnnotations;

namespace NextBroker.Pages.Finansii
{
    public class DokumentZaOdobruvanjeVleznaFakturaKonBrokerModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;

        public DokumentZaOdobruvanjeVleznaFakturaKonBrokerModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        [BindProperty]
        public DateTime DatumOd { get; set; } = DateTime.Today.AddMonths(-1);

        [BindProperty]
        public DateTime DatumDo { get; set; } = DateTime.Today;

        [BindProperty]
        public VleznaFakturaOdobruvanjeInputModel OdobruvanjeInput { get; set; } = new();

        public List<VleznaFakturaResult> FakturaResults { get; set; } = new();
        public List<VleznaFakturaOdobruvanjeRecord> OdobruvanjeRecords { get; set; } = new();
        public HashSet<string> ExistingApprovals { get; set; } = new();

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("DokumentZaOdobruvanjeVleznaFakturaKonBroker"))
            {
                return RedirectToAccessDenied();
            }

            await LoadOdobruvanjeRecords();
            await LoadExistingApprovals();
            return Page();
        }

        public async Task<IActionResult> OnPost()
        {
            if (!await HasPageAccess("DokumentZaOdobruvanjeVleznaFakturaKonBroker"))
            {
                return RedirectToAccessDenied();
            }

            await LoadFakturaResults();
            await LoadOdobruvanjeRecords();
            await LoadExistingApprovals();
            return Page();
        }

        public async Task<IActionResult> OnPostCreateOdobruvanje()
        {
            if (!await HasPageAccess("DokumentZaOdobruvanjeVleznaFakturaKonBroker"))
            {
                return RedirectToAccessDenied();
            }

            if (!ModelState.IsValid)
            {
                await LoadFakturaResults();
                await LoadOdobruvanjeRecords();
                await LoadExistingApprovals();
                return Page();
            }

            string username = HttpContext.Session.GetString("Username");
            if (string.IsNullOrEmpty(username))
            {
                TempData["ErrorMessage"] = "Вашата сесија е истечена. Најавете се повторно.";
                return RedirectToPage("/Account/Login");
            }

            try
            {
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    using (SqlCommand cmd = new SqlCommand(@"
                        INSERT INTO OdobruvanjeZaPlakanjeVoRokVlezniKonBroker (
                            DateCreated, UsernameCreated, BrojNaFaktura,
                            Premija, Odobruvanje, KlientiIdOdobruvanjeKon, BrojNaDokument,
                            DatumNaDokument, Iznos
                        ) VALUES (
                            GETDATE(), @UsernameCreated, @BrojNaFaktura,
                            @Premija, @Odobruvanje, @KlientiIdOdobruvanjeKon, @BrojNaDokument,
                            @DatumNaDokument, @Iznos
                        )", connection))
                    {
                        cmd.Parameters.AddWithValue("@UsernameCreated", username);
                        cmd.Parameters.AddWithValue("@BrojNaFaktura", OdobruvanjeInput.BrojNaFaktura ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@Premija", OdobruvanjeInput.Premija ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@Odobruvanje", OdobruvanjeInput.Odobruvanje ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@KlientiIdOdobruvanjeKon", OdobruvanjeInput.KlientiIdOdobruvanjeKon ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@BrojNaDokument", OdobruvanjeInput.BrojNaDokument ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@DatumNaDokument", OdobruvanjeInput.DatumNaDokument ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@Iznos", OdobruvanjeInput.Iznos ?? (object)DBNull.Value);

                        await cmd.ExecuteNonQueryAsync();
                    }
                }

                TempData["SuccessMessage"] = "Одобрувањето е успешно креирано.";
                return RedirectToPage();
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"Грешка при креирање на одобрувањето: {ex.Message}";
                await LoadFakturaResults();
                await LoadOdobruvanjeRecords();
                await LoadExistingApprovals();
                return Page();
            }
        }

        public async Task<IActionResult> OnPostStornoOdobruvanje(long id)
        {
            if (!await HasPageAccess("DokumentZaOdobruvanjeVleznaFakturaKonBroker"))
            {
                return RedirectToAccessDenied();
            }

            string username = HttpContext.Session.GetString("Username");
            if (string.IsNullOrEmpty(username))
            {
                TempData["ErrorMessage"] = "Вашата сесија е истечена. Најавете се повторно.";
                return RedirectToPage("/Account/Login");
            }

            try
            {
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    using (SqlCommand cmd = new SqlCommand(@"
                        UPDATE OdobruvanjeZaPlakanjeVoRokVlezniKonBroker
                        SET Storno = 1, DateModified = GETDATE(), UsernameModified = @UsernameModified
                        WHERE Id = @Id", connection))
                    {
                        cmd.Parameters.AddWithValue("@Id", id);
                        cmd.Parameters.AddWithValue("@UsernameModified", username);

                        int rowsAffected = await cmd.ExecuteNonQueryAsync();
                        if (rowsAffected > 0)
                        {
                            TempData["SuccessMessage"] = "Одобрувањето е успешно сторнирано.";
                        }
                        else
                        {
                            TempData["ErrorMessage"] = "Не е пронајдено одобрување за сторнирање.";
                        }
                    }
                }

                return RedirectToPage();
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"Грешка при сторнирање на одобрувањето: {ex.Message}";
                return RedirectToPage();
            }
        }

        private async Task LoadFakturaResults()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    select
                        distinct pnns.Id,
                         pnns.DatumNaVleznaFaktura,
                         pnns.BrojNaFaktura,
                         pnns.RokNaPlakjanjeFakturaVlezna,
                         klnt.Naziv,
                         pnns.IznosNaFaktura + dbo.VratiOZZbirIznosOZSpecifikacijaPoPolisi(pnns.BrojNaFaktura) + dbo.VraziOZIznosVleznaFaktura(pnns.BrojNaFaktura) as [IznosNaFaktura],
                         pnns.IznosNaFakturaVoRok + dbo.VratiOZZbirIznosOZSpecifikacijaPoPolisi(pnns.BrojNaFaktura) + dbo.VraziOZIznosVleznaFaktura(pnns.BrojNaFaktura) as [IznosNaFakturaVoRok]
                    from PrenosNaNaplataSpecifikacii pnns
                    left join Klienti klnt on pnns.KlientiIdOsiguritel = klnt.Id
                    left join StavkaPremija stavprm on pnns.StavkaPremijaId = stavprm.Id
                    left join polisi p on pnns.BrojNafaktura = p.BrojNaFakturaVlezna
                    WHERE p.Storno != 1
                    AND p.TipNaFaktura = 'Влезна фактура кон брокер'
                    AND  pnns.IznosNaFaktura > pnns.IznosNaFakturaVoRok
                    and (((pnns.IznosNaFaktura + dbo.VratiOZZbirIznosOZSpecifikacijaPoPolisi(pnns.BrojNaFaktura) + dbo.VraziOZIznosVleznaFaktura(pnns.BrojNaFaktura)) - dbo.VratiZbirNaIznosStavkiPovrzaniSoSpecifikacijaPrenosNaNaplata(pnns.ID)) = 0) OR (((pnns.IznosNaFakturaVoRok + dbo.VratiOZZbirIznosOZSpecifikacijaPoPolisi(pnns.BrojNaFaktura) + dbo.VraziOZIznosVleznaFaktura(pnns.BrojNaFaktura)) - dbo.VratiZbirNaIznosStavkiPovrzaniSoSpecifikacijaPrenosNaNaplata(pnns.ID)) = 0 and dbo.VratiDatumNaIzvodPoStavka(dbo.VratiPrenosNaNaplataListaSpecifikaciiPoslednaPovrzanaStavka(pnns.Id)) <= pnns.RokNaPlakjanjeFakturaVlezna) and dbo.VratiTipNaFakturaPoBrojNaFaktura(pnns.BrojNaFaktura) = 'Влезна фактура кон брокер'
                    and dbo.VratiDatumNaIzvodPoStavka(pnns.StavkaPremijaId) is not null
                    and dbo.VratiDatumNaIzvodPoStavka(pnns.StavkaPremijaId) <=  pnns.RokNaPlakjanjeFakturaVlezna
                    and pnns.DatumNaVleznaFaktura between @DatumOd and @DatumDo", connection))
                {
                    cmd.Parameters.AddWithValue("@DatumOd", DatumOd.Date);
                    cmd.Parameters.AddWithValue("@DatumDo", DatumDo.Date);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var results = new List<VleznaFakturaResult>();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new VleznaFakturaResult
                        {
                            Id = reader["Id"] == DBNull.Value ? null : Convert.ToInt64(reader["Id"]),
                            DatumNaVleznaFaktura = reader["DatumNaVleznaFaktura"] == DBNull.Value ? null : (DateTime?)reader["DatumNaVleznaFaktura"],
                            BrojNaFaktura = reader["BrojNaFaktura"] == DBNull.Value ? null : reader["BrojNaFaktura"].ToString(),
                            RokNaPlakjanjeFakturaVlezna = reader["RokNaPlakjanjeFakturaVlezna"] == DBNull.Value ? null : (DateTime?)reader["RokNaPlakjanjeFakturaVlezna"],
                            Naziv = reader["Naziv"] == DBNull.Value ? null : reader["Naziv"].ToString(),
                            IznosNaFaktura = reader["IznosNaFaktura"] == DBNull.Value ? null : (decimal?)reader["IznosNaFaktura"],
                            IznosNaFakturaVoRok = reader["IznosNaFakturaVoRok"] == DBNull.Value ? null : (decimal?)reader["IznosNaFakturaVoRok"]
                        });
                    }
                    FakturaResults = results;
                }
            }
        }

        private async Task LoadOdobruvanjeRecords()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT
                        ozpv.Id, ozpv.DateCreated, ozpv.UsernameCreated, ozpv.BrojNaFaktura,
                        ozpv.Premija, ozpv.Odobruvanje, ozpv.KlientiIdOdobruvanjeKon,
                         ISNULL(CAST(klnt.Ime AS VARCHAR(100)), '') + ' ' +
    ISNULL(CAST(klnt.Prezime AS VARCHAR(100)), '') +  ' ' +
    ISNULL(CAST(klnt.Naziv AS VARCHAR(100)), '')  as [OdobruvanjeKon],
                         ozpv.BrojNaDokument,
                        ozpv.DatumNaDokument, ozpv.Iznos, ozpv.Storno
                    FROM OdobruvanjeZaPlakanjeVoRokVlezniKonBroker ozpv
                    left join klienti klnt on ozpv.KlientiIdOdobruvanjeKon = klnt.id
                    ORDER BY ozpv.Id DESC", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var records = new List<VleznaFakturaOdobruvanjeRecord>();
                    while (await reader.ReadAsync())
                    {
                        records.Add(new VleznaFakturaOdobruvanjeRecord
                        {
                            Id = Convert.ToInt64(reader["Id"]),
                            DateCreated = reader["DateCreated"] == DBNull.Value ? null : (DateTime?)reader["DateCreated"],
                            UsernameCreated = reader["UsernameCreated"] == DBNull.Value ? null : reader["UsernameCreated"].ToString(),
                            BrojNaFaktura = reader["BrojNaFaktura"] == DBNull.Value ? null : reader["BrojNaFaktura"].ToString(),
                            Premija = reader["Premija"] == DBNull.Value ? null : (decimal?)reader["Premija"],
                            Odobruvanje = reader["Odobruvanje"] == DBNull.Value ? null : (decimal?)reader["Odobruvanje"],
                            KlientiIdOdobruvanjeKon = reader["KlientiIdOdobruvanjeKon"] == DBNull.Value ? null : (long?)reader["KlientiIdOdobruvanjeKon"],
                            OdobruvanjeKon = reader["OdobruvanjeKon"] == DBNull.Value ? null : reader["OdobruvanjeKon"].ToString()?.Trim(),
                            BrojNaDokument = reader["BrojNaDokument"] == DBNull.Value ? null : reader["BrojNaDokument"].ToString(),
                            DatumNaDokument = reader["DatumNaDokument"] == DBNull.Value ? null : (DateTime?)reader["DatumNaDokument"],
                            Iznos = reader["Iznos"] == DBNull.Value ? null : (decimal?)reader["Iznos"],
                            Storno = reader["Storno"] == DBNull.Value ? false : Convert.ToBoolean(reader["Storno"])
                        });
                    }
                    OdobruvanjeRecords = records;
                }
            }
        }

        private async Task LoadExistingApprovals()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT DISTINCT BrojNaFaktura
                    FROM OdobruvanjeZaPlakanjeVoRokVlezniKonBroker
                    WHERE (Storno = 0 OR Storno IS NULL)
                    AND BrojNaFaktura IS NOT NULL", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var existingApprovals = new HashSet<string>();
                    while (await reader.ReadAsync())
                    {
                        var brojNaFaktura = reader["BrojNaFaktura"]?.ToString();
                        if (!string.IsNullOrEmpty(brojNaFaktura))
                        {
                            existingApprovals.Add(brojNaFaktura);
                        }
                    }
                    ExistingApprovals = existingApprovals;
                }
            }
        }

        public async Task<IActionResult> OnGetSearchKlientiAsync(string mb)
        {
            if (string.IsNullOrWhiteSpace(mb))
                return new JsonResult(new List<object>());

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT TOP 10
                        Id,
                        ISNULL(MB, '') as MB,
                        ISNULL(EDB, '') as EDB,
                        ISNULL(EMBG, '') as EMBG,
                        CASE
                            WHEN KlientFizickoPravnoLice = 'P' THEN Naziv
                            ELSE CONCAT(ISNULL(Ime, ''), ' ', ISNULL(Prezime, ''))
                        END as Naziv,
                        KlientFizickoPravnoLice,
                        ISNULL(Ime, '') as Ime,
                        ISNULL(Prezime, '') as Prezime
                    FROM Klienti
                    WHERE MB LIKE @Search + '%'
                       OR EDB LIKE @Search + '%'
                       OR EMBG LIKE @Search + '%'
                       OR Naziv LIKE '%' + @Search + '%'
                       OR Ime LIKE '%' + @Search + '%'
                       OR Prezime LIKE '%' + @Search + '%'
                    ORDER BY
                        CASE
                            WHEN MB LIKE @Search + '%' THEN 1
                            WHEN EDB LIKE @Search + '%' THEN 2
                            WHEN EMBG LIKE @Search + '%' THEN 3
                            ELSE 4
                        END,
                        MB, Naziv", connection))
                {
                    cmd.Parameters.AddWithValue("@Search", mb);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var results = new List<object>();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            id = reader["Id"],
                            mb = reader["MB"],
                            edb = reader["EDB"],
                            embg = reader["EMBG"],
                            naziv = reader["Naziv"],
                            ime = reader["Ime"],
                            prezime = reader["Prezime"],
                            tip = reader["KlientFizickoPravnoLice"].ToString()
                        });
                    }
                    return new JsonResult(results);
                }
            }
        }
    }

    public class VleznaFakturaResult
    {
        public long? Id { get; set; }
        public DateTime? DatumNaVleznaFaktura { get; set; }
        public string? BrojNaFaktura { get; set; }
        public DateTime? RokNaPlakjanjeFakturaVlezna { get; set; }
        public string? Naziv { get; set; }
        public decimal? IznosNaFaktura { get; set; }
        public decimal? IznosNaFakturaVoRok { get; set; }
    }

    public class VleznaFakturaOdobruvanjeRecord
    {
        public long Id { get; set; }
        public DateTime? DateCreated { get; set; }
        public string? UsernameCreated { get; set; }
        public string? BrojNaFaktura { get; set; }
        public decimal? Premija { get; set; }
        public decimal? Odobruvanje { get; set; }
        public long? KlientiIdOdobruvanjeKon { get; set; }
        public string? OdobruvanjeKon { get; set; }
        public string? BrojNaDokument { get; set; }
        public DateTime? DatumNaDokument { get; set; }
        public decimal? Iznos { get; set; }
        public bool Storno { get; set; }
    }

    public class VleznaFakturaOdobruvanjeInputModel
    {
        [Required(ErrorMessage = "Број на фактура е задолжително")]
        public string? BrojNaFaktura { get; set; }

        public decimal? Premija { get; set; }
        public decimal? Odobruvanje { get; set; }

        [Display(Name = "Клиент ID за одобрување")]
        public long? KlientiIdOdobruvanjeKon { get; set; }

        [Display(Name = "Број на документ")]
        public string? BrojNaDokument { get; set; }

        [Required(ErrorMessage = "Датум на документ е задолжително")]
        [Display(Name = "Датум на документ")]
        public DateTime? DatumNaDokument { get; set; }

        public decimal? Iznos { get; set; }
    }
}