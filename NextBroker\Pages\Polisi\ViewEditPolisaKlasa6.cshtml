@page "{id:long}"
@model NextBroker.Pages.Polisi.ViewEditPolisaKlasa6Model
@{
    ViewData["Title"] = "Преглед/Измена на полиса Класа 6";
    var formClass = !Model.HasAdminAccess ? "form-disabled" : "";
}

<style>
    .readonly-select {
        pointer-events: none;
        background-color: #e9ecef !important;
        opacity: 1 !important;
    }
    .readonly-select:focus {
        outline: none;
        box-shadow: none;
    }
</style>

<div class="container-fluid">
    <div class="card">
        <div class="card-header">
            <h4>@ViewData["Title"]</h4>
        </div>
        <div class="card-body">
            <div class="text-end mb-3">
                <button type="button" class="btn btn-outline-secondary btn-sm" id="collapseAllBtn">
                    <i class="bi bi-chevron-up"></i> Затвори ги сите секции
                </button>
            </div>

            <!-- Debug Information -->
            @if (!ModelState.IsValid)
            {
                <div class="alert alert-danger">
                    <h5>Model State Errors:</h5>
                    <div asp-validation-summary="All" class="text-danger"></div>
                </div>
            }

            @if (TempData["ErrorMessage"] != null)
            {
                <div class="alert alert-danger">
                    <h5>Error:</h5>
                    <p>@TempData["ErrorMessage"]</p>
                </div>
            }

            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success">
                    <h5>Success:</h5>
                    <p>@TempData["SuccessMessage"]</p>
                </div>
            }

            @if (TempData["DebugInfo"] != null)
            {
                <div class="alert alert-info">
                    <h5>Debug Info:</h5>
                    <pre>@TempData["DebugInfo"]</pre>
                </div>
            }
            <!-- End Debug Information -->

            @if (!Model.HasAdminAccess)
            {
                <style>
                    .form-disabled input:not([type="hidden"]),
                    .form-disabled select,
                    .form-disabled textarea {
                        pointer-events: none;
                        background-color: #e9ecef;
                        opacity: 1;
                    }
                </style>
            }

            <form method="post" class="@formClass">
                <!-- Non-editable Information Card -->
                <div class="card mb-3">
                    <div class="card-header" role="button" data-bs-toggle="collapse" data-bs-target="#systemInfo" aria-expanded="false" aria-controls="systemInfo" style="cursor: pointer;">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Системски информации</h5>
                            <i class="bi bi-chevron-down"></i>
                        </div>
                    </div>
                    <div class="collapse show" id="systemInfo">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-2 mb-3">
                                    <label class="form-label">ID</label>
                                    <input type="text" class="form-control" value="@Model.Input.Id" readonly />
                                    <input type="hidden" asp-for="Input.Id" />
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label class="form-label">Датум на креирање</label>
                                    <input type="text" class="form-control" value="@(Model.Input.DateCreated?.ToString("dd.MM.yyyy HH:mm:ss"))" readonly />
                                </div>
                                <div class="col-md-2 mb-3">
                                    <label class="form-label">Креирано од</label>
                                    <input type="text" class="form-control" value="@Model.Input.UsernameCreated" readonly />
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label class="form-label">Последна промена</label>
                                    <input type="text" class="form-control" value="@(Model.Input.DateModified?.ToString("dd.MM.yyyy HH:mm:ss"))" readonly />
                                </div>
                                <div class="col-md-2 mb-3">
                                    <label class="form-label">Променето од</label>
                                    <input type="text" class="form-control" value="@Model.Input.UsernameModified" readonly />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <input type="hidden" asp-for="Input.DateCreated" />
                <input type="hidden" asp-for="Input.UsernameCreated" />
                <input type="hidden" asp-for="Input.DateModified" />
                <input type="hidden" asp-for="Input.UsernameModified" />

                <!-- Basic Information Card -->
                <div class="card mb-3">
                    <div class="card-header" role="button" data-bs-toggle="collapse" data-bs-target="#basicInfo" aria-expanded="false" aria-controls="basicInfo" style="cursor: pointer;">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Основни информации</h5>
                            <i class="bi bi-chevron-down"></i>
                        </div>
                    </div>
                    <div class="collapse show" id="basicInfo">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label class="form-label">Осигурител</label>
                                        <select class="form-control readonly-select" asp-for="Input.KlientiIdOsiguritel" asp-items="Model.Osiguriteli" readonly>
                                            <option value="">-- Изберете осигурител --</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label class="form-label">Класа на осигурување</label>
                                        <select class="form-control" asp-for="Input.KlasiOsiguruvanjeIdKlasa" asp-items="Model.KlasiOsiguruvanje" disabled>
                                            <option value="">-- Изберете класа --</option>
                                        </select>
                                        <input type="hidden" asp-for="Input.KlasiOsiguruvanjeIdKlasa" />
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label class="form-label">Производ</label>
                                        <select class="form-control readonly-select" asp-for="Input.ProduktiIdProizvod" asp-items="Model.Produkti" readonly>
                                            <option value="">-- Изберете производ --</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <label class="form-label">Број на полиса</label>
                                        <input type="text" class="form-control" asp-for="Input.BrojNaPolisa" readonly />
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group mb-3" style="display:none;">
                                        <label class="form-label">Број на понуда</label>
                                        <input type="text" class="form-control" asp-for="Input.BrojNaPonuda" />
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-10 mb-3">
                                    <label class="form-label">Договорувач (пребарувај по МБ, ЕДБ, ЕМБГ, име или назив)</label>
                                    <div class="input-group">
                                        <button type="button" class="btn btn-outline-secondary btn-sm clear-field" data-target="dogovoruvac" style="width: 30px; height: 30px; padding: 0; margin-right: 5px;" disabled>
                                            <i class="fas fa-eraser" style="font-size: 0.7rem;"></i>
                                        </button>
                                        <input type="text" id="dogovoruvacMBSearch" class="form-control" 
                                               autocomplete="off"
                                               placeholder="Пребарувај по МБ, ЕДБ, ЕМБГ, име/назив..."
                                               value="@ViewData["DogovoruvacNaziv"]" readonly />
                                        <input type="hidden" asp-for="Input.KlientiIdDogovoruvac" id="KlientiIdDogovoruvac" />
                                    </div>
                                    <div id="dogovoruvacSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1"
                                         style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                                    </div>
                                    @if (!string.IsNullOrEmpty(Model.DogovoruvacEMNGMB))
                                    {
                                        <div class="mt-2">
                                            <small class="text-muted">ЕМБГ/МБ: <strong>@Model.DogovoruvacEMNGMB</strong></small>
                                        </div>
                                    }
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-10 mb-3">
                                    <label class="form-label">Осигуреник (пребарувај по МБ, ЕДБ, ЕМБГ, име или назив)</label>
                                    <div class="input-group">
                                        <button type="button" class="btn btn-outline-secondary btn-sm clear-field" data-target="osigurenik" style="width: 30px; height: 30px; padding: 0; margin-right: 5px;" disabled>
                                            <i class="fas fa-eraser" style="font-size: 0.7rem;"></i>
                                        </button>
                                        <input type="text" id="osigurenikMBSearch" class="form-control" 
                                               autocomplete="off"
                                               placeholder="Пребарувај по МБ, ЕДБ, ЕМБГ, име/назив..."
                                               value="@ViewData["OsigurenikNaziv"]" readonly />
                                        <input type="hidden" asp-for="Input.KlientiIdOsigurenik" id="KlientiIdOsigurenik" />
                                    </div>
                                    <div id="osigurenikSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1"
                                         style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                                    </div>
                                    @if (!string.IsNullOrEmpty(Model.OsigurenikEMNGMB))
                                    {
                                        <div class="mt-2">
                                            <small class="text-muted">ЕМБГ/МБ: <strong>@Model.OsigurenikEMNGMB</strong></small>
                                        </div>
                                    }
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-10 mb-3">
                                    <label class="form-label">Соработник/вработен (пребарувај по МБ, ЕДБ, ЕМБГ, име или назив)</label>
                                    <div class="input-group">
                                        <button type="button" class="btn btn-outline-secondary btn-sm clear-field" data-target="sorabotnik" style="width: 30px; height: 30px; padding: 0; margin-right: 5px;">
                                            <i class="fas fa-eraser" style="font-size: 0.7rem;"></i>
                                        </button>
                                        <input type="text" id="sorabotnikMBSearch" class="form-control" 
                                               autocomplete="off"
                                               placeholder="Пребарувај по МБ, ЕДБ, ЕМБГ, име/назив..."
                                               value="@ViewData["SorabotnikNaziv"]" />
                                        <input type="hidden" asp-for="Input.KlientiIdSorabotnik" id="KlientiIdSorabotnik" />
                                    </div>
                                    <div id="sorabotnikSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1" 
                                         style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <label class="form-label">Датум важи од</label>
                                        <input type="date" class="form-control" asp-for="Input.DatumVaziOd" readonly />
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <label class="form-label">Датум важи до</label>
                                        <input type="date" class="form-control" asp-for="Input.DatumVaziDo" readonly />
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <label class="form-label">Датум на издавање</label>
                                        <input type="date" class="form-control" asp-for="Input.DatumNaIzdavanje" readonly />
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <label class="form-label">Времетраење на полиса (месеци)</label>
                                        <input type="number" class="form-control" asp-for="Input.VremetraenjeNaPolisa" />
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <label class="form-label">Валута</label>
                                        <select class="form-control" asp-for="Input.SifrarnikValutiIdValuta" asp-items="Model.Valuti" disabled>
                                            <option value="">-- Изберете валута --</option>
                                        </select>
                                        <input type="hidden" asp-for="Input.SifrarnikValutiIdValuta" />
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <label class="form-label">Начин на плаќање</label>
                                        <select class="form-control" asp-for="Input.SifrarnikNacinNaPlakjanjeId" asp-items="Model.NaciniNaPlakjanje" disabled>
                                            <option value="">-- Изберете начин на плаќање --</option>
                                        </select>
                                        <input type="hidden" asp-for="Input.SifrarnikNacinNaPlakjanjeId" />
                                    </div>
                                </div>
                                <div class="col-md-3 d-none">
                                    <div class="form-group mb-3">
                                        <label class="form-label">Тип на плаќање</label>
                                        <select class="form-control" asp-for="Input.SifrarnikTipNaPlakanjeId" asp-items="Model.TipoviNaPlakanje">
                                            <option value="">-- Изберете тип на плаќање --</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3 d-none">
                                    <div class="form-group mb-3">
                                        <label class="form-label">Банка</label>
                                        <select class="form-control" asp-for="Input.SifrarnikBankiIdBanka" asp-items="Model.Banki">
                                            <option value="">-- Изберете банка --</option>
                                        </select>
                                    </div>
                                </div>
                                <!-- Hidden inputs for Тип на плаќање and Банка -->
                                <input type="hidden" asp-for="Input.SifrarnikTipNaPlakanjeId" value="1" />
                                <input type="hidden" asp-for="Input.SifrarnikBankiIdBanka" />
                            </div>

                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <label class="form-label">Период на уплата</label>
                                        <input type="number" class="form-control" asp-for="Input.PeriodNaUplata" />
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <label asp-for="Input.SifrarnikValutiIdFranshizaValuta" class="form-label">Валута за франшиза</label>
                                        <select class="form-control" asp-for="Input.SifrarnikValutiIdFranshizaValuta" asp-items="Model.Valuti">
                                            <option value="">-- Изберете валута --</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <label class="form-label">Процент франшиза</label>
                                        <input type="number" step="0.01" class="form-control" asp-for="Input.ProcentFranshiza" />
                                    </div>
                                </div>

                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.FranshizaIznos" class="form-label">Износ на франшиза</label>
                            <input asp-for="Input.FranshizaIznos" class="form-control" type="number" step="0.0001" />
                            <span asp-validation-for="Input.FranshizaIznos" class="text-danger"></span>
                        </div>

                                <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <label class="form-label">Процент финансиски</label>
                                        <input type="number" step="0.01" class="form-control" asp-for="Input.ProcentFinansiski" />
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <label class="form-label">Корегирана стапка на провизија</label>
                                        <input type="number" step="0.01" class="form-control" asp-for="Input.KoregiranaStapkaNaProvizija" />
                                    </div>
                                </div>
                            </div>

                            <div class="row" style="display: none;">
                                <div class="col-md-12">
                                    <div class="form-group mb-3">
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input" asp-for="Input.Kolektivna" id="kolektivna" readonly onclick="return false;" />
                                            <label class="custom-control-label" for="kolektivna">Колективна</label>
                                        </div>
                                    </div>
                                    <div class="form-group mb-3">
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input" asp-for="Input.KolektivnaNeodredenBrOsigurenici" id="kolektivnaNeodredenBr" readonly onclick="return false;" />
                                            <label class="custom-control-label" for="kolektivnaNeodredenBr">Колективна со неодреден број на осигуреници</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">Рок на плаќање фактура влезна</label>
                                    <input type="date" class="form-control" asp-for="Input.RokNaPlakjanjeFakturaVlezna" readonly />
                                    <input type="hidden" asp-for="Input.RokNaPlakjanjeFakturaVlezna" />
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">Број на фактура влезна</label>
                                    <input type="text" class="form-control" asp-for="Input.BrojNaFakturaVlezna" readonly />
                                    <input type="hidden" asp-for="Input.BrojNaFakturaVlezna" />
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">Тип на фактура</label>
                                    <select asp-for="Input.TipNaFaktura" 
                                            asp-items="Model.TipoviNaFaktura" 
                                            class="form-select" 
                                            disabled>
                                        <option value="">-- Избери тип на фактура --</option>
                                    </select>
                                    <input type="hidden" asp-for="Input.TipNaFaktura" />
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">Датум на фактура влезна</label>
                                    <input type="date" class="form-control" asp-for="Input.DatumNaFakturaVlezna" readonly />
                                    <input type="hidden" asp-for="Input.DatumNaFakturaVlezna" />
                                </div>
                                <div style="display: @(Model.Input.GeneriranaFakturaIzlezna ? "block" : "none")">
                                <div class="col-md-4 mb-3">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" asp-for="Input.GeneriranaFakturaIzlezna" onclick="return false;" />
                                        <input type="hidden" asp-for="Input.GeneriranaFakturaIzlezna" />
                                        <label class="form-check-label">Генерирана фактура излезна</label>
                                    </div>
                                </div>
                                </div>
                                <div class="col-md-4 mb-3" style="display: @(Model.Input.GeneriranaFakturaIzlezna ? "block" : "none")">
                                    <label class="form-label">Број на фактура излезна</label>
                                    <input type="text" class="form-control" asp-for="Input.BrojNaFakturaIzlezna" readonly />
                                </div>
                            </div> 

                            <div class="row">
                                <div class="col-md-4 mb-3" style="display: @(Model.Input.GeneriranaFakturaIzlezna ? "block" : "none")">
                                    <label class="form-label">Датум на излезна фактура</label>
                                    <input type="date" class="form-control" asp-for="Input.DatumNaIzleznaFaktura" readonly />
                                </div>
                                <div class="col-md-4 mb-3" style="display: @(Model.Input.GeneriranaFakturaIzlezna ? "block" : "none")">
                                    <label class="form-label">Рок на плаќање фактура излезна</label>
                                    <input type="date" class="form-control" asp-for="Input.RokNaPlakjanjeFakturaIzlezna" readonly />
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" asp-for="Input.Storno" disabled="@(!Model.HasStornoAccess)" />
                                        <label class="form-check-label">Сторно</label>
                                        <input type="hidden" asp-for="Input.Storno" />
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label class="form-label">Причина за сторно</label>
                                    <textarea class="form-control" asp-for="Input.PricinaZaStorno" rows="3"></textarea>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12" style="display:none;">
                                    <div class="form-group mb-3">
                                        <label class="form-label">Забелешка за неодреден број на осигуреници</label>
                                        <textarea class="form-control" asp-for="Input.NeodredenBrOsigureniciZabeleska" rows="3" readonly></textarea>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group mb-3">
                                        <label class="form-label">Забелешка</label>
                                        <textarea class="form-control" asp-for="Input.Zabeleska" rows="3"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Други осигурувања на имот Card -->
                <div class="card mb-3">
                    <div class="card-header" role="button" data-bs-toggle="collapse" data-bs-target="#drugiOsiguruvanjaInfo" aria-expanded="false" aria-controls="drugiOsiguruvanjaInfo" style="cursor: pointer;">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Други осигурувања на имот</h5>
                            <i class="bi bi-chevron-down"></i>
                        </div>
                    </div>
                    <div class="collapse show" id="drugiOsiguruvanjaInfo">
                        <div class="card-body">                           
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label asp-for="Klasa6.OsigurenaVrednost" class="form-label">Осигурена вредност</label>
                                    <input type="number" step="0.0001" class="form-control" asp-for="Klasa6.OsigurenaVrednost" readonly />
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label asp-for="Klasa6.Premija" class="form-label">Премија</label>
                                    <input type="number" step="0.0001" class="form-control" asp-for="Klasa6.Premija" readonly />
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label asp-for="Klasa6.DopolnitelniRiziciOpis" class="form-label">Дополнителни ризици - Опис</label>
                                    <input type="text" class="form-control" asp-for="Klasa6.DopolnitelniRiziciOpis" readonly />
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label asp-for="Klasa6.DopolnitelniRiziciPremija" class="form-label">Дополнителни ризици - Премија</label>
                                    <input type="number" step="0.0001" class="form-control" asp-for="Klasa6.DopolnitelniRiziciPremija" readonly />
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label asp-for="Klasa6.Opis" class="form-label">Опис</label>
                                    <input type="text" class="form-control" asp-for="Klasa6.Opis" readonly />
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label asp-for="Klasa6.RegistarskaOznaka" class="form-label">Регистарска ознака</label>
                                    <input type="text" class="form-control" asp-for="Klasa6.RegistarskaOznaka" readonly />
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label asp-for="Klasa6.Marka" class="form-label">Марка</label>
                                    <input type="text" class="form-control" asp-for="Klasa6.Marka" readonly />
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label asp-for="Klasa6.Tip" class="form-label">Тип</label>
                                    <input type="text" class="form-control" asp-for="Klasa6.Tip" readonly />
                                </div>                               
                                <div class="col-md-4 mb-3">
                                    <label asp-for="Klasa6.BrojNaSasija" class="form-label">Број на Шасија</label>
                                    <input type="text" class="form-control" asp-for="Klasa6.BrojNaSasija" readonly />
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label asp-for="Klasa6.GodinaNaProizvodstvo" class="form-label">Година на производство</label>
                                    <input type="number" step="0.0001" class="form-control" asp-for="Klasa6.GodinaNaProizvodstvo" readonly />
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label asp-for="Klasa6.Motor" class="form-label">Мотор</label>
                                    <input type="text" class="form-control" asp-for="Klasa6.Motor" readonly />
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label asp-for="Klasa6.VkupnaPremija" class="form-label">Вкупна премија</label>
                                    <input type="number" step="0.0001" class="form-control" asp-for="Klasa6.VkupnaPremija" readonly />
                                </div>
                             
                            </div>

                          
                        </div>
                    </div>
                </div>

                <!-- Discounts Card -->
                <div class="card mb-3">
                    <div class="card-header" role="button" data-bs-toggle="collapse" data-bs-target="#discountsInfo" aria-expanded="false" aria-controls="discountsInfo" style="cursor: pointer;">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Попусти</h5>
                            <i class="bi bi-chevron-down"></i>
                        </div>
                    </div>
                    <div class="collapse show" id="discountsInfo">
                        <div class="card-body">
                        <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label asp-for="Klasa6.ProcentNaPopustZaFakturaVoRok" class="form-label">Процент на попуст за фактура во рок</label>
                                    <input type="number" step="0.01" class="form-control" asp-for="Klasa6.ProcentNaPopustZaFakturaVoRok" readonly />
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label asp-for="Klasa6.IznosZaPlakjanjeVoRok" class="form-label">Износ за плаќање во рок</label>
                                    <input type="number" step="0.0001" class="form-control" asp-for="Klasa6.IznosZaPlakjanjeVoRok" readonly />
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label asp-for="Klasa6.ProcentKomercijalenPopust" class="form-label">Процент комерцијален попуст</label>
                                    <input type="number" step="0.01" class="form-control" asp-for="Klasa6.ProcentKomercijalenPopust" readonly />
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label asp-for="Klasa6.ProcentFinansiski" class="form-label">Процент финансиски</label>
                                    <input type="number" step="0.01" class="form-control" asp-for="Klasa6.ProcentFinansiski" readonly />
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label asp-for="Klasa6.PremijaZaNaplata" class="form-label">Премија за наплата</label>
                                    <input type="number" step="0.0001" class="form-control" asp-for="Klasa6.PremijaZaNaplata" readonly />
                                </div>
                        </div>                        
                            
                        </div>
                    </div>
                </div> 

                <!-- Collective Insureds Card -->
                <div class="card mb-4" id="collectiveInsuredsSection" style="display: none;">
                    <div class="card-header" role="button" data-bs-toggle="collapse" data-bs-target="#collectiveInsureds" aria-expanded="false" aria-controls="collectiveInsureds" style="cursor: pointer;">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Листа Осигуреници колективно</h5>
                            <i class="bi bi-chevron-down"></i>
                        </div>
                    </div>
                    <div class="collapse show" id="collectiveInsureds">
                        <div class="card-body">
                            @if (Model.HasAdminAccess)
                            {
                                <div class="mb-3">
                                    <a href="/Polisi/EditOsigureniciKolektivno/@Model.Input.Id" class="btn btn-primary">
                                        <i class="bi bi-pencil"></i> Менаџирај осигуреници
                                    </a>
                                </div>
                            }
                            @if (Model.OsigureniciKolektivno != null && Model.OsigureniciKolektivno.Any())
                            {
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Име</th>
                                                <th>Презиме</th>
                                                <th>ЕМБГ</th>
                                                <th>Број на лична карта</th>
                                                <th>Број на пасош</th>
                                                <th>Општина</th>
                                                <th>Адреса</th>
                                                <th>Број</th>
                                                <th>Телефон</th>
                                                <th>Email</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var osigurenik in Model.OsigureniciKolektivno)
                                            {
                                                <tr>
                                                    <td>@osigurenik.Id</td>
                                                    <td>@osigurenik.Ime</td>
                                                    <td>@osigurenik.Prezime</td>
                                                    <td>@osigurenik.EMBG</td>
                                                    <td>@osigurenik.BrojNaLicnaKarta</td>
                                                    <td>@osigurenik.BrojNaPasos</td>
                                                    <td>@osigurenik.OpstinaNaziv</td>
                                                    <td>@osigurenik.Adresa</td>
                                                    <td>@osigurenik.Broj</td>
                                                    <td>@osigurenik.Telefon</td>
                                                    <td>@osigurenik.Email</td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                            else
                            {
                                <p class="text-muted mb-0">Нема внесени осигуреници.</p>
                            }
                        </div>
                    </div>
                </div>


                        <!-- Kartica Card -->
        <div class="card mb-4">
            <div class="card-header" role="button" data-bs-toggle="collapse" data-bs-target="#karticaInfo" aria-expanded="false" aria-controls="karticaInfo" style="cursor: pointer;">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Картица</h5>
                    <i class="bi bi-chevron-down"></i>
                </div>
            </div>
            <div class="collapse show" id="karticaInfo">
                <div class="card-body">
                    @if (Model.KarticaData != null && Model.KarticaData.Rows.Count > 0)
                    {
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        @foreach (System.Data.DataColumn col in Model.KarticaData.Columns)
                                        {
                                            <th>@col.ColumnName</th>
                                        }
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (System.Data.DataRow row in Model.KarticaData.Rows)
                                    {
                                        <tr>
                                            <td>@row["ID"]</td>
                                            <td>@row["Рата број"]</td>
                                            <td>@(row["Датум на доспевање"] != DBNull.Value ? Convert.ToDateTime(row["Датум на доспевање"]).ToString("dd.MM.yyyy") : "")</td>
                                            <td class="text-end">@(row["Износ на рата"] != DBNull.Value ? Convert.ToDecimal(row["Износ на рата"]).ToString("N2") : "")</td>
                                            <td>@(row["Датум на уплата"] != DBNull.Value ? Convert.ToDateTime(row["Датум на уплата"]).ToString("dd.MM.yyyy") : "")</td>
                                            <td class="text-end">@(row["Уплатен износ"] != DBNull.Value ? Convert.ToDecimal(row["Уплатен износ"]).ToString("N2") : "")</td>
                                            <td class="text-center">
                                                @if (row["Затворена рата"] != DBNull.Value && Convert.ToBoolean(row["Затворена рата"]))
                                                {
                                                    @:Да
                                                }
                                            </td>
                                            <td class="text-center">
                                                @if (row["Сторно"] != DBNull.Value && Convert.ToBoolean(row["Сторно"]))
                                                {
                                                    @:Да
                                                }
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <p class="text-muted mb-0">Нема податоци во картица.</p>
                    }

                    <!-- OZ Information Section -->
                    @if (Model.OZIznosIzleznaFakturaPremija != 0 || Model.OZIznosPolisa != 0)
                    {
                        <hr class="my-4">
                        <h6 class="text-primary mb-3">Одобрување / Задолжување информации</h6>
                        <div class="row">
                            @if (Model.OZIznosIzleznaFakturaPremija != 0)
                            {
                                <div class="col-12 mb-2">
                                    <div class="alert alert-info mb-2">
                                        <strong>За фактурата од оваа полиса е пресметано одобрување / задолжување со износ од:</strong> 
                                        <span class="fw-bold">@Model.OZIznosIzleznaFakturaPremija.ToString("N2")</span>
                                    </div>
                                </div>
                            }
                            @if (Model.OZIznosPolisa != 0)
                            {
                                <div class="col-12 mb-2">
                                    <div class="alert alert-warning mb-2">
                                        <strong>За оваа полиса е пресметано одобрување / задолжување со износ од:</strong> 
                                        <span class="fw-bold">@Model.OZIznosPolisa.ToString("N2")</span>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                </div>
            </div>
        </div>

                  <!-- Generiraj izlezna faktura -->
        <div class="card mb-4" style="@(Model.Input.TipNaFaktura == "VleznaFakturaKonKlient" || Model.Input.TipNaFaktura == "Влезна фактура кон клиент" ? "display:none;" : "")">
            <div class="card-header" role="button" data-bs-toggle="collapse" data-bs-target="#izleznaFakturaInfo" aria-expanded="false" aria-controls="izleznaFakturaInfo" style="cursor: pointer;">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Фактури</h5>
                    <i class="bi bi-chevron-down"></i>
                </div>
            <div class="collapse show" id="izleznaFakturaInfo">
                <div class="card-body">
                    <button type="button" class="btn btn-primary" onclick="openGenerirajFakturaPopup(@Model.Input.Id)">
                        <i class="bi bi-file-earmark-text"></i> Менаџирање со излезни фактури кон клиент
                    </button>
                </div>
            </div>
            <script>
                function openGenerirajFakturaPopup(polisaId) {
                    window.open('/Finansii/GenerirajIzleznaFakturaKonKlient?id=' + polisaId, 'GenerirajFaktura', 
                        'width=1024,height=768,resizable=yes,scrollbars=yes,status=yes');
                }
            </script>
            </div>
        </div>

                <!-- Files Card -->
                <div class="card mb-4">
                    <div class="card-header" role="button" data-bs-toggle="collapse" data-bs-target="#filesInfo" aria-expanded="false" aria-controls="filesInfo" style="cursor: pointer;">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Датотеки</h5>
                            <i class="bi bi-chevron-down"></i>
                        </div>
                    </div>
                    <div class="collapse show" id="filesInfo">
                        <div class="card-body">
                            @if (Model.Files != null && Model.Files.Any())
                            {
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Име на документ</th>
                                                <th>Датум на прикачување</th>
                                                <th>Прикачил</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var file in Model.Files)
                                            {
                                                <tr>
                                                    <td>@file.FileName</td>
                                                    <td>@file.DateCreated.ToString("dd.MM.yyyy HH:mm")</td>
                                                    <td>@file.UsernameCreated</td>
                                                    <td>
                                                        <a href="?handler=DownloadFile&fileId=@file.Id" class="btn btn-sm btn-primary">
                                                            <i class="bi bi-download"></i> Преземи
                                                        </a>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                            else
                            {
                                <p class="text-muted mb-0">Нема прикачени документи.</p>
                            }
                        </div>
                    </div>
                </div>


     <!-- Задолжување/Раздолжување Card -->
     <div class="card mb-4">
         <div class="card-header" role="button" data-bs-toggle="collapse" data-bs-target="#zadolzuvanjeInfo" aria-expanded="false" aria-controls="zadolzuvanjeInfo" style="cursor: pointer;">
             <div class="d-flex justify-content-between align-items-center">
                 <h5 class="mb-0">Задолжување/Раздолжување</h5>
                 <i class="bi bi-chevron-down"></i>
             </div>
         </div>
         <div class="collapse show" id="zadolzuvanjeInfo">
             <div class="card-body">
                 @if (Model.ZadolzuvanjeData != null)
                 {
                     <div class="row g-3">
                         <div class="col-md-4">
                             <label class="form-label fw-bold">Задолжен</label>
                             <div>@(Model.ZadolzuvanjeData.Zadolzen ?? "-")</div>
                         </div>
                         <div class="col-md-4">
                             <label class="form-label fw-bold">Датум на задолжување</label>
                             <div>@(Model.ZadolzuvanjeData.DatumNaZadolzuvanje?.ToString("dd.MM.yyyy") ?? "-")</div>
                         </div>
                         <div class="col-md-4">
                             <label class="form-label fw-bold">Основ за раздолжување</label>
                             <div>@(Model.ZadolzuvanjeData.OsnovZaRazdolzuvanje ?? "-")</div>
                         </div>
                         <div class="col-md-3">
                             <label class="form-label fw-bold">Потврдено кај брокер</label>
                             <div>@(Model.ZadolzuvanjeData.PotvrdenoRazdolzuvanjeKajBroker ? "Да" : "Не")</div>
                         </div>
                         <div class="col-md-3">
                             <label class="form-label fw-bold">Датум на раздолжување кај брокер</label>
                             <div>@(Model.ZadolzuvanjeData.DatumNaRazdolzuvanjeKajBroker?.ToString("dd.MM.yyyy") ?? "-")</div>
                         </div>
                         <div class="col-md-3">
                             <label class="form-label fw-bold">Потврдено кај осигурител</label>
                             <div>@(Model.ZadolzuvanjeData.PotvrdenoRazdolzuvanjeKajOsiguritel ? "Да" : "Не")</div>
                         </div>
                         <div class="col-md-3">
                             <label class="form-label fw-bold">Датум на раздолжување кај осигурител</label>
                             <div>@(Model.ZadolzuvanjeData.DatumNaRazdolzuvanjeKajOsiguritel?.ToString("dd.MM.yyyy") ?? "-")</div>
                         </div>
                     </div>
                 }
                 else
                 {
                     <div class="alert alert-info mb-0">Нема податоци за задолжување/раздолжување</div>
                 }
             </div>
         </div>
     </div>



                <div class="row mt-3">
                    <div class="col-md-12">
                        @if (Model.HasAdminAccess)
                        {
                            <button type="submit" class="btn btn-primary">Зачувај</button>
                        }
                        <!-- Add the Upload Files button -->
                        @if (Model.Input.Id > 0 && Model.HasAdminAccess)
                        {
                            <a href="/Polisi/PolisiFileUpload/@Model.Input.Id" class="btn btn-secondary ms-2">
                                <i class="bi bi-upload"></i> Прикачи документи
                            </a>
                        }
                        <a href="javascript:history.back()" class="btn btn-secondary">Назад</a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    <style>
        .search-results .list-group-item.text-center {
            padding: 1rem;
        }
        .search-results .list-group-item.text-center p {
            color: #6c757d;
            margin-bottom: 0.75rem;
        }
        .search-results .btn-primary {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
        }
    </style>
    <script>
        $(document).ready(function() {
            // Auto-hide success message after 10 seconds
            const successMessage = $('.alert-success');
            if (successMessage.length > 0) {
                setTimeout(function() {
                    successMessage.fadeOut('slow');
                }, 10000);
            }

            // Function to create search functionality
            function createSearchFunctionality(searchInputId, resultsContainerId, hiddenInputId, searchHandler = 'SearchKlienti') {
                let searchTimeout;
                const antiForgeryToken = $('input[name="__RequestVerificationToken"]').val();

                $(`#${searchInputId}`).on('input', function() {
                    clearTimeout(searchTimeout);
                    const searchTerm = $(this).val();
                    const resultsDiv = $(`#${resultsContainerId}`);

                    if (searchTerm.length < 1) {
                        resultsDiv.hide();
                        return;
                    }

                    searchTimeout = setTimeout(function() {
                        $.ajax({
                            url: `?handler=${searchHandler}`,
                            type: 'GET',
                            data: { mb: searchTerm },
                            headers: {
                                "RequestVerificationToken": antiForgeryToken
                            },
                            success: function(data) {
                                resultsDiv.empty();
                                if (data.length > 0) {
                                    const list = $('<div class="list-group"></div>');
                                    data.forEach(function(item) {
                                        const listItem = $(`
                                            <div class="list-group-item list-group-item-action" style="cursor: pointer;">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <div>
                                                        <strong>${item.naziv}</strong><br>
                                                        <small>
                                                            ${item.mb ? 'МБ: ' + item.mb : ''}
                                                            ${item.edb ? ' ЕДБ: ' + item.edb : ''}
                                                            ${item.embg ? ' ЕМБГ: ' + item.embg : ''}
                                                        </small>
                                                    </div>
                                                    <button class="btn btn-sm btn-primary">Избери</button>
                                                </div>
                                            </div>
                                        `);

                                        listItem.on('click', function() {
                                            $(`#${hiddenInputId}`).val(item.id);
                                            $(`#${searchInputId}`).val(item.naziv);
                                            resultsDiv.hide();
                                        });

                                        list.append(listItem);
                                    });
                                    resultsDiv.append(list);
                                    resultsDiv.show();
                                } else {
                                    resultsDiv.hide();
                                }
                            }
                        });
                    }, 300);
                });

                // Hide results when clicking outside
                $(document).on('click', function(e) {
                    if (!$(e.target).closest(`#${searchInputId}, #${resultsContainerId}`).length) {
                        $(`#${resultsContainerId}`).hide();
                    }
                });
            }

            // Initialize search functionality for each field
            createSearchFunctionality('dogovoruvacMBSearch', 'dogovoruvacSearchResults', 'KlientiIdDogovoruvac');
            createSearchFunctionality('osigurenikMBSearch', 'osigurenikSearchResults', 'KlientiIdOsigurenik');
            createSearchFunctionality('sorabotnikMBSearch', 'sorabotnikSearchResults', 'KlientiIdSorabotnik', 'SearchSorabotnici');

            // Add click handlers for clear buttons
            $('.clear-field').on('click', function() {
                const target = $(this).data('target');
                if (target === 'dogovoruvac') {
                    $('#dogovoruvacMBSearch').val('');
                    $('#KlientiIdDogovoruvac').val('');
                    $('#dogovoruvacSearchResults').hide();
                } else if (target === 'osigurenik') {
                    $('#osigurenikMBSearch').val('');
                    $('#KlientiIdOsigurenik').val('');
                    $('#osigurenikSearchResults').hide();
                } else if (target === 'sorabotnik') {
                    $('#sorabotnikMBSearch').val('');
                    $('#KlientiIdSorabotnik').val('');
                    $('#sorabotnikSearchResults').hide();
                }
            });

/*
            // Show/hide collective insureds section based on checkbox
            function toggleCollectiveSection() {
                if ($('#kolektivna').is(':checked') && !$('#kolektivnaNeodredenBr').is(':checked')) {
                    $('#collectiveInsuredsSection').show();
                } else {
                    $('#collectiveInsuredsSection').hide();
                }
            }

            // Initial check
            toggleCollectiveSection();
*/

            // Add event listener for both checkbox changes
            $('#kolektivna, #kolektivnaNeodredenBr').change(toggleCollectiveSection);
        });
    </script>

    <!-- Add collapse functionality in a separate script block -->
    <script>
        $(document).ready(function() {
            let isAllCollapsed = false;
            const collapseAllBtn = $('#collapseAllBtn');
            const sections = $('.collapse');
            const icons = $('[data-bs-toggle="collapse"] .bi');

            collapseAllBtn.click(function() {
                isAllCollapsed = !isAllCollapsed;
                if (isAllCollapsed) {
                    sections.collapse('hide');
                    icons.css('transform', '');
                    $(this).html('<i class="bi bi-chevron-down"></i> Отвори ги сите секции');
                } else {
                    sections.collapse('show');
                    icons.css('transform', 'rotate(180deg)');
                    $(this).html('<i class="bi bi-chevron-up"></i> Затвори ги сите секции');
                }
            });

            // Set initial rotation for all collapse icons
            document.querySelectorAll('[data-bs-toggle="collapse"]').forEach(header => {
                const icon = header.querySelector('.bi');
                icon.style.transform = 'rotate(180deg)';
                
                header.addEventListener('click', function() {
                    const icon = this.querySelector('.bi');
                    icon.style.transform = icon.style.transform === 'rotate(180deg)' ? '' : 'rotate(180deg)';
                });
            });
        });
    </script>

      <script type="text/javascript">
        // Function to validate dates
        function validateDates() {
            const vaziOd = $('#Input_DatumVaziOd').val();
            const vaziDo = $('#Input_DatumVaziDo').val();
            
            if (vaziOd && vaziDo) {
                const dateVaziOd = new Date(vaziOd);
                const dateVaziDo = new Date(vaziDo);
                
                if (dateVaziDo < dateVaziOd) {
                    // Add error message if it doesn't exist
                    if (!$('#vaziDoError').length) {
                        $('#Input_DatumVaziDo').before('<span id="vaziDoError" class="text-danger">Датумот "Важи до" не може да биде пред "Важи од"</span>');
                    }
                    // Set VaziDo to VaziOd
                    $('#Input_DatumVaziDo').val(vaziOd);
                    return false;
                } else {
                    // Remove error message if exists
                    $('#vaziDoError').remove();
                    return true;
                }
            }
            return true;
        }

        // Add event listeners for date fields
        $('#Input_DatumVaziOd, #Input_DatumVaziDo').on('change', function() {
            validateDates();
        });

        // Run initial validation
        $(document).ready(function() {
            validateDates();
        });
    </script>
} 