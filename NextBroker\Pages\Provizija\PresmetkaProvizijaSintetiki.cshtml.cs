using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System.Data;
using Microsoft.Data.SqlClient;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using System.Text.Json;
using Microsoft.AspNetCore.Authorization;

namespace NextBroker.Pages.Provizija
{
    public class PresmetkaProvizijaSintetikiModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;

        public PresmetkaProvizijaSintetikiModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        // Properties to hold the batch data
        public DataTable BatchData { get; set; } = new DataTable();

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("PresmetkaProvizijaSintetiki"))
            {
                return RedirectToAccessDenied();
            }

            // Load batch data
            await LoadBatchData();

            return Page();
        }

        private async Task LoadBatchData()
        {
            try
            {
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    string query = @"
                        SELECT
                            -- Extract BatchId (middle number between slashes)
                            SUBSTRING(
                                BrojNaSpecifikacijaProvizija,
                                CHARINDEX('/', BrojNaSpecifikacijaProvizija) + 1,
                                CHARINDEX('/', BrojNaSpecifikacijaProvizija, CHARINDEX('/', BrojNaSpecifikacijaProvizija) + 1) - CHARINDEX('/', BrojNaSpecifikacijaProvizija) - 1
                            ) AS BatchId,
                            MIN(BrojNaSpecifikacijaProvizija) AS BrojNaSpecifikacijaProvizija,
                            MIN(SelektiranaPresmetkaOd) AS SelektiranaPresmetkaOd,
                            MIN(SelektiranaPresmetkaDo) AS SelektiranaPresmetkaDo,
                            MIN(DateCreated) AS DateCreated
                        FROM dbo.ProvizijaPresmetkaStavki
                        WHERE BrojNaSpecifikacijaProvizija IS NOT NULL
                            AND CHARINDEX('/', BrojNaSpecifikacijaProvizija) > 0
                            AND CHARINDEX('/', BrojNaSpecifikacijaProvizija, CHARINDEX('/', BrojNaSpecifikacijaProvizija) + 1) > 0
                        GROUP BY
                            SUBSTRING(
                                BrojNaSpecifikacijaProvizija,
                                CHARINDEX('/', BrojNaSpecifikacijaProvizija) + 1,
                                CHARINDEX('/', BrojNaSpecifikacijaProvizija, CHARINDEX('/', BrojNaSpecifikacijaProvizija) + 1) - CHARINDEX('/', BrojNaSpecifikacijaProvizija) - 1
                            )
                        ORDER BY MIN(DateCreated) DESC";

                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        using (SqlDataAdapter adapter = new SqlDataAdapter(command))
                        {
                            adapter.Fill(BatchData);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log error or handle as needed
                Console.WriteLine($"Error loading batch data: {ex.Message}");
                BatchData = new DataTable(); // Ensure we have an empty table on error
            }
        }

        public async Task<IActionResult> OnGetExportBatch(DateTime datumOd, DateTime datumDo)
        {
            if (!await HasPageAccess("PresmetkaProvizijaSintetiki"))
            {
                return RedirectToAccessDenied();
            }

            try
            {
                // Get detailed commission data for the specific date range
                DataTable reportData = await GetBatchReportData(datumOd, datumDo);

                // Generate Excel file
                using (var package = new ExcelPackage())
                {
                    var worksheet = package.Workbook.Worksheets.Add("Провизија детали");

                    // Set up the header
                    int currentRow = 1;

                    // Title
                    worksheet.Cells["A" + currentRow].Value = "Детален извештај за провизија";
                    worksheet.Cells["A" + currentRow].Style.Font.Size = 16;
                    worksheet.Cells["A" + currentRow].Style.Font.Bold = true;
                    currentRow += 2;

                    // Date range
                    worksheet.Cells["A" + currentRow].Value = $"Период: {datumOd:dd.MM.yyyy} - {datumDo:dd.MM.yyyy}";
                    worksheet.Cells["A" + currentRow].Style.Font.Bold = true;
                    currentRow++;

                    // Record count
                    worksheet.Cells["A" + currentRow].Value = $"Вкупно записи: {reportData.Rows.Count}";
                    worksheet.Cells["A" + currentRow].Style.Font.Bold = true;
                    currentRow += 2;

                    if (reportData.Rows.Count > 0)
                    {
                        // Table headers
                        int tableStartRow = currentRow;
                        string[] headers = {
                            "Број на спецификација",
                            "Примач на провизија",
                            "ЕМБГ/МБ примач",
                            "Договорувач",
                            "ЕМБГ/МБ договорувач",
                            "Осигурител",
                            "Број на полиса",
                            "Класа осигурување",
                            "Продукт",
                            "Период од",
                            "Период до",
                            "Датум на пресметка",
                            "Начин на плаќање",
                            "Година",
                            "Начин на пресметка",
                            "Износ за пресметка",
                            "Датум на премија",
                            "Број на влезна фактура",
                            "Бруто провизија",
                            "Процент данок",
                            "Нето провизија",
                            "Стапка за провизија",
                            "Соработник прво ниво",
                            "Надреден ставка"
                        };

                        for (int i = 0; i < headers.Length; i++)
                        {
                            worksheet.Cells[currentRow, i + 1].Value = headers[i];
                            worksheet.Cells[currentRow, i + 1].Style.Font.Bold = true;
                            worksheet.Cells[currentRow, i + 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
                            worksheet.Cells[currentRow, i + 1].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                        }
                        currentRow++;

                        // Data rows
                        foreach (DataRow row in reportData.Rows)
                        {
                            worksheet.Cells[currentRow, 1].Value = row["BrojNaSpecifikacijaProvizija"]?.ToString();
                            worksheet.Cells[currentRow, 2].Value = row["PrimacProvizija"]?.ToString();
                            worksheet.Cells[currentRow, 3].Value = row["EMBGMBPrimacProvizija"]?.ToString();
                            worksheet.Cells[currentRow, 4].Value = row["DogovoruvacImePrezime"]?.ToString();
                            worksheet.Cells[currentRow, 5].Value = row["EMBGMBKlientProvizija"]?.ToString();
                            worksheet.Cells[currentRow, 6].Value = row["OsiguritelNaziv"]?.ToString();
                            worksheet.Cells[currentRow, 7].Value = row["BrojNaPolisa"]?.ToString();
                            worksheet.Cells[currentRow, 8].Value = row["KlasaImeOsiguruvanje"]?.ToString();
                            worksheet.Cells[currentRow, 9].Value = row["ProduktIme"]?.ToString();

                            if (row["DatumNaPresmetkaOd"] != DBNull.Value)
                                worksheet.Cells[currentRow, 10].Value = Convert.ToDateTime(row["DatumNaPresmetkaOd"]).ToString("dd.MM.yyyy");
                            if (row["DatumNaPresmetkaDo"] != DBNull.Value)
                                worksheet.Cells[currentRow, 11].Value = Convert.ToDateTime(row["DatumNaPresmetkaDo"]).ToString("dd.MM.yyyy");
                            if (row["DatumNaPresmetkaNaPremija"] != DBNull.Value)
                                worksheet.Cells[currentRow, 12].Value = Convert.ToDateTime(row["DatumNaPresmetkaNaPremija"]).ToString("dd.MM.yyyy");

                            worksheet.Cells[currentRow, 13].Value = row["SifrarnikNacinPlakanjeId"]?.ToString();
                            worksheet.Cells[currentRow, 14].Value = row["Godina"]?.ToString();
                            worksheet.Cells[currentRow, 15].Value = row["NacinNaPresmetka"]?.ToString();

                            if (row["IznosZaPresmetka"] != DBNull.Value)
                                worksheet.Cells[currentRow, 16].Value = Convert.ToDecimal(row["IznosZaPresmetka"]);
                            if (row["DatumNaPremijaZaPresmetka"] != DBNull.Value)
                                worksheet.Cells[currentRow, 17].Value = Convert.ToDateTime(row["DatumNaPremijaZaPresmetka"]).ToString("dd.MM.yyyy");

                            worksheet.Cells[currentRow, 18].Value = row["BrojNaVleznaFaktura"]?.ToString();

                            if (row["IznosProvizijaBruto"] != DBNull.Value)
                                worksheet.Cells[currentRow, 19].Value = Convert.ToDecimal(row["IznosProvizijaBruto"]);
                            if (row["ProcentDanok"] != DBNull.Value)
                                worksheet.Cells[currentRow, 20].Value = Convert.ToDecimal(row["ProcentDanok"]);
                            if (row["IznosProvizijaNeto"] != DBNull.Value)
                                worksheet.Cells[currentRow, 21].Value = Convert.ToDecimal(row["IznosProvizijaNeto"]);
                            if (row["StapkaZaProvizija"] != DBNull.Value)
                                worksheet.Cells[currentRow, 22].Value = Convert.ToDecimal(row["StapkaZaProvizija"]);

                            worksheet.Cells[currentRow, 23].Value = row["SorabotnikPrvoNivo"]?.ToString();
                            worksheet.Cells[currentRow, 24].Value = row["NadredenStavka"]?.ToString();

                            currentRow++;
                        }

                        // Auto-fit columns
                        worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();

                        // Add borders to the table
                        var tableRange = worksheet.Cells[tableStartRow, 1, currentRow - 1, headers.Length];
                        tableRange.Style.Border.Top.Style = ExcelBorderStyle.Thin;
                        tableRange.Style.Border.Left.Style = ExcelBorderStyle.Thin;
                        tableRange.Style.Border.Right.Style = ExcelBorderStyle.Thin;
                        tableRange.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                    }
                    else
                    {
                        worksheet.Cells["A" + currentRow].Value = "Нема податоци за прикажување.";
                        worksheet.Cells["A" + currentRow].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    }

                    // Generate file
                    var content = package.GetAsByteArray();
                    var fileName = $"Provizija_Batch_{datumOd:yyyy-MM-dd}_{datumDo:yyyy-MM-dd}.xlsx";

                    return File(content, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error generating Excel export: {ex.Message}");
                TempData["ErrorMessage"] = "Грешка при генерирање на Excel фајлот.";
                return RedirectToPage();
            }
        }

        private async Task<DataTable> GetBatchReportData(DateTime datumOd, DateTime datumDo)
        {
            DataTable reportData = new DataTable();

            try
            {
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    // Same query as Commission Report but filter by exact SelektiranaPresmetkaOd/Do dates
                    string query = @"
                        SELECT
                            pps.BrojNaSpecifikacijaProvizija,
                            pps.KlientProvizijaId,
                            CASE
                                WHEN klient_primac.Naziv IS NOT NULL THEN klient_primac.Naziv
                                ELSE CONCAT(klient_primac.Ime, ' ', klient_primac.Prezime)
                            END AS PrimacProvizija,
                            pps.EMBGMBPrimacProvizija,
                            CASE
                                WHEN dogovoruvac.Naziv IS NOT NULL THEN dogovoruvac.Naziv
                                ELSE CONCAT(dogovoruvac.Ime, ' ', dogovoruvac.Prezime)
                            END AS DogovoruvacImePrezime,
                            pps.EMBGMBKlientProvizija,
                            CASE
                                WHEN osiguritel.Naziv IS NOT NULL THEN osiguritel.Naziv
                                ELSE CONCAT(osiguritel.Ime, ' ', osiguritel.Prezime)
                            END AS OsiguritelNaziv,
                            pps.BrojNaPolisa,
                            klasa.KlasaIme AS KlasaImeOsiguruvanje,
                            produkt.Ime AS ProduktIme,
                            pps.DatumNaPresmetkaOd,
                            pps.DatumNaPresmetkaDo,
                            pps.DateCreated AS DatumNaPresmetkaNaPremija,
                            pps.SifrarnikNacinPlakanjeId,
                            pps.Godina,
                            nacin_presmetka.Nacin AS NacinNaPresmetka,
                            pps.IznosZaPresmetka,
                            pps.DatumNaPremijaZaPresmetka,
                            pps.BrojNaVleznaFaktura,
                            pps.IznosProvizijaBruto,
                            pps.ProcentDanok,
                            pps.IznosProvizijaNeto,
                            pps.StapkaZaProvizija,
                            CASE
                                WHEN sorabotnik_prvo_nivo.Naziv IS NOT NULL THEN sorabotnik_prvo_nivo.Naziv
                                ELSE CONCAT(sorabotnik_prvo_nivo.Ime, ' ', sorabotnik_prvo_nivo.Prezime)
                            END AS SorabotnikPrvoNivo,
                            pps.NadredenStavka
                        FROM
                            dbo.ProvizijaPresmetkaStavki pps
                        LEFT JOIN
                            dbo.Klienti osiguritel ON pps.KlientiIdOsiguritel = osiguritel.Id
                        LEFT JOIN
                            dbo.Klienti klient ON pps.KlientProvizijaId = klient.Id
                        LEFT JOIN
                            dbo.Klienti klient_primac ON pps.KlientiIdKlient = klient_primac.Id
                        LEFT JOIN
                            dbo.KlasiOsiguruvanje klasa ON pps.KlasiOsiguruvanjeIdKlasa = klasa.Id
                        LEFT JOIN
                            dbo.Produkti produkt ON pps.ProizvodId = produkt.Id
                        LEFT JOIN
                            dbo.SifrarnikNacinNaPresmetkaProvizija nacin_presmetka ON pps.SifrarnikNacinNaPresmetkaProvizijaId = nacin_presmetka.Id
                        LEFT JOIN
                            dbo.Klienti sorabotnik_prvo_nivo ON pps.KlientiIdSorabotnikPrvoNivoPolisa = sorabotnik_prvo_nivo.Id
                        LEFT JOIN
                            dbo.Klienti dogovoruvac ON pps.DogovoruvacIdKlient = dogovoruvac.Id
                        WHERE
                            pps.SelektiranaPresmetkaOd = @DatumOd
                            AND pps.SelektiranaPresmetkaDo = @DatumDo
                        ORDER BY
                            pps.DateCreated DESC, pps.Id DESC";

                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@DatumOd", datumOd);
                        command.Parameters.AddWithValue("@DatumDo", datumDo);

                        using (SqlDataAdapter adapter = new SqlDataAdapter(command))
                        {
                            adapter.Fill(reportData);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading batch report data: {ex.Message}");
                reportData = new DataTable(); // Return empty table on error
            }

            return reportData;
        }

        public async Task<IActionResult> OnPostLoadVrabotenData(string datumOd, string datumDo)
        {
            if (!await HasPageAccess("PresmetkaProvizijaSintetiki"))
            {
                return new JsonResult(new { success = false, message = "Немате дозвола за пристап." });
            }

            try
            {
                Console.WriteLine($"Loading vraboten data for dates: {datumOd} to {datumDo}");

                // First check if we have any records for these dates
                if (string.IsNullOrEmpty(datumOd) || string.IsNullOrEmpty(datumDo))
                {
                    return new JsonResult(new { success = false, message = "Датумите не се валидни." });
                }
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    // Get vraboteni rows grouped by EMBGMBPrimacProvizija with summed bruto and neto amounts
                    string query = @"
                        SELECT
                            MAX(pps.BrojNaSpecifikacijaProvizija) AS BrojNaSpecifikacijaProvizija,
                            MAX(pps.SelektiranaPresmetkaOd) AS SelektiranaPresmetkaOd,
                            MAX(pps.SelektiranaPresmetkaDo) AS SelektiranaPresmetkaDo,
                            CASE
                                WHEN MAX(klient.Ime) IS NOT NULL AND MAX(klient.Prezime) IS NOT NULL
                                THEN CONCAT(MAX(klient.Ime), ' ', MAX(klient.Prezime))
                                WHEN MAX(klient.Naziv) IS NOT NULL
                                THEN MAX(klient.Naziv)
                                ELSE 'N/A'
                            END AS PrimacNaProvizija,
                            pps.EMBGMBPrimacProvizija,
                            MAX(klient.DaumNaDogovor) AS DaumNaDogovor,
                            SUM(ISNULL(pps.IznosProvizijaBruto, 0)) AS IznosProvizijaBruto,
                            SUM(ISNULL(pps.IznosProvizijaNeto, 0)) AS IznosProvizijaNeto
                        FROM dbo.ProvizijaPresmetkaStavki pps
                        LEFT JOIN dbo.Klienti klient ON pps.EMBGMBPrimacProvizija = klient.EMBG OR pps.EMBGMBPrimacProvizija = klient.MB
                        WHERE pps.SelektiranaPresmetkaOd = @DatumOd
                            AND pps.SelektiranaPresmetkaDo = @DatumDo
                            AND pps.BrojNaSpecifikacijaProvizija LIKE '2/%'
                        GROUP BY pps.EMBGMBPrimacProvizija
                        ORDER BY PrimacNaProvizija";

                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        try
                        {
                            command.Parameters.AddWithValue("@DatumOd", DateTime.Parse(datumOd));
                            command.Parameters.AddWithValue("@DatumDo", DateTime.Parse(datumDo));

                            Console.WriteLine($"Executing query with parameters: DatumOd={datumOd}, DatumDo={datumDo}");

                            var vrabotenList = new List<object>();
                            using (SqlDataReader reader = await command.ExecuteReaderAsync())
                            {
                                int recordCount = 0;
                                while (await reader.ReadAsync())
                                {
                                    recordCount++;
                                    vrabotenList.Add(new
                                    {
                                        brojNaSpecifikacija = reader["BrojNaSpecifikacijaProvizija"]?.ToString() ?? "",
                                        selektiranaPresmetkaOd = reader["SelektiranaPresmetkaOd"] != DBNull.Value
                                            ? Convert.ToDateTime(reader["SelektiranaPresmetkaOd"]).ToString("yyyy-MM-dd")
                                            : "",
                                        selektiranaPresmetkaDo = reader["SelektiranaPresmetkaDo"] != DBNull.Value
                                            ? Convert.ToDateTime(reader["SelektiranaPresmetkaDo"]).ToString("yyyy-MM-dd")
                                            : "",
                                        primacNaProvizija = reader["PrimacNaProvizija"]?.ToString() ?? "",
                                        embgMbPrimacProvizija = reader["EMBGMBPrimacProvizija"]?.ToString() ?? "",
                                        datumNaDogovor = reader["DaumNaDogovor"] != DBNull.Value
                                            ? Convert.ToDateTime(reader["DaumNaDogovor"]).ToString("yyyy-MM-dd")
                                            : "",
                                        iznosProvizijaBruto = reader["IznosProvizijaBruto"]?.ToString() ?? "0",
                                        iznosProvizijaNeto = reader["IznosProvizijaNeto"]?.ToString() ?? "0"
                                    });
                                }
                                Console.WriteLine($"Found {recordCount} vraboten records");
                            }

                            return new JsonResult(new
                            {
                                success = true,
                                vrabotenData = vrabotenList
                            });
                        }
                        catch (Exception queryEx)
                        {
                            Console.WriteLine($"Query execution error: {queryEx.Message}");
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading vraboten data: {ex.Message}");
                return new JsonResult(new { success = false, message = "Грешка при вчитување на податоците." });
            }
        }

        public async Task<IActionResult> OnPostTestMethodAsync()
        {
            Console.WriteLine("=== TEST METHOD CALLED ===");
            Console.WriteLine($"Request Method: {Request.Method}");
            Console.WriteLine($"Request Path: {Request.Path}");
            Console.WriteLine($"Request Query: {Request.QueryString}");
            return new JsonResult(new { success = true, message = "Test method works!" });
        }

        public async Task<IActionResult> OnPostSaveVrabotenData(string tableDataJson)
        {
            Console.WriteLine("=== SaveVrabotenData METHOD CALLED ===");
            Console.WriteLine($"Received JSON: {tableDataJson}");

            if (string.IsNullOrEmpty(tableDataJson))
            {
                Console.WriteLine("ERROR: tableDataJson is null or empty");
                return new JsonResult(new { success = false, message = "Нема податоци за зачувување" });
            }

            List<VrabotenSintetikaData> tableData;
            try
            {
                tableData = JsonSerializer.Deserialize<List<VrabotenSintetikaData>>(tableDataJson);
                Console.WriteLine($"Deserialized {tableData?.Count ?? 0} records");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"JSON deserialization error: {ex.Message}");
                return new JsonResult(new { success = false, message = "Грешка при обработка на податоците" });
            }

            if (tableData == null || !tableData.Any())
            {
                return new JsonResult(new { success = false, message = "Нема податоци за зачувување" });
            }

            try
            {
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    Console.WriteLine("Database connection opened");

                    foreach (var row in tableData)
                    {
                        Console.WriteLine($"Processing row: EMBG={row.embg}, ImePrezime={row.imePrezime}");

                        string insertQuery = @"
                            INSERT INTO [dbo].[ProvizijaSintetikaVraboten]
                            (BrojNaSpecifikacija, SelektiranaPresmetkaOd, SelektiranaPresmetkaDo,
                             ImePrezime, EMBG, BrutoIznos, Pridonesi, NetoIznos, DataNaDogovor, DatumNaIsplata)
                            VALUES
                            (@BrojNaSpecifikacija, @SelektiranaPresmetkaOd, @SelektiranaPresmetkaDo,
                             @ImePrezime, @EMBG, @BrutoIznos, @Pridonesi, @NetoIznos, @DataNaDogovor, @DatumNaIsplata)";

                        using (var command = new SqlCommand(insertQuery, connection))
                        {
                            command.Parameters.AddWithValue("@BrojNaSpecifikacija", row.brojNaSpecifikacija ?? "");
                            command.Parameters.AddWithValue("@SelektiranaPresmetkaOd",
                                DateTime.TryParse(row.selektiranaPresmetkaOd, out var presmetkaOd) ? presmetkaOd : (object)DBNull.Value);
                            command.Parameters.AddWithValue("@SelektiranaPresmetkaDo",
                                DateTime.TryParse(row.selektiranaPresmetkaDo, out var presmetkaDo) ? presmetkaDo : (object)DBNull.Value);
                            command.Parameters.AddWithValue("@ImePrezime", row.imePrezime ?? "");
                            command.Parameters.AddWithValue("@EMBG", row.embg ?? "");
                            command.Parameters.AddWithValue("@BrutoIznos",
                                decimal.TryParse(row.brutoIznos, out var bruto) ? bruto : 0);
                            command.Parameters.AddWithValue("@Pridonesi",
                                decimal.TryParse(row.pridonesi, out var pridonesi) ? pridonesi : 0);
                            command.Parameters.AddWithValue("@NetoIznos",
                                decimal.TryParse(row.netoIznos, out var neto) ? neto : 0);
                            command.Parameters.AddWithValue("@DataNaDogovor",
                                DateTime.TryParse(row.datumNaDogovor, out var dogovor) ? dogovor : (object)DBNull.Value);
                            // For vraboteni, DatumNaIsplata should remain empty (DBNull.Value) when not provided
                            command.Parameters.AddWithValue("@DatumNaIsplata",
                                !string.IsNullOrEmpty(row.datumNaIsplata) && DateTime.TryParse(row.datumNaIsplata, out var isplata)
                                    ? isplata : (object)DBNull.Value);

                            Console.WriteLine("Executing SQL command...");
                            await command.ExecuteNonQueryAsync();
                            Console.WriteLine("SQL command executed successfully");
                        }
                    }
                }

                Console.WriteLine("All records processed successfully");
                return new JsonResult(new { success = true, message = "Записите се успешно зачувани" });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in SaveVrabotenDataAsync: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                return new JsonResult(new { success = false, message = "Грешка при зачувување: " + ex.Message });
            }
        }

        [IgnoreAntiforgeryToken]
        public async Task<IActionResult> OnPostSaveVrabotenSintetikaMultiple()
        {
            Console.WriteLine("=== SaveVrabotenSintetikaMultiple METHOD CALLED ===");
            Console.WriteLine($"Request Method: {Request.Method}");
            Console.WriteLine($"Request Path: {Request.Path}");
            Console.WriteLine($"Request Query: {Request.QueryString}");
            Console.WriteLine($"Content Type: {Request.ContentType}");
            Console.WriteLine($"Content Length: {Request.ContentLength}");

            try
            {
                Console.WriteLine("Starting to read request body...");

                using (var reader = new StreamReader(Request.Body))
                {
                    var json = await reader.ReadToEndAsync();
                    Console.WriteLine($"Received JSON length: {json?.Length ?? 0}");
                    Console.WriteLine($"Received JSON: {json}");

                    if (string.IsNullOrEmpty(json))
                    {
                        Console.WriteLine("ERROR: JSON is null or empty");
                        return new JsonResult(new { success = false, message = "Нема податоци во барањето" });
                    }

                    Console.WriteLine("Attempting to deserialize JSON...");
                    var tableData = JsonSerializer.Deserialize<List<VrabotenSintetikaData>>(json);
                    Console.WriteLine($"Deserialized {tableData?.Count ?? 0} records");

                    if (tableData != null)
                    {
                        for (int i = 0; i < tableData.Count; i++)
                        {
                            Console.WriteLine($"Record {i}: embg={tableData[i]?.embg}, ime={tableData[i]?.imePrezime}");
                        }
                    }

                    if (tableData == null || !tableData.Any())
                    {
                        Console.WriteLine("No data to save");
                        return new JsonResult(new { success = false, message = "Нема податоци за зачувување" });
                    }

                    string connectionString = _configuration.GetConnectionString("DefaultConnection");
                    Console.WriteLine($"Connection string: {connectionString?.Substring(0, Math.Min(50, connectionString?.Length ?? 0))}...");

                    using (var connection = new SqlConnection(connectionString))
                    {
                        await connection.OpenAsync();
                        Console.WriteLine("Database connection opened");

                        foreach (var row in tableData)
                        {
                            Console.WriteLine($"Processing row: EMBG={row.embg}, ImePrezime={row.imePrezime}");
                            Console.WriteLine($"Saving BrojNaSpecifikacija: '{row.brojNaSpecifikacija}'");
                            string insertQuery = @"
                                INSERT INTO [dbo].[ProvizijaSintetikaVraboten]
                                (BrojNaSpecifikacija, SelektiranaPresmetkaOd, SelektiranaPresmetkaDo,
                                 ImePrezime, EMBG, BrutoIznos, Pridonesi, NetoIznos, DataNaDogovor, DatumNaIsplata)
                                VALUES
                                (@BrojNaSpecifikacija, @SelektiranaPresmetkaOd, @SelektiranaPresmetkaDo,
                                 @ImePrezime, @EMBG, @BrutoIznos, @Pridonesi, @NetoIznos, @DataNaDogovor, @DatumNaIsplata)";

                            using (var command = new SqlCommand(insertQuery, connection))
                            {
                                command.Parameters.AddWithValue("@BrojNaSpecifikacija", row.brojNaSpecifikacija ?? "");
                                command.Parameters.AddWithValue("@SelektiranaPresmetkaOd",
                                    DateTime.TryParse(row.selektiranaPresmetkaOd, out var presmetkaOd) ? presmetkaOd : (object)DBNull.Value);
                                command.Parameters.AddWithValue("@SelektiranaPresmetkaDo",
                                    DateTime.TryParse(row.selektiranaPresmetkaDo, out var presmetkaDo) ? presmetkaDo : (object)DBNull.Value);
                                command.Parameters.AddWithValue("@ImePrezime", row.imePrezime ?? "");
                                command.Parameters.AddWithValue("@EMBG", row.embg ?? "");
                                command.Parameters.AddWithValue("@BrutoIznos",
                                    decimal.TryParse(row.brutoIznos, out var bruto) ? bruto : 0);
                                command.Parameters.AddWithValue("@Pridonesi",
                                    decimal.TryParse(row.pridonesi, out var pridonesi) ? pridonesi : 0);
                                command.Parameters.AddWithValue("@NetoIznos",
                                    decimal.TryParse(row.netoIznos, out var neto) ? neto : 0);
                                command.Parameters.AddWithValue("@DataNaDogovor",
                                    DateTime.TryParse(row.datumNaDogovor, out var dogovor) ? dogovor : (object)DBNull.Value);
                                command.Parameters.AddWithValue("@DatumNaIsplata",
                                    !string.IsNullOrEmpty(row.datumNaIsplata) && DateTime.TryParse(row.datumNaIsplata, out var isplata)
                                        ? isplata : DateTime.Now);

                                Console.WriteLine("Executing SQL command...");
                                await command.ExecuteNonQueryAsync();
                                Console.WriteLine("SQL command executed successfully");
                            }
                        }
                    }

                    Console.WriteLine("All records processed successfully");
                    return new JsonResult(new { success = true, message = "Записите се успешно зачувани" });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in SaveVrabotenSintetikaAsync: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                return new JsonResult(new { success = false, message = "Грешка при зачувување: " + ex.Message });
            }
        }

        public async Task<IActionResult> OnPostLoadSorabotnikData(string datumOd, string datumDo)
        {
            if (!await HasPageAccess("PresmetkaProvizijaSintetiki"))
            {
                return new JsonResult(new { success = false, message = "Немате дозвола за пристап." });
            }

            try
            {
                Console.WriteLine($"Loading sorabotnik data for dates: {datumOd} to {datumDo}");

                // First check if we have any records for these dates
                if (string.IsNullOrEmpty(datumOd) || string.IsNullOrEmpty(datumDo))
                {
                    return new JsonResult(new { success = false, message = "Датумите не се валидни." });
                }
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    // Get sorabotnik rows grouped by EMBGMBPrimacProvizija with summed bruto and neto amounts
                    // Filter for BrojNaSpecifikacijaProvizija that starts with "1/" instead of "2/"
                    string query = @"
                        SELECT
                            MAX(pps.BrojNaSpecifikacijaProvizija) AS BrojNaSpecifikacija,
                            MAX(pps.SelektiranaPresmetkaOd) AS SelektiranaPresmetkaOd,
                            MAX(pps.SelektiranaPresmetkaDo) AS SelektiranaPresmetkaDo,
                            CASE
                                WHEN MAX(klient.Ime) IS NOT NULL AND MAX(klient.Prezime) IS NOT NULL
                                THEN CONCAT(MAX(klient.Ime), ' ', MAX(klient.Prezime))
                                WHEN MAX(klient.Naziv) IS NOT NULL
                                THEN MAX(klient.Naziv)
                                ELSE 'N/A'
                            END AS PrimacNaProvizija,
                            pps.EMBGMBPrimacProvizija,
                            MAX(klient.DaumNaDogovor) AS DaumNaDogovor,
                            MAX(klient.PlateznaSmetka) AS PlateznaSmetka,
                            MAX(pps.ProcentDanok) AS ProcentDanok,
                            CASE
                                WHEN MAX(klient.EMBG) IS NOT NULL AND MAX(klient.EMBG) = pps.EMBGMBPrimacProvizija
                                THEN 'Физичко лице'
                                WHEN MAX(klient.MB) IS NOT NULL AND MAX(klient.MB) = pps.EMBGMBPrimacProvizija
                                THEN 'Правно лице'
                                ELSE 'Непознато'
                            END AS TipPrimacProvizija,
                            SUM(ISNULL(pps.IznosProvizijaBruto, 0)) AS IznosProvizijaBruto,
                            SUM(ISNULL(pps.IznosProvizijaNeto, 0)) AS IznosProvizijaNeto
                        FROM dbo.ProvizijaPresmetkaStavki pps
                        LEFT JOIN dbo.Klienti klient ON pps.EMBGMBPrimacProvizija = klient.EMBG OR pps.EMBGMBPrimacProvizija = klient.MB
                        WHERE pps.SelektiranaPresmetkaOd = @DatumOd
                            AND pps.SelektiranaPresmetkaDo = @DatumDo
                            AND pps.BrojNaSpecifikacijaProvizija LIKE '1/%'
                        GROUP BY pps.EMBGMBPrimacProvizija
                        ORDER BY PrimacNaProvizija";

                    Console.WriteLine($"Executing query with parameters: DatumOd={datumOd}, DatumDo={datumDo}");

                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@DatumOd", DateTime.Parse(datumOd));
                        command.Parameters.AddWithValue("@DatumDo", DateTime.Parse(datumDo));

                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            var sorabotnikList = new List<object>();
                            int recordCount = 0;

                            while (await reader.ReadAsync())
                            {
                                recordCount++;
                                sorabotnikList.Add(new
                                {
                                    brojNaSpecifikacija = reader["BrojNaSpecifikacija"]?.ToString() ?? "",
                                    selektiranaPresmetkaOd = reader["SelektiranaPresmetkaOd"] != DBNull.Value
                                        ? Convert.ToDateTime(reader["SelektiranaPresmetkaOd"]).ToString("yyyy-MM-dd")
                                        : "",
                                    selektiranaPresmetkaDo = reader["SelektiranaPresmetkaDo"] != DBNull.Value
                                        ? Convert.ToDateTime(reader["SelektiranaPresmetkaDo"]).ToString("yyyy-MM-dd")
                                        : "",
                                    primacNaProvizija = reader["PrimacNaProvizija"]?.ToString() ?? "",
                                    embgMbPrimacProvizija = reader["EMBGMBPrimacProvizija"]?.ToString() ?? "",
                                    datumNaDogovor = reader["DaumNaDogovor"] != DBNull.Value
                                        ? Convert.ToDateTime(reader["DaumNaDogovor"]).ToString("yyyy-MM-dd")
                                        : "",
                                    plateznaSmetka = reader["PlateznaSmetka"]?.ToString() ?? "",
                                    procentDanok = reader["ProcentDanok"]?.ToString() ?? "0",
                                    tipPrimacProvizija = reader["TipPrimacProvizija"]?.ToString() ?? "Непознато",
                                    iznosProvizijaBruto = reader["IznosProvizijaBruto"]?.ToString() ?? "0",
                                    iznosProvizijaNeto = reader["IznosProvizijaNeto"]?.ToString() ?? "0"
                                });
                            }

                            Console.WriteLine($"Found {recordCount} sorabotnik records");
                            return new JsonResult(new { success = true, sorabotnikData = sorabotnikList });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading sorabotnik data: {ex.Message}");
                return new JsonResult(new { success = false, message = "Грешка при вчитување на податоците за соработници." });
            }
        }

        public async Task<IActionResult> OnPostSaveSorabotnikData(string tableDataJson)
        {
            Console.WriteLine("=== SaveSorabotnikData METHOD CALLED ===");
            Console.WriteLine($"Received JSON: {tableDataJson}");

            if (!await HasPageAccess("PresmetkaProvizijaSintetiki"))
            {
                return new JsonResult(new { success = false, message = "Немате дозвола за пристап." });
            }

            try
            {
                // Parse the JSON data
                var tableData = JsonSerializer.Deserialize<List<SorabotnikSintetikaModel>>(tableDataJson);
                Console.WriteLine($"Deserialized {tableData?.Count ?? 0} records");

                if (tableData == null || !tableData.Any())
                {
                    return new JsonResult(new { success = false, message = "Нема податоци за зачувување." });
                }

                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    Console.WriteLine("Database connection opened");

                    foreach (var row in tableData)
                    {
                        Console.WriteLine($"Processing row: EMBG={row.embg}, ImePrezime={row.imePrezime}");
                        Console.WriteLine($"Saving BrojNaSpecifikacija: '{row.brojNaSpecifikacija}'");

                        string insertQuery = @"
                            INSERT INTO [dbo].[ProvizijaSintetikaSorabotnik]
                            (BrojNaSpecifikacija, SelektiranaPresmetkaOd, SelektiranaPresmetkaDo, TipKlient,
                             ImePrezime, EMBG, BrutoIznos, Danok, NetoIznos, DataNaDogovor, Smetka, DatumNaIsplata)
                            VALUES
                            (@BrojNaSpecifikacija, @SelektiranaPresmetkaOd, @SelektiranaPresmetkaDo, @TipKlient,
                             @ImePrezime, @EMBG, @BrutoIznos, @Danok, @NetoIznos, @DataNaDogovor, @Smetka, @DatumNaIsplata)";

                        using (SqlCommand command = new SqlCommand(insertQuery, connection))
                        {
                            command.Parameters.AddWithValue("@BrojNaSpecifikacija", row.brojNaSpecifikacija ?? "");
                            command.Parameters.AddWithValue("@SelektiranaPresmetkaOd",
                                string.IsNullOrEmpty(row.selektiranaPresmetkaOd) ? DBNull.Value : DateTime.Parse(row.selektiranaPresmetkaOd));
                            command.Parameters.AddWithValue("@SelektiranaPresmetkaDo",
                                string.IsNullOrEmpty(row.selektiranaPresmetkaDo) ? DBNull.Value : DateTime.Parse(row.selektiranaPresmetkaDo));
                            command.Parameters.AddWithValue("@TipKlient", row.tipPrimacProvizija ?? "");
                            command.Parameters.AddWithValue("@ImePrezime", row.imePrezime ?? "");
                            command.Parameters.AddWithValue("@EMBG", row.embg ?? "");
                            command.Parameters.AddWithValue("@BrutoIznos", decimal.TryParse(row.brutoIznos, out var bruto) ? bruto : 0);
                            command.Parameters.AddWithValue("@Danok", decimal.TryParse(row.danok, out var danok) ? danok : 0);
                            command.Parameters.AddWithValue("@NetoIznos", decimal.TryParse(row.netoIznos, out var neto) ? neto : 0);
                            command.Parameters.AddWithValue("@DataNaDogovor",
                                string.IsNullOrEmpty(row.datumNaDogovor) ? DBNull.Value : DateTime.Parse(row.datumNaDogovor));
                            command.Parameters.AddWithValue("@Smetka", row.smetka ?? "");
                            command.Parameters.AddWithValue("@DatumNaIsplata",
                                string.IsNullOrEmpty(row.datumNaIsplata) ? DBNull.Value : DateTime.Parse(row.datumNaIsplata));

                            Console.WriteLine("Executing SQL command...");
                            await command.ExecuteNonQueryAsync();
                            Console.WriteLine("SQL command executed successfully");
                        }
                    }
                }

                Console.WriteLine("All records processed successfully");
                return new JsonResult(new { success = true, message = "Записите се успешно зачувани!" });
            }
            catch (JsonException ex)
            {
                Console.WriteLine($"JSON parsing error: {ex.Message}");
                return new JsonResult(new { success = false, message = "Грешка при обработка на податоците." });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving sorabotnik data: {ex.Message}");
                return new JsonResult(new { success = false, message = "Грешка при зачувување на податоците." });
            }
        }

        public async Task<IActionResult> OnPostLoadSavedVrabotenData(string brojNaSpecifikacija)
        {
            if (!await HasPageAccess("PresmetkaProvizijaSintetiki"))
            {
                return new JsonResult(new { success = false, message = "Немате дозвола за пристап." });
            }

            try
            {
                // For vraboteni, convert "1/*" pattern to "2/*" pattern
                string vrabotenBrojNaSpecifikacija = brojNaSpecifikacija;
                if (!string.IsNullOrEmpty(brojNaSpecifikacija) && brojNaSpecifikacija.StartsWith("1/"))
                {
                    vrabotenBrojNaSpecifikacija = brojNaSpecifikacija.Replace("1/", "2/");
                }

                string connectionString = _configuration.GetConnectionString("DefaultConnection");

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    string query = @"
                        SELECT
                            BrojNaSpecifikacija,
                            SelektiranaPresmetkaOd,
                            SelektiranaPresmetkaDo,
                            ImePrezime,
                            EMBG,
                            BrutoIznos,
                            Pridonesi,
                            NetoIznos,
                            DataNaDogovor,
                            DatumNaIsplata,
                            DateCreated
                        FROM [dbo].[ProvizijaSintetikaVraboten]
                        WHERE BrojNaSpecifikacija = @BrojNaSpecifikacija
                        ORDER BY ImePrezime";

                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@BrojNaSpecifikacija", vrabotenBrojNaSpecifikacija ?? "");

                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            var savedData = new List<object>();
                            int recordCount = 0;

                            while (await reader.ReadAsync())
                            {
                                recordCount++;

                                savedData.Add(new
                                {
                                    brojNaSpecifikacija = reader["BrojNaSpecifikacija"]?.ToString() ?? "",
                                    selektiranaPresmetkaOd = reader["SelektiranaPresmetkaOd"] != DBNull.Value
                                        ? Convert.ToDateTime(reader["SelektiranaPresmetkaOd"]).ToString("yyyy-MM-dd")
                                        : "",
                                    selektiranaPresmetkaDo = reader["SelektiranaPresmetkaDo"] != DBNull.Value
                                        ? Convert.ToDateTime(reader["SelektiranaPresmetkaDo"]).ToString("yyyy-MM-dd")
                                        : "",
                                    imePrezime = reader["ImePrezime"]?.ToString() ?? "",
                                    embg = reader["EMBG"]?.ToString() ?? "",
                                    brutoIznos = reader["BrutoIznos"]?.ToString() ?? "0",
                                    pridonesi = reader["Pridonesi"]?.ToString() ?? "0",
                                    netoIznos = reader["NetoIznos"]?.ToString() ?? "0",
                                    datumNaDogovor = reader["DataNaDogovor"] != DBNull.Value
                                        ? Convert.ToDateTime(reader["DataNaDogovor"]).ToString("yyyy-MM-dd")
                                        : "",
                                    datumNaIsplata = reader["DatumNaIsplata"] != DBNull.Value
                                        ? Convert.ToDateTime(reader["DatumNaIsplata"]).ToString("yyyy-MM-dd")
                                        : "",
                                    dateCreated = reader["DateCreated"] != DBNull.Value
                                        ? Convert.ToDateTime(reader["DateCreated"]).ToString("yyyy-MM-dd HH:mm")
                                        : ""
                                });
                            }

                            if (recordCount == 0)
                            {
                                return new JsonResult(new { success = true, savedData = new List<object>(), recordCount = 0 });
                            }

                            return new JsonResult(new { success = true, savedData = savedData, recordCount = recordCount });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = "Грешка при вчитување на зачуваните податоци." });
            }
        }

        public async Task<IActionResult> OnPostLoadSavedSorabotnikData(string brojNaSpecifikacija)
        {
            if (!await HasPageAccess("PresmetkaProvizijaSintetiki"))
            {
                return new JsonResult(new { success = false, message = "Немате дозвола за пристап." });
            }

            try
            {
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    string query = @"
                        SELECT
                            BrojNaSpecifikacija,
                            SelektiranaPresmetkaOd,
                            SelektiranaPresmetkaDo,
                            TipKlient,
                            ImePrezime,
                            EMBG,
                            BrutoIznos,
                            Danok,
                            NetoIznos,
                            DataNaDogovor,
                            Smetka,
                            DatumNaIsplata,
                            DateCreated
                        FROM [dbo].[ProvizijaSintetikaSorabotnik]
                        WHERE BrojNaSpecifikacija = @BrojNaSpecifikacija
                        ORDER BY ImePrezime";

                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@BrojNaSpecifikacija", brojNaSpecifikacija ?? "");

                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            var savedData = new List<object>();
                            int recordCount = 0;

                            while (await reader.ReadAsync())
                            {
                                recordCount++;

                                savedData.Add(new
                                {
                                    brojNaSpecifikacija = reader["BrojNaSpecifikacija"]?.ToString() ?? "",
                                    selektiranaPresmetkaOd = reader["SelektiranaPresmetkaOd"] != DBNull.Value
                                        ? Convert.ToDateTime(reader["SelektiranaPresmetkaOd"]).ToString("yyyy-MM-dd")
                                        : "",
                                    selektiranaPresmetkaDo = reader["SelektiranaPresmetkaDo"] != DBNull.Value
                                        ? Convert.ToDateTime(reader["SelektiranaPresmetkaDo"]).ToString("yyyy-MM-dd")
                                        : "",
                                    tipPrimacProvizija = reader["TipKlient"]?.ToString() ?? "",
                                    imePrezime = reader["ImePrezime"]?.ToString() ?? "",
                                    embg = reader["EMBG"]?.ToString() ?? "",
                                    brutoIznos = reader["BrutoIznos"]?.ToString() ?? "0",
                                    danok = reader["Danok"]?.ToString() ?? "0",
                                    netoIznos = reader["NetoIznos"]?.ToString() ?? "0",
                                    datumNaDogovor = reader["DataNaDogovor"] != DBNull.Value
                                        ? Convert.ToDateTime(reader["DataNaDogovor"]).ToString("yyyy-MM-dd")
                                        : "",
                                    smetka = reader["Smetka"]?.ToString() ?? "",
                                    datumNaIsplata = reader["DatumNaIsplata"] != DBNull.Value
                                        ? Convert.ToDateTime(reader["DatumNaIsplata"]).ToString("yyyy-MM-dd")
                                        : "",
                                    dateCreated = reader["DateCreated"] != DBNull.Value
                                        ? Convert.ToDateTime(reader["DateCreated"]).ToString("yyyy-MM-dd HH:mm")
                                        : ""
                                });
                            }

                            if (recordCount == 0)
                            {
                                return new JsonResult(new { success = true, savedData = new List<object>(), recordCount = 0 });
                            }

                            return new JsonResult(new { success = true, savedData = savedData, recordCount = recordCount });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = "Грешка при вчитување на зачуваните податоци за соработници." });
            }
        }

        public async Task<IActionResult> OnGetExportVrabotenExcel(string brojNaSpecifikacija)
        {
            if (!await HasPageAccess("PresmetkaProvizijaSintetiki"))
            {
                return Forbid();
            }

            try
            {
                Console.WriteLine($"=== ExportVrabotenExcel METHOD CALLED ===");
                Console.WriteLine($"Exporting Vraboten Excel for BrojNaSpecifikacija: {brojNaSpecifikacija}");

                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    string query = @"
                        SELECT
                            BrojNaSpecifikacija,
                            SelektiranaPresmetkaOd,
                            SelektiranaPresmetkaDo,
                            ImePrezime,
                            EMBG,
                            BrutoIznos,
                            Pridonesi,
                            NetoIznos,
                            DataNaDogovor,
                            DatumNaIsplata,
                            DateCreated
                        FROM [dbo].[ProvizijaSintetikaVraboten]
                        WHERE BrojNaSpecifikacija = @BrojNaSpecifikacija
                        ORDER BY ImePrezime";

                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@BrojNaSpecifikacija", brojNaSpecifikacija ?? "");

                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                            using (var package = new ExcelPackage())
                            {
                                var worksheet = package.Workbook.Worksheets.Add("Вработен Синтетика");

                                // Header information
                                var firstRecord = true;
                                string datumOd = "", datumDo = "";

                                // Set up the header
                                worksheet.Cells[1, 1].Value = "ВРАБОТЕН СИНТЕТИКА";
                                worksheet.Cells[1, 1].Style.Font.Bold = true;
                                worksheet.Cells[1, 1].Style.Font.Size = 16;

                                worksheet.Cells[2, 1].Value = $"Број на спецификација: {brojNaSpecifikacija}";
                                worksheet.Cells[2, 1].Style.Font.Bold = true;

                                // Column headers starting from row 5
                                int headerRow = 5;
                                worksheet.Cells[headerRow, 1].Value = "Број на спецификација";
                                worksheet.Cells[headerRow, 2].Value = "Селектирана пресметка од";
                                worksheet.Cells[headerRow, 3].Value = "Селектирана пресметка до";
                                worksheet.Cells[headerRow, 4].Value = "Примач на провизија";
                                worksheet.Cells[headerRow, 5].Value = "ЕМБГ/МБ примач провизија";
                                worksheet.Cells[headerRow, 6].Value = "Датум на договор";
                                worksheet.Cells[headerRow, 7].Value = "Износ провизија бруто";
                                worksheet.Cells[headerRow, 8].Value = "Износ провизија нето";
                                worksheet.Cells[headerRow, 9].Value = "Приdonesi";
                                worksheet.Cells[headerRow, 10].Value = "Датум исплата";
                                worksheet.Cells[headerRow, 11].Value = "Датум на зачувување";

                                // Style headers
                                var headerRange = worksheet.Cells[headerRow, 1, headerRow, 11];
                                headerRange.Style.Font.Bold = true;
                                headerRange.Style.Fill.PatternType = ExcelFillStyle.Solid;
                                headerRange.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                                headerRange.Style.Border.Top.Style = ExcelBorderStyle.Thick;
                                headerRange.Style.Border.Bottom.Style = ExcelBorderStyle.Thick;
                                headerRange.Style.Border.Left.Style = ExcelBorderStyle.Thick;
                                headerRange.Style.Border.Right.Style = ExcelBorderStyle.Thick;

                                int currentRow = headerRow + 1;
                                int recordCount = 0;
                                while (await reader.ReadAsync())
                                {
                                    recordCount++;
                                    if (firstRecord)
                                    {
                                        datumOd = reader["SelektiranaPresmetkaOd"] != DBNull.Value
                                            ? Convert.ToDateTime(reader["SelektiranaPresmetkaOd"]).ToString("dd.MM.yyyy")
                                            : "";
                                        datumDo = reader["SelektiranaPresmetkaDo"] != DBNull.Value
                                            ? Convert.ToDateTime(reader["SelektiranaPresmetkaDo"]).ToString("dd.MM.yyyy")
                                            : "";

                                        worksheet.Cells[3, 1].Value = $"Период: {datumOd} - {datumDo}";
                                        worksheet.Cells[3, 1].Style.Font.Bold = true;
                                        firstRecord = false;
                                    }

                                    worksheet.Cells[currentRow, 1].Value = reader["BrojNaSpecifikacija"]?.ToString() ?? "";
                                    worksheet.Cells[currentRow, 2].Value = datumOd;
                                    worksheet.Cells[currentRow, 3].Value = datumDo;
                                    worksheet.Cells[currentRow, 4].Value = reader["ImePrezime"]?.ToString() ?? "";
                                    worksheet.Cells[currentRow, 5].Value = reader["EMBG"]?.ToString() ?? "";
                                    worksheet.Cells[currentRow, 6].Value = reader["DataNaDogovor"] != DBNull.Value
                                        ? Convert.ToDateTime(reader["DataNaDogovor"]).ToString("dd.MM.yyyy")
                                        : "";
                                    worksheet.Cells[currentRow, 7].Value = reader["BrutoIznos"]?.ToString() ?? "0";
                                    worksheet.Cells[currentRow, 8].Value = reader["NetoIznos"]?.ToString() ?? "0";
                                    worksheet.Cells[currentRow, 9].Value = reader["Pridonesi"]?.ToString() ?? "0";
                                    worksheet.Cells[currentRow, 10].Value = reader["DatumNaIsplata"] != DBNull.Value
                                        ? Convert.ToDateTime(reader["DatumNaIsplata"]).ToString("dd.MM.yyyy")
                                        : "";
                                    worksheet.Cells[currentRow, 11].Value = reader["DateCreated"] != DBNull.Value
                                        ? Convert.ToDateTime(reader["DateCreated"]).ToString("dd.MM.yyyy HH:mm")
                                        : "";

                                    currentRow++;
                                }

                                Console.WriteLine($"Processed {recordCount} records for Vraboten Excel export");

                                // Auto-fit columns
                                worksheet.Cells.AutoFitColumns();

                                // Create memory stream
                                using (var stream = new MemoryStream())
                                {
                                    package.SaveAs(stream);
                                    stream.Position = 0;

                                    string fileName = $"Vraboten_Sintetika_{brojNaSpecifikacija?.Replace("/", "_")}_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
                                    Console.WriteLine($"Generated Excel file: {fileName}");
                                    return File(stream.ToArray(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error exporting Vraboten Excel: {ex.Message}");
                return BadRequest("Грешка при експорт на Excel датотека.");
            }
        }

        public async Task<IActionResult> OnGetExportSorabotnikExcel(string brojNaSpecifikacija)
        {
            if (!await HasPageAccess("PresmetkaProvizijaSintetiki"))
            {
                return Forbid();
            }

            try
            {
                Console.WriteLine($"=== ExportSorabotnikExcel METHOD CALLED ===");
                Console.WriteLine($"Exporting Sorabotnik Excel for BrojNaSpecifikacija: {brojNaSpecifikacija}");

                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    string query = @"
                        SELECT
                            BrojNaSpecifikacija,
                            SelektiranaPresmetkaOd,
                            SelektiranaPresmetkaDo,
                            TipKlient,
                            ImePrezime,
                            EMBG,
                            BrutoIznos,
                            Danok,
                            NetoIznos,
                            DataNaDogovor,
                            Smetka,
                            DatumNaIsplata,
                            DateCreated
                        FROM [dbo].[ProvizijaSintetikaSorabotnik]
                        WHERE BrojNaSpecifikacija = @BrojNaSpecifikacija
                        ORDER BY ImePrezime";

                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@BrojNaSpecifikacija", brojNaSpecifikacija ?? "");

                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                            using (var package = new ExcelPackage())
                            {
                                var worksheet = package.Workbook.Worksheets.Add("Соработник Синтетика");

                                // Header information
                                var firstRecord = true;
                                string datumOd = "", datumDo = "";

                                // Set up the header
                                worksheet.Cells[1, 1].Value = "СОРАБОТНИК СИНТЕТИКА";
                                worksheet.Cells[1, 1].Style.Font.Bold = true;
                                worksheet.Cells[1, 1].Style.Font.Size = 16;

                                worksheet.Cells[2, 1].Value = $"Број на спецификација: {brojNaSpecifikacija}";
                                worksheet.Cells[2, 1].Style.Font.Bold = true;

                                // Column headers starting from row 5
                                int headerRow = 5;
                                worksheet.Cells[headerRow, 1].Value = "Број на спецификација";
                                worksheet.Cells[headerRow, 2].Value = "Селектирана пресметка од";
                                worksheet.Cells[headerRow, 3].Value = "Селектирана пресметка до";
                                worksheet.Cells[headerRow, 4].Value = "Примач на провизија";
                                worksheet.Cells[headerRow, 5].Value = "ЕМБГ/МБ примач провизија";
                                worksheet.Cells[headerRow, 6].Value = "Тип примач провизија";
                                worksheet.Cells[headerRow, 7].Value = "Датум на договор";
                                worksheet.Cells[headerRow, 8].Value = "Сметка";
                                worksheet.Cells[headerRow, 9].Value = "Износ провизија бруто";
                                worksheet.Cells[headerRow, 10].Value = "Износ провизија нето";
                                worksheet.Cells[headerRow, 11].Value = "Данок";
                                worksheet.Cells[headerRow, 12].Value = "Датум исплата";
                                worksheet.Cells[headerRow, 13].Value = "Датум на зачувување";

                                // Style headers
                                var headerRange = worksheet.Cells[headerRow, 1, headerRow, 13];
                                headerRange.Style.Font.Bold = true;
                                headerRange.Style.Fill.PatternType = ExcelFillStyle.Solid;
                                headerRange.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                                headerRange.Style.Border.Top.Style = ExcelBorderStyle.Thick;
                                headerRange.Style.Border.Bottom.Style = ExcelBorderStyle.Thick;
                                headerRange.Style.Border.Left.Style = ExcelBorderStyle.Thick;
                                headerRange.Style.Border.Right.Style = ExcelBorderStyle.Thick;

                                int currentRow = headerRow + 1;
                                while (await reader.ReadAsync())
                                {
                                    if (firstRecord)
                                    {
                                        datumOd = reader["SelektiranaPresmetkaOd"] != DBNull.Value
                                            ? Convert.ToDateTime(reader["SelektiranaPresmetkaOd"]).ToString("dd.MM.yyyy")
                                            : "";
                                        datumDo = reader["SelektiranaPresmetkaDo"] != DBNull.Value
                                            ? Convert.ToDateTime(reader["SelektiranaPresmetkaDo"]).ToString("dd.MM.yyyy")
                                            : "";

                                        worksheet.Cells[3, 1].Value = $"Период: {datumOd} - {datumDo}";
                                        worksheet.Cells[3, 1].Style.Font.Bold = true;
                                        firstRecord = false;
                                    }

                                    worksheet.Cells[currentRow, 1].Value = reader["BrojNaSpecifikacija"]?.ToString() ?? "";
                                    worksheet.Cells[currentRow, 2].Value = datumOd;
                                    worksheet.Cells[currentRow, 3].Value = datumDo;
                                    worksheet.Cells[currentRow, 4].Value = reader["ImePrezime"]?.ToString() ?? "";
                                    worksheet.Cells[currentRow, 5].Value = reader["EMBG"]?.ToString() ?? "";
                                    worksheet.Cells[currentRow, 6].Value = reader["TipKlient"]?.ToString() ?? "";
                                    worksheet.Cells[currentRow, 7].Value = reader["DataNaDogovor"] != DBNull.Value
                                        ? Convert.ToDateTime(reader["DataNaDogovor"]).ToString("dd.MM.yyyy")
                                        : "";
                                    worksheet.Cells[currentRow, 8].Value = reader["Smetka"]?.ToString() ?? "";
                                    worksheet.Cells[currentRow, 9].Value = reader["BrutoIznos"]?.ToString() ?? "0";
                                    worksheet.Cells[currentRow, 10].Value = reader["NetoIznos"]?.ToString() ?? "0";
                                    worksheet.Cells[currentRow, 11].Value = reader["Danok"]?.ToString() ?? "0";
                                    worksheet.Cells[currentRow, 12].Value = reader["DatumNaIsplata"] != DBNull.Value
                                        ? Convert.ToDateTime(reader["DatumNaIsplata"]).ToString("dd.MM.yyyy")
                                        : "";
                                    worksheet.Cells[currentRow, 13].Value = reader["DateCreated"] != DBNull.Value
                                        ? Convert.ToDateTime(reader["DateCreated"]).ToString("dd.MM.yyyy HH:mm")
                                        : "";

                                    currentRow++;
                                }

                                // Auto-fit columns
                                worksheet.Cells.AutoFitColumns();

                                // Create memory stream
                                using (var stream = new MemoryStream())
                                {
                                    package.SaveAs(stream);
                                    stream.Position = 0;

                                    string fileName = $"Sorabotnik_Sintetika_{brojNaSpecifikacija?.Replace("/", "_")}_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
                                    return File(stream.ToArray(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error exporting Sorabotnik Excel: {ex.Message}");
                return BadRequest("Грешка при експорт на Excel датотека за соработници.");
            }
        }

        public async Task<IActionResult> OnPostSaveVrabotenSintetika(VrabotenSintetikaModel model)
        {
            if (!await HasPageAccess("PresmetkaProvizijaSintetiki"))
            {
                return RedirectToAccessDenied();
            }

            try
            {
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    string insertQuery = @"
                        INSERT INTO dbo.ProvizijaSintetikaVraboten (
                            BrojNaSpecifikacija,
                            SelektiranaPresmetkaOd,
                            SelektiranaPresmetkaDo,
                            ImePrezime,
                            EMBG,
                            BrutoIznos,
                            Pridonesi,
                            NetoIznos,
                            DataNaDogovor,
                            DatumNaIsplata
                        ) VALUES (
                            @BrojNaSpecifikacija,
                            @SelektiranaPresmetkaOd,
                            @SelektiranaPresmetkaDo,
                            @ImePrezime,
                            @EMBG,
                            @BrutoIznos,
                            @Pridonesi,
                            @NetoIznos,
                            @DataNaDogovor,
                            @DatumNaIsplata
                        )";

                    using (SqlCommand command = new SqlCommand(insertQuery, connection))
                    {
                        command.Parameters.AddWithValue("@BrojNaSpecifikacija", model.BrojNaSpecifikacija ?? "");
                        command.Parameters.AddWithValue("@SelektiranaPresmetkaOd", model.SelektiranaPresmetkaOd);
                        command.Parameters.AddWithValue("@SelektiranaPresmetkaDo", model.SelektiranaPresmetkaDo);
                        command.Parameters.AddWithValue("@ImePrezime", model.ImePrezime ?? "");
                        command.Parameters.AddWithValue("@EMBG", model.EMBG ?? "");
                        command.Parameters.AddWithValue("@BrutoIznos", model.BrutoIznos);
                        command.Parameters.AddWithValue("@Pridonesi", model.Pridonesi ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@NetoIznos", model.NetoIznos ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@DataNaDogovor", model.DataNaDogovor);
                        command.Parameters.AddWithValue("@DatumNaIsplata", model.DatumNaIsplata ?? (object)DBNull.Value);

                        await command.ExecuteNonQueryAsync();
                    }
                }

                TempData["SuccessMessage"] = "Вработен синтетика успешно зачувана.";
                return RedirectToPage();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving vraboten sintetika: {ex.Message}");
                TempData["ErrorMessage"] = "Грешка при зачувување на вработен синтетика.";
                return RedirectToPage();
            }
        }

        // Helper classes for model binding
        public class LoadVrabotenDataRequest
        {
            public string BrojNaSpecifikacija { get; set; }
            public string DatumOd { get; set; }
            public string DatumDo { get; set; }
        }

        public class VrabotenSintetikaModel
        {
            public string BrojNaSpecifikacija { get; set; }
            public DateTime SelektiranaPresmetkaOd { get; set; }
            public DateTime SelektiranaPresmetkaDo { get; set; }
            public string ImePrezime { get; set; }
            public string EMBG { get; set; }
            public decimal BrutoIznos { get; set; }
            public decimal? Pridonesi { get; set; }
            public decimal? NetoIznos { get; set; }
            public DateTime DataNaDogovor { get; set; }
            public DateTime? DatumNaIsplata { get; set; }
        }

        public class SorabotnikSintetikaModel
        {
            public string brojNaSpecifikacija { get; set; } = "";
            public string selektiranaPresmetkaOd { get; set; } = "";
            public string selektiranaPresmetkaDo { get; set; } = "";
            public string tipPrimacProvizija { get; set; } = "";
            public string imePrezime { get; set; } = "";
            public string embg { get; set; } = "";
            public string datumNaDogovor { get; set; } = "";
            public string smetka { get; set; } = "";
            public string brutoIznos { get; set; } = "";
            public string netoIznos { get; set; } = "";
            public string danok { get; set; } = "";
            public string datumNaIsplata { get; set; } = "";
        }

        public class VrabotenSintetikaData
        {
            public string brojNaSpecifikacija { get; set; }
            public string selektiranaPresmetkaOd { get; set; }
            public string selektiranaPresmetkaDo { get; set; }
            public string imePrezime { get; set; }
            public string embg { get; set; }
            public string datumNaDogovor { get; set; }
            public string brutoIznos { get; set; }
            public string netoIznos { get; set; }
            public string pridonesi { get; set; }
            public string datumNaIsplata { get; set; }
        }
    }
}