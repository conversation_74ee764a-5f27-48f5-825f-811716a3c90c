using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Data.SqlClient;
using RazorPortal.Services;
using System.Data;
using OfficeOpenXml;
using System.ComponentModel.DataAnnotations;

namespace NextBroker.Pages.Pregledi
{
    public class IzvestajKlientiModel : SecurePageModel
    {
        public List<KlientData> Klienti { get; set; } = new List<KlientData>();
        public string SearchTerm { get; set; } = string.Empty;
        public string SortColumn { get; set; } = string.Empty;
        public string SortDirection { get; set; } = "asc";

        public IzvestajKlientiModel(IConfiguration configuration)
            : base(configuration)
        {
            // Set EPPlus license context
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        }

        public async Task<IActionResult> OnGet(string searchTerm = "", string sortColumn = "", string sortDirection = "asc")
        {
            if (!await HasPageAccess("IzvestajKlienti"))
            {
                return RedirectToAccessDenied();
            }

            SearchTerm = searchTerm ?? string.Empty;
            SortColumn = sortColumn ?? string.Empty;
            SortDirection = sortDirection ?? "asc";

            await LoadKlienti();
            return Page();
        }

        public async Task<IActionResult> OnGetExportExcel(string searchTerm = "", string sortColumn = "", string sortDirection = "asc")
        {
            if (!await HasPageAccess("IzvestajKlienti"))
            {
                return RedirectToAccessDenied();
            }

            SearchTerm = searchTerm ?? string.Empty;
            SortColumn = sortColumn ?? string.Empty;
            SortDirection = sortDirection ?? "asc";

            await LoadKlienti();

            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("Извештај Клиенти");

            // Add headers
            var headers = new[]
            {
                "Име", "Презиме", "Назив", "Единствен даночен број", "Матичен број", "ЕМБГ",
                "Општина од документ за идентицикација", "Улица од документ за идентификација",
                "Број од документ за идентификација", "Општина за комуникација", "Улица за комуникација",
                "Број за комуникација", "Датум на тековна состојба", "Број на пасош / лична карта",
                "Датум важи од", "Датум важи до", "Дејност", "Е-маил", "Телефон", "Веб страна",
                "Согласност за директен маркетинг", "Согласност за Е-маил комуникација",
                "Согласност за тел. комуникација", "Датум на повлеќена согласност за директен маркетинг",
                "Вистински сопственик", "Вистински сопственик Име", "Вистински сопственик Презиме",
                "Носител на јавна функција", "Основ за носител на јавна функција", "Засилена анализа",
                "Ниво на ризик", "Датум на договор", "Договор важи до", "Број на договор",
                "Договор определено / неопределено", "Платежна сметка", "Банка", "Работна позиција",
                "Организациона единица", "Експозитура", "Задолжително лиценца",
                "Број на решение од АСО за лиценца", "Датум на решение од АСО за лиценца",
                "Датум на одземање на лиценца", "Број на дозвола за вршење Осигурително Брокерски работи",
                "Датум на дозвола за вршење осигурително брокерски работи", "Живот / Неживот",
                "Надреден", "Надреден важи од", "Надреден важи до"
            };

            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = headers[i];
                worksheet.Cells[1, i + 1].Style.Font.Bold = true;
            }

            // Add data
            for (int i = 0; i < Klienti.Count; i++)
            {
                var klient = Klienti[i];
                var row = i + 2;

                worksheet.Cells[row, 1].Value = klient.Ime;
                worksheet.Cells[row, 2].Value = klient.Prezime;
                worksheet.Cells[row, 3].Value = klient.Naziv;
                worksheet.Cells[row, 4].Value = klient.EDB;
                worksheet.Cells[row, 5].Value = klient.MB;
                worksheet.Cells[row, 6].Value = klient.EMBG;
                worksheet.Cells[row, 7].Value = klient.OpstinaOdDokument;
                worksheet.Cells[row, 8].Value = klient.UlicaOdDokument;
                worksheet.Cells[row, 9].Value = klient.BrojOdDokument;
                worksheet.Cells[row, 10].Value = klient.OpstinaZaKomunikacija;
                worksheet.Cells[row, 11].Value = klient.UlicaZaKomunikacija;
                worksheet.Cells[row, 12].Value = klient.BrojZaKomunikacija;
                worksheet.Cells[row, 13].Value = klient.DatumNaTekovnaSostojba?.ToString("dd.MM.yyyy");
                worksheet.Cells[row, 14].Value = klient.BrojPasosLicnaKarta;
                worksheet.Cells[row, 15].Value = klient.DatumVaziOdPasosLicnaKarta?.ToString("dd.MM.yyyy");
                worksheet.Cells[row, 16].Value = klient.DatumVaziDoPasosLicnaKarta?.ToString("dd.MM.yyyy");
                worksheet.Cells[row, 17].Value = klient.Dejnost;
                worksheet.Cells[row, 18].Value = klient.Email;
                worksheet.Cells[row, 19].Value = klient.Tel;
                worksheet.Cells[row, 20].Value = klient.WebStrana;
                worksheet.Cells[row, 21].Value = klient.SoglasnostZaDirektenMarketing ? "Да" : "Не";
                worksheet.Cells[row, 22].Value = klient.SoglasnostZaEmailKomunikacija ? "Да" : "Не";
                worksheet.Cells[row, 23].Value = klient.SoglasnostZaTelKomunikacija ? "Да" : "Не";
                worksheet.Cells[row, 24].Value = klient.DatumNaPovlecenaSoglasnostZaDirektenMarketing?.ToString("dd.MM.yyyy");
                worksheet.Cells[row, 25].Value = klient.VistinskiSopstvenik ? "Да" : "Не";
                worksheet.Cells[row, 26].Value = klient.VistinskiSopstvenikIme;
                worksheet.Cells[row, 27].Value = klient.VistinskiSopstvenikPrezime;
                worksheet.Cells[row, 28].Value = klient.NositelNaJF ? "Да" : "Не";
                worksheet.Cells[row, 29].Value = klient.OsnovZaNositelNaJF;
                worksheet.Cells[row, 30].Value = klient.ZasilenaAnaliza ? "Да" : "Не";
                worksheet.Cells[row, 31].Value = klient.NivoNaRizik;
                worksheet.Cells[row, 32].Value = klient.DatumNaDogovor?.ToString("dd.MM.yyyy");
                worksheet.Cells[row, 33].Value = klient.DogovorVaziDo?.ToString("dd.MM.yyyy");
                worksheet.Cells[row, 34].Value = klient.BrojNaDogovor;
                worksheet.Cells[row, 35].Value = klient.DogovorOpredelenoNeopredeleno;
                worksheet.Cells[row, 36].Value = klient.PlateznaSmetka;
                worksheet.Cells[row, 37].Value = klient.Banka;
                worksheet.Cells[row, 38].Value = klient.RabotnaPozicija;
                worksheet.Cells[row, 39].Value = klient.OrganizacionaEdinica;
                worksheet.Cells[row, 40].Value = klient.Ekspozitura;
                worksheet.Cells[row, 41].Value = klient.ZadolzitelnoLicenca ? "Да" : "Не";
                worksheet.Cells[row, 42].Value = klient.BrojNaResenieOdASOZaLicenca;
                worksheet.Cells[row, 43].Value = klient.DatumNaResenieOdASOZaLicenca?.ToString("dd.MM.yyyy");
                worksheet.Cells[row, 44].Value = klient.DatumNaOdzemenaLicenca?.ToString("dd.MM.yyyy");
                worksheet.Cells[row, 45].Value = klient.BrojNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti;
                worksheet.Cells[row, 46].Value = klient.DatumNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti?.ToString("dd.MM.yyyy");
                worksheet.Cells[row, 47].Value = klient.ZivotNezivot;
                worksheet.Cells[row, 48].Value = klient.Nadreden;
                worksheet.Cells[row, 49].Value = klient.NadredenVaziOd?.ToString("dd.MM.yyyy");
                worksheet.Cells[row, 50].Value = klient.NadredenDo?.ToString("dd.MM.yyyy");
            }

            // Auto-fit columns
            worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();

            using var stream = new MemoryStream();
            package.SaveAs(stream);
            stream.Position = 0;

            return File(stream.ToArray(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                $"IzvestajKlienti_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx");
        }

        private async Task LoadKlienti()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using var connection = new SqlConnection(connectionString);
            await connection.OpenAsync();

            var query = @"
                select
                k.Ime as [Име],
                k.Prezime as [Презиме],
                k.Naziv as [Назив],
                k.EDB as [Единствен даночен број],
                k.MB as [Матичен број],
                k.EMBG as [ЕМБГ],
                lo.Opstina as [Општина од документ за идентицикација],
                k.UlicaOdDokumentZaIdentifikacija as [Улица од документ за идентификација],
                k.BrojOdDokumentZaIdentifikacija as [Број од документ за идентификација],
                lok.Opstina as [Општина за комуникација],
                k.UlicaZaKomunikacija as [Улица за комуникација],
                k.BrojZaKomunikacija as [Број за комуникација],
                k.DatumNaTekovnaSostojba as [Датум на тековна состојба],
                k.BrojPasosLicnaKarta as [Број на пасош / лична карта],
                k.DatumVaziOdPasosLicnaKarta as [Датум важи од],
                k.DatumVaziDoPasosLicnaKarta as [Датум важи до],
                ld.NazivDejnost as [Дејност],
                k.Email as [Е-маил],
                k.Tel as [Телефон],
                k.webstrana as [Веб страна],
                k.SoglasnostZaDirektenMarketing as [Согласност за директен маркетинг],
                k.SoglasnostZaEmailKomunikacija as [Согласност за Е-маил комуникација],
                k.SoglasnostZaTelKomunikacija as [Согласност за тел. комуникација],
                k.DatumNaPovlecenaSoglasnostZaDirektenMarketing as [Датум на повлеќена согласност за директен маркетинг],
                k.VistinskiSopstvenik as [Вистински сопственик],
                k.VistinskiSopstvenikIme as [Вистински сопственик Име],
                k.VistinskiSopstvenikPrezime as [Вистински сопственик Презиме],
                k.NositelNaJF as [Носител на јавна функција],
                k.OsnovZaNositelNaJF as [Основ за носител на јавна функција],
                k.ZasilenaAnaliza as [Засилена анализа],
                nnr.OpisNivoRizik as [Ниво на ризик],
                k.DaumNaDogovor as [Датум на договор],
                k.DogovorVaziDo as [Договор важи до],
                k.BrojNaDogovor as [Број на договор],
                k.DogovorOpredelenoNeopredeleno as [Договор определено / неопределено],
                k.PlateznaSmetka as [Платежна сметка],
                sb.Banka as [Банка],
                srp.RabotnaPozicija as [Работна позиција],
                sro.OrganizacionaEdinica as [Организациона единица],
                eks.Ime as [Експозитура],
                k.ZadolzitelnoLicenca as [Задолжително лиценца],
                k.BrojNaResenieOdASOZaLicenca as [Број на решение од АСО за лиценца],
                k.DatumNaResenieOdASOZaLicenca as [Датум на решение од АСО за лиценца],
                k.DatumNaOdzemenaLicenca as [Датум на одземање на лиценца],
                k.BrojNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti as [Број на дозвола за вршење Осигурително Брокерски работи],
                k.DatumNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti as [Датум на дозвола за вршење осигурително брокерски работи],
                k.ZivotNezivot as [Живот / Неживот],
                Coalesce (nadred.Ime,'') + ' ' + Coalesce(nadred.Prezime,'') as [Надреден],
                k.NadredenVaziOd as [Надреден важи од],
                k.NadredenDo as [Надреден важи до]

                from
                Klienti k
                left join ListaOpstini lo on lo.id = k.ListaOpstiniIdOpstinaOdDokumentZaIdentifikacija
                left join ListaOpstini lok on lok.id = k.ListaOpstiniIdOpstinaZaKomunikacija
                left join ListaDejnosti ld on ld.id = k.ListaDejnostiIdDejnost
                left join NivoaNaRizik nnr on nnr.id = k.NivoaNaRizikIdNivoNaRizik
                left join SifrarnikBanki sb on sb.id = k.SifrarnikBankiIdBanka
                left join SifrarnikRabotniPozicii srp on srp.id = k.SifrarnikRabotniPoziciiIdRabotnaPozicija
                left join SifrarnikOrganizacioniEdinici sro on sro.id = k.SifrarnikOrganizacioniEdiniciIdOrganizacionaEdinica
                left join Ekspozituri eks on eks.id = k.EkspozituriIdEkspozitura
                left join klienti nadred on nadred.id = k.SifrarnikRabotniPoziciiIdNadreden";

            // Add search filter if provided
            if (!string.IsNullOrEmpty(SearchTerm))
            {
                query += @" WHERE (
                    k.Ime LIKE @SearchTerm OR
                    k.Prezime LIKE @SearchTerm OR
                    k.Naziv LIKE @SearchTerm OR
                    k.EDB LIKE @SearchTerm OR
                    k.MB LIKE @SearchTerm OR
                    k.EMBG LIKE @SearchTerm OR
                    k.Email LIKE @SearchTerm OR
                    k.Tel LIKE @SearchTerm
                )";
            }

            // Add sorting
            if (!string.IsNullOrEmpty(SortColumn))
            {
                var columnMapping = new Dictionary<string, string>
                {
                    { "Ime", "k.Ime" },
                    { "Prezime", "k.Prezime" },
                    { "Naziv", "k.Naziv" },
                    { "EDB", "k.EDB" },
                    { "MB", "k.MB" },
                    { "EMBG", "k.EMBG" },
                    { "Email", "k.Email" },
                    { "Tel", "k.Tel" },
                    { "DatumNaTekovnaSostojba", "k.DatumNaTekovnaSostojba" },
                    { "DatumNaDogovor", "k.DaumNaDogovor" },
                    { "DogovorVaziDo", "k.DogovorVaziDo" }
                };

                if (columnMapping.ContainsKey(SortColumn))
                {
                    query += $" ORDER BY {columnMapping[SortColumn]} {(SortDirection == "desc" ? "DESC" : "ASC")}";
                }
            }
            else
            {
                query += " ORDER BY k.Ime, k.Prezime, k.Naziv";
            }

            using var command = new SqlCommand(query, connection);
            if (!string.IsNullOrEmpty(SearchTerm))
            {
                command.Parameters.AddWithValue("@SearchTerm", $"%{SearchTerm}%");
            }

            using var reader = await command.ExecuteReaderAsync();
            var klienti = new List<KlientData>();

            while (await reader.ReadAsync())
            {
                klienti.Add(new KlientData
                {
                    Ime = reader["Име"]?.ToString(),
                    Prezime = reader["Презиме"]?.ToString(),
                    Naziv = reader["Назив"]?.ToString(),
                    EDB = reader["Единствен даночен број"]?.ToString(),
                    MB = reader["Матичен број"]?.ToString(),
                    EMBG = reader["ЕМБГ"]?.ToString(),
                    OpstinaOdDokument = reader["Општина од документ за идентицикација"]?.ToString(),
                    UlicaOdDokument = reader["Улица од документ за идентификација"]?.ToString(),
                    BrojOdDokument = reader["Број од документ за идентификација"]?.ToString(),
                    OpstinaZaKomunikacija = reader["Општина за комуникација"]?.ToString(),
                    UlicaZaKomunikacija = reader["Улица за комуникација"]?.ToString(),
                    BrojZaKomunikacija = reader["Број за комуникација"]?.ToString(),
                    DatumNaTekovnaSostojba = reader["Датум на тековна состојба"] as DateTime?,
                    BrojPasosLicnaKarta = reader["Број на пасош / лична карта"]?.ToString(),
                    DatumVaziOdPasosLicnaKarta = reader["Датум важи од"] as DateTime?,
                    DatumVaziDoPasosLicnaKarta = reader["Датум важи до"] as DateTime?,
                    Dejnost = reader["Дејност"]?.ToString(),
                    Email = reader["Е-маил"]?.ToString(),
                    Tel = reader["Телефон"]?.ToString(),
                    WebStrana = reader["Веб страна"]?.ToString(),
                    SoglasnostZaDirektenMarketing = GetBooleanValue(reader["Согласност за директен маркетинг"]),
                    SoglasnostZaEmailKomunikacija = GetBooleanValue(reader["Согласност за Е-маил комуникација"]),
                    SoglasnostZaTelKomunikacija = GetBooleanValue(reader["Согласност за тел. комуникација"]),
                    DatumNaPovlecenaSoglasnostZaDirektenMarketing = reader["Датум на повлеќена согласност за директен маркетинг"] as DateTime?,
                    VistinskiSopstvenik = GetBooleanValue(reader["Вистински сопственик"]),
                    VistinskiSopstvenikIme = reader["Вистински сопственик Име"]?.ToString(),
                    VistinskiSopstvenikPrezime = reader["Вистински сопственик Презиме"]?.ToString(),
                    NositelNaJF = GetBooleanValue(reader["Носител на јавна функција"]),
                    OsnovZaNositelNaJF = reader["Основ за носител на јавна функција"]?.ToString(),
                    ZasilenaAnaliza = GetBooleanValue(reader["Засилена анализа"]),
                    NivoNaRizik = reader["Ниво на ризик"]?.ToString(),
                    DatumNaDogovor = reader["Датум на договор"] as DateTime?,
                    DogovorVaziDo = reader["Договор важи до"] as DateTime?,
                    BrojNaDogovor = reader["Број на договор"]?.ToString(),
                    DogovorOpredelenoNeopredeleno = reader["Договор определено / неопределено"]?.ToString(),
                    PlateznaSmetka = reader["Платежна сметка"]?.ToString(),
                    Banka = reader["Банка"]?.ToString(),
                    RabotnaPozicija = reader["Работна позиција"]?.ToString(),
                    OrganizacionaEdinica = reader["Организациона единица"]?.ToString(),
                    Ekspozitura = reader["Експозитура"]?.ToString(),
                    ZadolzitelnoLicenca = GetBooleanValue(reader["Задолжително лиценца"]),
                    BrojNaResenieOdASOZaLicenca = reader["Број на решение од АСО за лиценца"]?.ToString(),
                    DatumNaResenieOdASOZaLicenca = reader["Датум на решение од АСО за лиценца"] as DateTime?,
                    DatumNaOdzemenaLicenca = reader["Датум на одземање на лиценца"] as DateTime?,
                    BrojNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti = reader["Број на дозвола за вршење Осигурително Брокерски работи"]?.ToString(),
                    DatumNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti = reader["Датум на дозвола за вршење осигурително брокерски работи"] as DateTime?,
                    ZivotNezivot = reader["Живот / Неживот"]?.ToString(),
                    Nadreden = reader["Надреден"]?.ToString(),
                    NadredenVaziOd = reader["Надреден важи од"] as DateTime?,
                    NadredenDo = reader["Надреден важи до"] as DateTime?
                });
            }

            Klienti = klienti;
        }

        private static bool GetBooleanValue(object value)
        {
            if (value == null || value == DBNull.Value)
                return false;

            if (value is bool boolValue)
                return boolValue;

            if (value is string stringValue)
            {
                // Handle common string representations of boolean values
                return stringValue.Equals("1", StringComparison.OrdinalIgnoreCase) ||
                       stringValue.Equals("true", StringComparison.OrdinalIgnoreCase) ||
                       stringValue.Equals("да", StringComparison.OrdinalIgnoreCase) ||
                       stringValue.Equals("yes", StringComparison.OrdinalIgnoreCase);
            }

            if (value is int intValue)
                return intValue != 0;

            if (value is byte byteValue)
                return byteValue != 0;

            // For any other type, try to convert to boolean safely
            try
            {
                return Convert.ToBoolean(value);
            }
            catch
            {
                return false;
            }
        }
    }

    public class KlientData
    {
        public string? Ime { get; set; }
        public string? Prezime { get; set; }
        public string? Naziv { get; set; }
        public string? EDB { get; set; }
        public string? MB { get; set; }
        public string? EMBG { get; set; }
        public string? OpstinaOdDokument { get; set; }
        public string? UlicaOdDokument { get; set; }
        public string? BrojOdDokument { get; set; }
        public string? OpstinaZaKomunikacija { get; set; }
        public string? UlicaZaKomunikacija { get; set; }
        public string? BrojZaKomunikacija { get; set; }
        public DateTime? DatumNaTekovnaSostojba { get; set; }
        public string? BrojPasosLicnaKarta { get; set; }
        public DateTime? DatumVaziOdPasosLicnaKarta { get; set; }
        public DateTime? DatumVaziDoPasosLicnaKarta { get; set; }
        public string? Dejnost { get; set; }
        public string? Email { get; set; }
        public string? Tel { get; set; }
        public string? WebStrana { get; set; }
        public bool SoglasnostZaDirektenMarketing { get; set; }
        public bool SoglasnostZaEmailKomunikacija { get; set; }
        public bool SoglasnostZaTelKomunikacija { get; set; }
        public DateTime? DatumNaPovlecenaSoglasnostZaDirektenMarketing { get; set; }
        public bool VistinskiSopstvenik { get; set; }
        public string? VistinskiSopstvenikIme { get; set; }
        public string? VistinskiSopstvenikPrezime { get; set; }
        public bool NositelNaJF { get; set; }
        public string? OsnovZaNositelNaJF { get; set; }
        public bool ZasilenaAnaliza { get; set; }
        public string? NivoNaRizik { get; set; }
        public DateTime? DatumNaDogovor { get; set; }
        public DateTime? DogovorVaziDo { get; set; }
        public string? BrojNaDogovor { get; set; }
        public string? DogovorOpredelenoNeopredeleno { get; set; }
        public string? PlateznaSmetka { get; set; }
        public string? Banka { get; set; }
        public string? RabotnaPozicija { get; set; }
        public string? OrganizacionaEdinica { get; set; }
        public string? Ekspozitura { get; set; }
        public bool ZadolzitelnoLicenca { get; set; }
        public string? BrojNaResenieOdASOZaLicenca { get; set; }
        public DateTime? DatumNaResenieOdASOZaLicenca { get; set; }
        public DateTime? DatumNaOdzemenaLicenca { get; set; }
        public string? BrojNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti { get; set; }
        public DateTime? DatumNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti { get; set; }
        public string? ZivotNezivot { get; set; }
        public string? Nadreden { get; set; }
        public DateTime? NadredenVaziOd { get; set; }
        public DateTime? NadredenDo { get; set; }
    }
}