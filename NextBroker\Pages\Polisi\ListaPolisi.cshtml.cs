using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using Microsoft.Data.SqlClient;
using Microsoft.AspNetCore.Http;

namespace NextBroker.Pages.Polisi
{
    public class ListaPolisiModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;
        public bool PregledSitePolisi { get; set; }
        public string CurrentUsername { get; set; }

        public ListaPolisiModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        public class PolisaViewModel
        {
            public long Id { get; set; }
            public DateTime? DateCreated { get; set; }
            public string? UsernameCreated { get; set; }
            public DateTime? DateModified { get; set; }
            public string? UsernameModified { get; set; }
            public long KlientiIdOsiguritel { get; set; }
            public string? OsiguritelNaziv { get; set; }
            public int KlasiOsiguruvanjeIdKlasa { get; set; }
            public string? KlasaOsiguruvanje { get; set; }
            public int? ProduktiIdProizvod { get; set; }
            public string? ProizvodIme { get; set; }
            public string? BrojNaPolisa { get; set; }
            public long? BrojNaPonuda { get; set; }
            public long KlientiIdDogovoruvac { get; set; }
            public string? DogovoruvacNaziv { get; set; }
            public decimal? VkupnaPremija { get; set; }
            public long? KlientiIdOsigurenik { get; set; }
            public string? OsigurenikNaziv { get; set; }
            public bool Kolektivna { get; set; }
            public DateTime? DatumVaziOd { get; set; }
            public DateTime? DatumVaziDo { get; set; }
            public DateTime? DatumNaIzdavanje { get; set; }
            public decimal? PremijaZaNaplata { get; set; }
            public decimal? Uplateno { get; set; }
            public decimal? DolznaPremija { get; set; }
            public bool Storno { get; set; }
            public string? PricinaZaStorno { get; set; }
            public long? KlientiIdSorabotnik { get; set; }
            public string? SorabotnikNaziv { get; set; }
            public string? MaticenBroj { get; set; }
            public int? BrokerskaProvizijaSetting { get; set; }
        }

        public List<PolisaViewModel> Polisi { get; set; } = new();

        public async Task<IActionResult> OnGetAsync()
        {
            if (!await HasPageAccess("ListaPolisi"))
            {
                return RedirectToAccessDenied();
            }

            // Check if user has access to PregledSitePolisi privilege
            PregledSitePolisi = await HasPageAccess("PregledSitePolisi");
            CurrentUsername = HttpContext.Session.GetString("Username");

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                string sql = @"
                    SELECT
                        p.*,
                        o.Naziv as OsiguritelNaziv,
                        CONCAT(k.KlasaBroj, ' - ', k.KlasaIme) as KlasaOsiguruvanje,
                        pr.Ime as ProizvodIme,
                        CONCAT(d.Ime, ' ', d.Prezime, ' ', d.Naziv) AS DogovoruvacNaziv,
                        CONCAT(os.Ime, ' ', os.Prezime, ' ', os.Naziv) AS OsigurenikNaziv,
                        CONCAT(s.Ime, ' ', s.Prezime, ' ', s.Naziv) AS SorabotnikNaziv,
                        CONCAT(d.EMBG, ' ', d.MB) AS MaticenBroj,
                        dbo.VratiPolisaVkupnaPremijaZaPlakanjePoPolisaId(p.Id) as VkupnaPremija,
                        dbo.VratiDaliPolisaImaSetiranjeZaBrokerskaProvizija(p.Id) as BrokerskaProvizijaSetting
                    FROM Polisi p
                    LEFT JOIN Klienti o ON p.KlientiIdOsiguritel = o.Id
                    LEFT JOIN KlasiOsiguruvanje k ON p.KlasiOsiguruvanjeIdKlasa = k.Id
                    LEFT JOIN Produkti pr ON p.ProduktiIdProizvod = pr.Id
                    LEFT JOIN Klienti d ON p.KlientiIdDogovoruvac = d.Id
                    LEFT JOIN Klienti os ON p.KlientiIdOsigurenik = os.Id
                    LEFT JOIN Klienti s ON p.KlientiIdSorabotnik = s.Id";

                // Add WHERE clause only if user doesn't have PregledSitePolisi privilege
                if (!PregledSitePolisi && !string.IsNullOrEmpty(CurrentUsername))
                {
                    sql += @"
                    WHERE (p.UsernameCreated = @Username
                    OR p.KlientiIdSorabotnik = (SELECT dbo.VratiKlientiIdPoUsername(@Username))";

                    // Add additional conditions for ListaPolisiPrivilegii rules
                    sql += @"
                    OR EXISTS (
                        SELECT 1
                        FROM ListaPolisiPrivilegii lpp
                        WHERE lpp.PraviloZa = (SELECT dbo.VratiKlientiIdPoUsername(@Username))
                        AND (p.KlientiIdSorabotnik = lpp.GledaPolisiOd
                             OR p.UsernameCreated = (SELECT TOP 1 Username FROM users WHERE emb = (SELECT TOP 1 EMBG FROM klienti WHERE id = lpp.GledaPolisiOd))
                             OR p.UsernameCreated = (SELECT TOP 1 Username FROM users WHERE emb = (SELECT TOP 1 EMB FROM klienti WHERE id = lpp.GledaPolisiOd)))
                    ))";
                }

                sql += " ORDER BY p.DateCreated DESC";

                using (SqlCommand cmd = new SqlCommand(sql, connection))
                {
                    // Add parameter for Username if needed
                    if (!PregledSitePolisi && !string.IsNullOrEmpty(CurrentUsername))
                    {
                        cmd.Parameters.AddWithValue("@Username", CurrentUsername);
                    }

                    using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            var polisa = new PolisaViewModel
                            {
                                Id = reader.GetInt64(reader.GetOrdinal("Id")),
                                DateCreated = reader.IsDBNull(reader.GetOrdinal("DateCreated")) ? null : reader.GetDateTime(reader.GetOrdinal("DateCreated")),
                                UsernameCreated = reader.IsDBNull(reader.GetOrdinal("UsernameCreated")) ? null : reader.GetString(reader.GetOrdinal("UsernameCreated")),
                                DateModified = reader.IsDBNull(reader.GetOrdinal("DateModified")) ? null : reader.GetDateTime(reader.GetOrdinal("DateModified")),
                                UsernameModified = reader.IsDBNull(reader.GetOrdinal("UsernameModified")) ? null : reader.GetString(reader.GetOrdinal("UsernameModified")),
                                KlientiIdOsiguritel = reader.GetInt64(reader.GetOrdinal("KlientiIdOsiguritel")),
                                OsiguritelNaziv = reader.IsDBNull(reader.GetOrdinal("OsiguritelNaziv")) ? null : reader.GetString(reader.GetOrdinal("OsiguritelNaziv")),
                                KlasiOsiguruvanjeIdKlasa = reader.GetInt32(reader.GetOrdinal("KlasiOsiguruvanjeIdKlasa")),
                                KlasaOsiguruvanje = reader.IsDBNull(reader.GetOrdinal("KlasaOsiguruvanje")) ? null : reader.GetString(reader.GetOrdinal("KlasaOsiguruvanje")),
                                ProduktiIdProizvod = reader.IsDBNull(reader.GetOrdinal("ProduktiIdProizvod")) ? null : reader.GetInt32(reader.GetOrdinal("ProduktiIdProizvod")),
                                ProizvodIme = reader.IsDBNull(reader.GetOrdinal("ProizvodIme")) ? null : reader.GetString(reader.GetOrdinal("ProizvodIme")),
                                BrojNaPolisa = reader.IsDBNull(reader.GetOrdinal("BrojNaPolisa")) ? null : reader.GetString(reader.GetOrdinal("BrojNaPolisa")),
                                BrojNaPonuda = reader.IsDBNull(reader.GetOrdinal("BrojNaPonuda")) ? null : reader.GetInt64(reader.GetOrdinal("BrojNaPonuda")),
                                KlientiIdDogovoruvac = reader.GetInt64(reader.GetOrdinal("KlientiIdDogovoruvac")),
                                DogovoruvacNaziv = reader.IsDBNull(reader.GetOrdinal("DogovoruvacNaziv")) ? null : reader.GetString(reader.GetOrdinal("DogovoruvacNaziv")),
                                VkupnaPremija = reader.IsDBNull(reader.GetOrdinal("VkupnaPremija")) ? null : reader.GetDecimal(reader.GetOrdinal("VkupnaPremija")),
                                KlientiIdOsigurenik = reader.IsDBNull(reader.GetOrdinal("KlientiIdOsigurenik")) ? null : reader.GetInt64(reader.GetOrdinal("KlientiIdOsigurenik")),
                                OsigurenikNaziv = reader.IsDBNull(reader.GetOrdinal("OsigurenikNaziv")) ? null : reader.GetString(reader.GetOrdinal("OsigurenikNaziv")),
                                Kolektivna = !reader.IsDBNull(reader.GetOrdinal("Kolektivna")) && reader.GetBoolean(reader.GetOrdinal("Kolektivna")),
                                DatumVaziOd = reader.IsDBNull(reader.GetOrdinal("DatumVaziOd")) ? null : reader.GetDateTime(reader.GetOrdinal("DatumVaziOd")),
                                DatumVaziDo = reader.IsDBNull(reader.GetOrdinal("DatumVaziDo")) ? null : reader.GetDateTime(reader.GetOrdinal("DatumVaziDo")),
                                DatumNaIzdavanje = reader.IsDBNull(reader.GetOrdinal("DatumNaIzdavanje")) ? null : reader.GetDateTime(reader.GetOrdinal("DatumNaIzdavanje")),
                                PremijaZaNaplata = reader.IsDBNull(reader.GetOrdinal("PremijaZaNaplata")) ? null : reader.GetDecimal(reader.GetOrdinal("PremijaZaNaplata")),
                                Uplateno = reader.IsDBNull(reader.GetOrdinal("Uplateno")) ? null : reader.GetDecimal(reader.GetOrdinal("Uplateno")),
                                DolznaPremija = reader.IsDBNull(reader.GetOrdinal("DolznaPremija")) ? null : reader.GetDecimal(reader.GetOrdinal("DolznaPremija")),
                                Storno = !reader.IsDBNull(reader.GetOrdinal("Storno")) && reader.GetBoolean(reader.GetOrdinal("Storno")),
                                PricinaZaStorno = reader.IsDBNull(reader.GetOrdinal("PricinaZaStorno")) ? null : reader.GetString(reader.GetOrdinal("PricinaZaStorno")),
                                KlientiIdSorabotnik = reader.IsDBNull(reader.GetOrdinal("KlientiIdSorabotnik")) ? null : reader.GetInt64(reader.GetOrdinal("KlientiIdSorabotnik")),
                                SorabotnikNaziv = reader.IsDBNull(reader.GetOrdinal("SorabotnikNaziv")) ? null : reader.GetString(reader.GetOrdinal("SorabotnikNaziv")),
                                MaticenBroj = reader.IsDBNull(reader.GetOrdinal("MaticenBroj")) ? null : reader.GetString(reader.GetOrdinal("MaticenBroj")),
                                BrokerskaProvizijaSetting = reader.IsDBNull(reader.GetOrdinal("BrokerskaProvizijaSetting")) ? null : reader.GetInt32(reader.GetOrdinal("BrokerskaProvizijaSetting"))
                            };
                            Polisi.Add(polisa);
                        }
                    }
                }
            }

            return Page();
        }
    }
}
