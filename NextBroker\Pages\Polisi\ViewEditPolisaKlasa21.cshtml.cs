using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Renci.SshNet;
using System.IO;
using System.ComponentModel.DataAnnotations;
using System.Reflection;
using System.Runtime.Serialization;

namespace NextBroker.Pages.Polisi
{
    public class ViewEditPolisaKlasa21Model : SecurePageModel
    {
        private readonly IConfiguration _configuration;
        public bool HasAdminAccess { get; private set; }
        public bool HasStornoAccess { get; private set; }

        public ViewEditPolisaKlasa21Model(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        [BindProperty]
        public PolisaViewModel Input { get; set; } = new();

        public IEnumerable<SelectListItem> Osiguriteli { get; set; }
        public IEnumerable<SelectListItem> KlasiOsiguruvanje { get; set; }
        public IEnumerable<SelectListItem> Produkti { get; set; }
        public IEnumerable<SelectListItem> Valuti { get; set; }
        public IEnumerable<SelectListItem> NaciniNaPlakanje { get; set; }
        public IEnumerable<SelectListItem> TipoviNaPlakanje { get; set; }
        public IEnumerable<SelectListItem> Banki { get; set; }
        public IEnumerable<SelectListItem> InvesticiskiFondovi { get; set; }
        public IEnumerable<SelectListItem> InvesticiskaDrushtva { get; set; }
        public IEnumerable<SelectListItem> TipoviNaFaktura { get; set; }

        public class PolisaViewModel
        {
            public long Id { get; set; }
            public DateTime? DateCreated { get; set; }
            public string UsernameCreated { get; set; }
            public DateTime? DateModified { get; set; }
            public string UsernameModified { get; set; }
            public long? KlientiIdOsiguritel { get; set; }
            public int? KlasiOsiguruvanjeIdKlasa { get; set; }
            public int? ProduktiIdProizvod { get; set; }
            public string? BrojNaPolisa { get; set; }
            public long? BrojNaPonuda { get; set; }
            public long? KlientiIdDogovoruvac { get; set; }
            public long? KlientiIdOsigurenik { get; set; }
            public bool Kolektivna { get; set; }
            public bool KolektivnaNeodredenBrOsigurenici { get; set; }
            public string NeodredenBrOsigureniciZabeleska { get; set; }
            public DateTime? DatumVaziOd { get; set; }
            public DateTime? DatumVaziDo { get; set; }
            public DateTime? DatumNaIzdavanje { get; set; }
            public int? VremetraenjeNaPolisa { get; set; }
            public int? PeriodNaUplata { get; set; }
            public long SifrarnikValutiIdValuta { get; set; }
            public long? SifrarnikValutiIdFranshizaValuta { get; set; }
            public long? KlientiIdSorabotnik { get; set; }
            public bool Faktoring { get; set; }
            public decimal? ProcentFranshiza { get; set; }
            public decimal? ProcentFinansiski { get; set; }
            public decimal? KoregiranaStapkaNaProvizija { get; set; }
            public long? SifrarnikNacinNaPlakjanjeId { get; set; }
            public string TipNaFaktura { get; set; }
            public string BrojNaFakturaVlezna { get; set; }
            public DateTime? DatumNaFakturaVlezna { get; set; }
            public DateTime? RokNaPlakjanjeFakturaVlezna { get; set; }
            public long? SifrarnikTipNaPlakanjeId { get; set; }
            public long? SifrarnikBankiIdBanka { get; set; }
            public bool GeneriranaFakturaIzlezna { get; set; }
            public string BrojNaFakturaIzlezna { get; set; }
            public DateTime? DatumNaIzleznaFaktura { get; set; }
            public DateTime? RokNaPlakjanjeFakturaIzlezna { get; set; }
            public bool Storno { get; set; }
            public string PricinaZaStorno { get; set; }
            public string Zabeleska { get; set; }
            [Display(Name = "Франшиза износ")]
            public decimal? FranshizaIznos { get; set; }
        }

        // Add new class for Klasa21 data
        public class PolisaKlasa21ViewModel
        {
            public DateTime? DateCreated { get; set; }
            public string UsernameCreated { get; set; }
            public DateTime? DateModified { get; set; }
            public string UsernameModified { get; set; }
            public decimal? ProcentNaPopustZaFakturaVoRok { get; set; }
            public decimal? IznosZaPlakjanjeVoRok { get; set; }
            public decimal? ProcentKomercijalenPopust { get; set; }
            public decimal? ProcentFinansiski { get; set; }
            public decimal? PremijaZaNaplata { get; set; }
            public long ShifrarnikInvesticiskiFondIdInvesticiskiFond { get; set; }
            public long NazivNaInvesticiskoDrushtvo { get; set; }
            public string NazivNaInvesticiskiFond { get; set; }
            public string IdentifikaciskiBrojaNaInvesticiskiFond { get; set; }
            public decimal? ProcentOdPremijaPoIFGodishnoEdnokratno { get; set; }
            public decimal? ProcentOdPremijaVoOsiguruvanjeGodishnoEdnokratno { get; set; }
            public decimal? IznosOdPremijaPoIFGodishnoEdnokratno { get; set; }
            public decimal? IznosOdPremijaVoOsiguruvanjeGodishnoEdnokratno { get; set; }
            public decimal? VkupenIznosNaPremijaZaOsiguruvanje { get; set; }
            public decimal? VkupenIznosNaPremijaZaIF { get; set; }
            public decimal? VkupenIznosNaTroshociOdPremija { get; set; }
            public string OsigurenaSumaZaDozhivuvanje { get; set; }
            public string OsigurenaSumaZaSmrtOdBolest { get; set; }
            public string OsigurenaSumaZaSmrtOdNezgoda { get; set; }
            public string PrivremenaOsiguritelnaZastita { get; set; }
            public string PremijaGodishnaEdnokratna { get; set; }
            public long? BrojNaUdeliPoIF { get; set; }
            public decimal? VkupnaPremijaGodishna { get; set; }
            public decimal? VkupnaPremijaEdnokratna { get; set; }
            public decimal? DoplatociZaPodgodishnoPlakjanje { get; set; }
        }

        [BindProperty]
        public PolisaKlasa21ViewModel Klasa21Input { get; set; } = new();

        // Add classes for supplementary insurance data
        public class PolisaKlasa21DopolnitelnoKlasa1ViewModel
        {
            public decimal? OsigurenaSumaZaSmrtOdNezgoda { get; set; }
            public decimal? OsigurenaSumaZa100ProcTraenInvaliditet { get; set; }
            public decimal? OsigurenaSumaZaTraenInvaliditet { get; set; }
            public decimal? DnevenNadomest { get; set; }
            public decimal? PremijaNezgodaGodishna { get; set; }
        }

        public class PolisaKlasa21DopolnitelnoKlasa2ViewModel
        {
            public decimal? OsigurenaSumaZaTeskoBolniSostojbi { get; set; }
            public decimal? PremijaTeshkoBolniSostojbiGodishna { get; set; }
            public decimal? OsigurenaSumaZaOperacii { get; set; }
            public decimal? PremijaZaOperaciiGodishna { get; set; }
            public decimal? OsigurenaSumaZaTrajnaNesposobnost { get; set; }
            public decimal? PremijaZaTrajnaNesposobnostGodishna { get; set; }
            public decimal? OsigurenaSumaZaHirushkiIntervencii { get; set; }
            public decimal? PremijaZaHirushkiIntervenciiGodishna { get; set; }
        }

        [BindProperty]
        public List<PolisaKlasa21DopolnitelnoKlasa1ViewModel> Klasa1Entries { get; set; } = new();

        [BindProperty]
        public List<PolisaKlasa21DopolnitelnoKlasa2ViewModel> Klasa2Entries { get; set; } = new();

        // Add after other properties
        public class FileInfo
        {
            public long Id { get; set; }
            public string FileName { get; set; }
            public DateTime DateCreated { get; set; }
            public string UsernameCreated { get; set; }
        }

        public List<FileInfo> Files { get; set; }

        public System.Data.DataTable KarticaData { get; set; }

        public class ZadolzuvanjeInfo
        {
            public string Zadolzen { get; set; }
            public DateTime? DatumNaZadolzuvanje { get; set; }
            public string OsnovZaRazdolzuvanje { get; set; }
            public bool PotvrdenoRazdolzuvanjeKajBroker { get; set; }
            public DateTime? DatumNaRazdolzuvanjeKajBroker { get; set; }
            public bool PotvrdenoRazdolzuvanjeKajOsiguritel { get; set; }
            public DateTime? DatumNaRazdolzuvanjeKajOsiguritel { get; set; }
        }

        public ZadolzuvanjeInfo ZadolzuvanjeData { get; set; }

        // OZ Information properties
        public decimal OZIznosIzleznaFakturaPremija { get; set; }
        public decimal OZIznosPolisa { get; set; }

        // New properties for database function results
        public string DogovoruvacEMNGMB { get; set; }
        public string OsigurenikEMNGMB { get; set; }

        private async Task LoadOsiguriteli()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Naziv
                    FROM Klienti
                    WHERE Osiguritel = 1
                    AND ZivotNezivot = N'Живот'
                    AND DogovorVaziDo > Convert(date,GETDATE())
                    ORDER BY Naziv", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Naziv"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Osiguriteli = items;
                }
            }
        }

        private async Task LoadKlasiOsiguruvanje()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, CONCAT(KlasaBroj, ' - ', KlasaIme) as DisplayName
                    FROM KlasiOsiguruvanje
                    WHERE KlasaBroj = 21
                    AND (Disabled = 0 OR Disabled IS NULL)", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["DisplayName"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    KlasiOsiguruvanje = items;
                }
            }
        }

        private async Task LoadProdukti()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Ime
                    FROM Produkti
                    ORDER BY Ime", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Ime"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Produkti = items;
                }
            }
        }

        private async Task LoadValuti()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Valuta
                    FROM SifrarnikValuti
                    ORDER BY Valuta", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Valuta"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Valuti = items;
                }
            }
        }

        private async Task LoadNaciniNaPlakanje()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, NacinNaPlakanje
                    FROM SifrarnikNacinNaPlakanje
                    ORDER BY NacinNaPlakanje", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["NacinNaPlakanje"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    NaciniNaPlakanje = items;
                }
            }
        }

        private async Task LoadTipoviNaPlakanje()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, TipNaPlakanje
                    FROM SifrarnikTipNaPlakanje
                    ORDER BY TipNaPlakanje", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["TipNaPlakanje"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    TipoviNaPlakanje = items;
                }
            }
        }

        private async Task LoadBanki()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Banka
                    FROM SifrarnikBanki
                    ORDER BY Banka", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Banka"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Banki = items;
                }
            }
        }

        private void LoadTipoviNaFaktura()
        {
            var items = new List<SelectListItem>
            {
                new SelectListItem("Фактура за премија", "Фактура за премија"),
                new SelectListItem("Фактура за провизија", "Фактура за провизија"),
                new SelectListItem("Фактура за штета", "Фактура за штета")
            };
            TipoviNaFaktura = items;
        }

        private async Task LoadInvesticiskiFondovi()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT f.Id, CONCAT(f.Naziv, ' - ', d.Naziv) as DisplayName
                    FROM ShifrarnikInvesticiskiFond f
                    INNER JOIN ShifrarnikInvesticiskoDrushtvo d ON f.ShifrarnikInvesticiskoDrushtvoIdDrushtvo = d.Id
                    ORDER BY f.Naziv", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["DisplayName"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    InvesticiskiFondovi = items;
                }
            }
        }

        private async Task LoadInvesticiskaDrushtva()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Naziv
                    FROM ShifrarnikInvesticiskoDrushtvo
                    ORDER BY Naziv", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Naziv"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    InvesticiskaDrushtva = items;
                }
            }
        }

        private async Task LoadFiles()
        {
            Files = new List<FileInfo>();
            string connectionString = _configuration.GetConnectionString("DefaultConnection");

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand command = new SqlCommand(
                    "SELECT Id, FileName, DateCreated, UsernameCreated " +
                    "FROM PolisiFileSystem " +
                    "WHERE PolisaId = @PolisaId " +
                    "ORDER BY DateCreated DESC", connection))
                {
                    command.Parameters.AddWithValue("@PolisaId", Input.Id);

                    using (SqlDataReader reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            Files.Add(new FileInfo
                            {
                                Id = reader.GetInt64(0),
                                FileName = reader.GetString(1),
                                DateCreated = reader.GetDateTime(2),
                                UsernameCreated = reader.GetString(3)
                            });
                        }
                    }
                }
            }
        }

        private async Task LoadKarticaData(long polisaId)
        {
            using (SqlConnection conn = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await conn.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT
                        [ID],
                        [Рата број],
                        [Датум на доспевање],
                        [Износ на рата],
                        [Датум на уплата],
                        [Уплатен износ],
                        [Затворена рата],
                        [Сторно]
                    FROM PolisaKartica
                    WHERE PolisaID = @PolisaID
                    ORDER BY [Рата број]", conn))
                {
                    cmd.Parameters.AddWithValue("@PolisaID", polisaId);
                    using (SqlDataAdapter adapter = new SqlDataAdapter(cmd))
                    {
                        KarticaData = new System.Data.DataTable();
                        adapter.Fill(KarticaData);
                    }
                }
            }
        }

        private async Task LoadZadolzuvanjeData()
        {
            if (string.IsNullOrEmpty(Input.BrojNaPolisa)) return;

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT
                        CAST(
                            COALESCE(kl.Ime, '') + ' ' +
                            COALESCE(kl.Prezime, '') + ' ' +
                            COALESCE(kl.Naziv, '')
                        AS VARCHAR(MAX)) AS Zadolzen,
                        pzr.DatumNaZadolzuvanje,
                        sozr.OsnovZaRazdolzuvanje,
                        pzr.PotvrdenoRazdolzuvanjeKajBroker,
                        pzr.DatumNaRazdolzuvanjeKajBroker,
                        pzr.PotvrdenoRazdolzuvanjeKajOsiguritel,
                        pzr.DatumNaRazdolzuvanjeKajOsiguritel
                    FROM PolisiZadolzuvanjeRazdolzuvanje pzr
                    LEFT JOIN Klienti kl on pzr.KlientiIdZadolzen = kl.Id
                    LEFT JOIN SifrarnikOsnovZaRazdolzuvanje sozr on pzr.SifrarnikOsnovZaRazdolzuvanjeId = sozr.Id
                    LEFT JOIN Polisi p on pzr.BrojNaPolisa = p.BrojNaPolisa
                    WHERE pzr.BrojNaPolisa = @brojnapolisa
                    AND (p.Storno is null or p.Storno != 1)", connection))
                {
                    cmd.Parameters.AddWithValue("@brojnapolisa", Input.BrojNaPolisa);
                    using (var reader = await cmd.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                            ZadolzuvanjeData = new ZadolzuvanjeInfo
                            {
                                Zadolzen = reader.IsDBNull(0) ? null : reader.GetString(0),
                                DatumNaZadolzuvanje = reader.IsDBNull(1) ? null : reader.GetDateTime(1),
                                OsnovZaRazdolzuvanje = reader.IsDBNull(2) ? null : reader.GetString(2),
                                PotvrdenoRazdolzuvanjeKajBroker = reader.IsDBNull(3) ? false : reader.GetBoolean(3),
                                DatumNaRazdolzuvanjeKajBroker = reader.IsDBNull(4) ? null : reader.GetDateTime(4),
                                PotvrdenoRazdolzuvanjeKajOsiguritel = reader.IsDBNull(5) ? false : reader.GetBoolean(5),
                                DatumNaRazdolzuvanjeKajOsiguritel = reader.IsDBNull(6) ? null : reader.GetDateTime(6)
                            };
                        }
                    }
                }
            }
        }

        private async Task LoadOZInformation()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                try
                {
                    // Load OZ Iznos Izlezna Faktura Premija
                    using (SqlCommand cmd = new SqlCommand("SELECT dbo.VraziOZIznosIzleznaFakturaPremija((select top 1 BrojNaFakturaIzlezna from polisi where id = @polisaId order by DateCreated Desc))", connection))
                    {
                        cmd.Parameters.AddWithValue("@polisaId", Input.Id);
                        var result = await cmd.ExecuteScalarAsync();
                        OZIznosIzleznaFakturaPremija = result != null && result != DBNull.Value ? Convert.ToDecimal(result) : 0;
                    }

                    // Load OZ Iznos Polisa
                    using (SqlCommand cmd = new SqlCommand("SELECT dbo.VraziOZIznosPolisa(@polisaId)", connection))
                    {
                        cmd.Parameters.AddWithValue("@polisaId", Input.Id);
                        var result = await cmd.ExecuteScalarAsync();
                        OZIznosPolisa = result != null && result != DBNull.Value ? Convert.ToDecimal(result) : 0;
                    }
                }
                catch (Exception ex)
                {
                    // Log the error or handle it appropriately
                    Console.WriteLine($"Error loading OZ information: {ex.Message}");
                    OZIznosIzleznaFakturaPremija = 0;
                    OZIznosPolisa = 0;
                }
            }
        }

        private async Task LoadDogovoruvacOsigurenikEMNGMB()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                try
                {
                    // Load Dogovoruvac EMNMGB
                    using (SqlCommand cmd = new SqlCommand("SELECT dbo.VratiPolisaDogovoruvacEMNGMB(@polisaId)", connection))
                    {
                        cmd.Parameters.AddWithValue("@polisaId", Input.Id);
                        var result = await cmd.ExecuteScalarAsync();
                        DogovoruvacEMNGMB = result != null && result != DBNull.Value ? result.ToString() : null;
                    }

                    // Load Osigurenik EMNMGB
                    using (SqlCommand cmd = new SqlCommand("SELECT dbo.VratiPolisaOsigurenikEMNGMB(@polisaId)", connection))
                    {
                        cmd.Parameters.AddWithValue("@polisaId", Input.Id);
                        var result = await cmd.ExecuteScalarAsync();
                        OsigurenikEMNGMB = result != null && result != DBNull.Value ? result.ToString() : null;
                    }
                }
                catch (Exception ex)
                {
                    // Log the error or handle it appropriately
                    Console.WriteLine($"Error loading Dogovoruvac/Osigurenik EMNMGB: {ex.Message}");
                    DogovoruvacEMNGMB = null;
                    OsigurenikEMNGMB = null;
                }
            }
        }

        public async Task<JsonResult> OnGetSearchKlienti(string mb)
        {
            if (string.IsNullOrWhiteSpace(mb))
                return new JsonResult(new List<object>());

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            var results = new List<object>();

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT TOP 10
                        Id,
                        ISNULL(MB, '') as MB,
                        ISNULL(EDB, '') as EDB,
                        ISNULL(EMBG, '') as EMBG,
                        CASE
                            WHEN KlientFizickoPravnoLice = 'P' THEN Naziv
                            ELSE CONCAT(ISNULL(Ime, ''), ' ', ISNULL(Prezime, ''))
                        END as Naziv,
                        KlientFizickoPravnoLice,
                        ISNULL(Ime, '') as Ime,
                        ISNULL(Prezime, '') as Prezime
                    FROM Klienti
                    WHERE MB LIKE @Search + '%'
                       OR EDB LIKE @Search + '%'
                       OR EMBG LIKE @Search + '%'
                       OR Naziv LIKE '%' + @Search + '%'
                       OR Ime LIKE '%' + @Search + '%'
                       OR Prezime LIKE '%' + @Search + '%'
                    ORDER BY
                        CASE
                            WHEN MB LIKE @Search + '%' THEN 1
                            WHEN EDB LIKE @Search + '%' THEN 2
                            WHEN EMBG LIKE @Search + '%' THEN 3
                            ELSE 4
                        END,
                        MB, Naziv", connection))
                {
                    cmd.Parameters.AddWithValue("@Search", mb);
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            id = reader["Id"].ToString(),
                            naziv = reader["Naziv"].ToString(),
                            mb = reader["MB"].ToString(),
                            edb = reader["EDB"].ToString(),
                            embg = reader["EMBG"].ToString(),
                            fizickoPravno = reader["KlientFizickoPravnoLice"].ToString(),
                            ime = reader["Ime"].ToString(),
                            prezime = reader["Prezime"].ToString()
                        });
                    }
                }
            }

            return new JsonResult(results);
        }

        public async Task<IActionResult> OnGetAsync(long id)
        {
            if (!await HasPageAccess("ViewEditPolisaKlasa21"))
            {
                return RedirectToAccessDenied();
            }

            // Check admin access
            HasAdminAccess = await HasPageAccess("ViewEditPolisaKlasa21Admin");

            // Check storno access
            HasStornoAccess = await HasPageAccess("StorniranjePolisi");

            await LoadOsiguriteli();
            await LoadKlasiOsiguruvanje();
            await LoadProdukti();
            await LoadValuti();
            await LoadNaciniNaPlakanje();
            await LoadTipoviNaPlakanje();
            await LoadBanki();
            LoadTipoviNaFaktura();
            await LoadInvesticiskiFondovi();
            await LoadInvesticiskaDrushtva();

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT p.Id, p.DateCreated, p.UsernameCreated, p.DateModified, p.UsernameModified,
           p.KlientiIdOsiguritel, p.KlasiOsiguruvanjeIdKlasa, p.ProduktiIdProizvod,
           p.BrojNaPolisa, p.BrojNaPonuda, p.KlientiIdDogovoruvac, p.KlientiIdOsigurenik,
           p.Kolektivna, p.KolektivnaNeodredenBrOsigurenici, p.NeodredenBrOsigureniciZabeleska,
           p.DatumVaziOd, p.DatumVaziDo, p.DatumNaIzdavanje, p.VremetraenjeNaPolisa, p.PeriodNaUplata,
           p.SifrarnikValutiIdValuta, p.KlientiIdSorabotnik, p.Faktoring, p.SifrarnikValutiIdFranshizaValuta,
           p.ProcentFranshiza, p.ProcentFinansiski, p.KoregiranaStapkaNaProvizija,
           p.SifrarnikNacinNaPlakjanjeId,
           p.TipNaFaktura, p.BrojNaFakturaVlezna,
           p.DatumNaFakturaVlezna, p.RokNaPlakjanjeFakturaVlezna,
           p.SifrarnikTipNaPlakanjeId,
           p.SifrarnikBankiIdBanka,
           p.GeneriranaFakturaIzlezna, p.BrojNaFakturaIzlezna,
           p.DatumNaIzleznaFaktura, p.RokNaPlakjanjeFakturaIzlezna,
           p.Storno, p.PricinaZaStorno, p.Zabeleska, p.FranshizaIznos,
           CASE
               WHEN k.KlientFizickoPravnoLice = 'P' THEN k.Naziv
               ELSE CONCAT(ISNULL(k.Ime, ''), ' ', ISNULL(k.Prezime, ''))
           END as DogovoruvacNaziv,
           CASE
               WHEN k2.KlientFizickoPravnoLice = 'P' THEN k2.Naziv
               ELSE CONCAT(ISNULL(k2.Ime, ''), ' ', ISNULL(k2.Prezime, ''))
           END as OsigurenikNaziv,
           CASE
               WHEN k3.KlientFizickoPravnoLice = 'P' THEN k3.Naziv
               ELSE CONCAT(ISNULL(k3.Ime, ''), ' ', ISNULL(k3.Prezime, ''))
           END as SorabotnikNaziv
    FROM Polisi p
    LEFT JOIN Klienti k ON p.KlientiIdDogovoruvac = k.Id
    LEFT JOIN Klienti k2 ON p.KlientiIdOsigurenik = k2.Id
    LEFT JOIN Klienti k3 ON p.KlientiIdSorabotnik = k3.Id
    WHERE p.Id = @Id", connection))
                {
                    cmd.Parameters.AddWithValue("@Id", id);
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    if (await reader.ReadAsync())
                    {
                        Input.Id = reader.GetInt64(0);
                        Input.DateCreated = reader.IsDBNull(1) ? null : reader.GetDateTime(1);
                        Input.UsernameCreated = reader.GetString(2);
                        Input.DateModified = reader.IsDBNull(3) ? null : reader.GetDateTime(3);
                        Input.UsernameModified = reader.IsDBNull(4) ? null : reader.GetString(4);
                        Input.KlientiIdOsiguritel = reader.IsDBNull(5) ? null : reader.GetInt64(5);
                        Input.KlasiOsiguruvanjeIdKlasa = reader.IsDBNull(6) ? null : reader.GetInt32(6);
                        Input.ProduktiIdProizvod = reader.IsDBNull(7) ? null : reader.GetInt32(7);
                        Input.BrojNaPolisa = reader.IsDBNull(8) ? null : reader.GetString(8);
                        Input.BrojNaPonuda = reader.IsDBNull(9) ? null : reader.GetInt64(9);
                        Input.KlientiIdDogovoruvac = reader.IsDBNull(10) ? null : reader.GetInt64(10);
                        Input.KlientiIdOsigurenik = reader.IsDBNull(11) ? null : reader.GetInt64(11);
                        Input.Kolektivna = !reader.IsDBNull(12) && reader.GetBoolean(12);
                        Input.KolektivnaNeodredenBrOsigurenici = !reader.IsDBNull(13) && reader.GetBoolean(13);
                        Input.NeodredenBrOsigureniciZabeleska = reader.IsDBNull(14) ? null : reader.GetString(14);
                        Input.DatumVaziOd = reader.IsDBNull(15) ? null : reader.GetDateTime(15);
                        Input.DatumVaziDo = reader.IsDBNull(16) ? null : reader.GetDateTime(16);
                        Input.DatumNaIzdavanje = reader.IsDBNull(17) ? null : reader.GetDateTime(17);
                        Input.VremetraenjeNaPolisa = reader.IsDBNull(18) ? null : reader.GetInt32(18);
                        Input.PeriodNaUplata = reader.IsDBNull(19) ? null : reader.GetInt32(19);
                        Input.SifrarnikValutiIdValuta = reader.IsDBNull(20) ? 0 : reader.GetInt64(20);
                        Input.KlientiIdSorabotnik = reader.IsDBNull(21) ? null : reader.GetInt64(21);
                        Input.Faktoring = !reader.IsDBNull(22) && reader.GetBoolean(22);
                        Input.SifrarnikValutiIdFranshizaValuta = reader.IsDBNull(23) ? null : reader.GetInt64(23);
                        Input.ProcentFranshiza = reader.IsDBNull(24) ? null : reader.GetDecimal(24);
                        Input.ProcentFinansiski = reader.IsDBNull(25) ? null : reader.GetDecimal(25);
                        Input.KoregiranaStapkaNaProvizija = reader.IsDBNull(26) ? null : reader.GetDecimal(26);
                        Input.SifrarnikNacinNaPlakjanjeId = reader.IsDBNull(27) ? null : reader.GetInt64(27);
                        Input.TipNaFaktura = reader.IsDBNull(28) ? null : reader.GetString(28);
                        Input.BrojNaFakturaVlezna = reader.IsDBNull(29) ? null : reader.GetString(29);
                        Input.DatumNaFakturaVlezna = reader.IsDBNull(30) ? null : reader.GetDateTime(30);
                        Input.RokNaPlakjanjeFakturaVlezna = reader.IsDBNull(31) ? null : reader.GetDateTime(31);
                        Input.SifrarnikTipNaPlakanjeId = reader.IsDBNull(32) ? null : reader.GetInt64(32);
                        Input.SifrarnikBankiIdBanka = reader.IsDBNull(33) ? null : reader.GetInt64(33);
                        Input.GeneriranaFakturaIzlezna = !reader.IsDBNull(34) && reader.GetBoolean(34);
                        Input.BrojNaFakturaIzlezna = reader.IsDBNull(35) ? null : reader.GetString(35);
                        Input.DatumNaIzleznaFaktura = reader.IsDBNull(36) ? null : reader.GetDateTime(36);
                        Input.RokNaPlakjanjeFakturaIzlezna = reader.IsDBNull(37) ? null : reader.GetDateTime(37);
                        Input.Storno = !reader.IsDBNull(38) && reader.GetBoolean(38);
                        Input.PricinaZaStorno = reader.IsDBNull(39) ? null : reader.GetString(39);
                        Input.Zabeleska = reader.IsDBNull(40) ? null : reader.GetString(40);
                        Input.FranshizaIznos = reader.IsDBNull(41) ? null : reader.GetDecimal(41);
                        ViewData["DogovoruvacNaziv"] = reader.IsDBNull(42) ? "" : reader.GetString(42);
                        ViewData["OsigurenikNaziv"] = reader.IsDBNull(43) ? "" : reader.GetString(43);
                        ViewData["SorabotnikNaziv"] = reader.IsDBNull(44) ? "" : reader.GetString(44);
                    }
                }

                // After loading the main polisa data, load the Klasa21 data
                using (SqlCommand cmdKlasa21 = new SqlCommand(@"
                    SELECT DateCreated, UsernameCreated, DateModified, UsernameModified,
                           ProcentNaPopustZaFakturaVoRok, IznosZaPlakjanjeVoRok, ProcentKomercijalenPopust,
                           ProcentFinansiski, PremijaZaNaplata, ShifrarnikInvesticiskiFondIdInvesticiskiFond,
                           NazivNaInvesticiskoDrushtvo, NazivNaInvesticiskiFond, IdentifikaciskiBrojaNaInvesticiskiFond,
                           ProcentOdPremijaPoIFGodishnoEdnokratno, ProcentOdPremijaVoOsiguruvanjeGodishnoEdnokratno,
                           IznosOdPremijaPoIFGodishnoEdnokratno, IznosOdPremijaVoOsiguruvanjeGodishnoEdnokratno,
                           VkupenIznosNaPremijaZaOsiguruvanje, VkupenIznosNaPremijaZaIF, VkupenIznosNaTroshociOdPremija,
                           OsigurenaSumaZaDozhivuvanje, OsigurenaSumaZaSmrtOdBolest, OsigurenaSumaZaSmrtOdNezgoda,
                           PrivremenaOsiguritelnaZastita, PremijaGodishnaEdnokratna, BrojNaUdeliPoIF,
                           VkupnaPremijaGodishna, VkupnaPremijaEdnokratna, DoplatociZaPodgodishnoPlakjanje
                    FROM PolisiKlasa21
                    WHERE PolisaId = @PolisaID", connection))
                {
                    cmdKlasa21.Parameters.AddWithValue("@PolisaID", id);
                    using SqlDataReader readerKlasa21 = await cmdKlasa21.ExecuteReaderAsync();
                    if (await readerKlasa21.ReadAsync())
                    {
                        Klasa21Input.DateCreated = readerKlasa21.IsDBNull(0) ? null : readerKlasa21.GetDateTime(0);
                        Klasa21Input.UsernameCreated = readerKlasa21.IsDBNull(1) ? null : readerKlasa21.GetString(1);
                        Klasa21Input.DateModified = readerKlasa21.IsDBNull(2) ? null : readerKlasa21.GetDateTime(2);
                        Klasa21Input.UsernameModified = readerKlasa21.IsDBNull(3) ? null : readerKlasa21.GetString(3);
                        Klasa21Input.ProcentNaPopustZaFakturaVoRok = readerKlasa21.IsDBNull(4) ? null : readerKlasa21.GetDecimal(4);
                        Klasa21Input.IznosZaPlakjanjeVoRok = readerKlasa21.IsDBNull(5) ? null : readerKlasa21.GetDecimal(5);
                        Klasa21Input.ProcentKomercijalenPopust = readerKlasa21.IsDBNull(6) ? null : readerKlasa21.GetDecimal(6);
                        Klasa21Input.ProcentFinansiski = readerKlasa21.IsDBNull(7) ? null : readerKlasa21.GetDecimal(7);
                        Klasa21Input.PremijaZaNaplata = readerKlasa21.IsDBNull(8) ? null : readerKlasa21.GetDecimal(8);
                        Klasa21Input.ShifrarnikInvesticiskiFondIdInvesticiskiFond = readerKlasa21.GetInt64(9);
                        Klasa21Input.NazivNaInvesticiskoDrushtvo = readerKlasa21.GetInt64(10);
                        Klasa21Input.NazivNaInvesticiskiFond = readerKlasa21.IsDBNull(11) ? null : readerKlasa21.GetString(11);
                        Klasa21Input.IdentifikaciskiBrojaNaInvesticiskiFond = readerKlasa21.IsDBNull(12) ? null : readerKlasa21.GetString(12);
                        Klasa21Input.ProcentOdPremijaPoIFGodishnoEdnokratno = readerKlasa21.IsDBNull(13) ? null : readerKlasa21.GetDecimal(13);
                        Klasa21Input.ProcentOdPremijaVoOsiguruvanjeGodishnoEdnokratno = readerKlasa21.IsDBNull(14) ? null : readerKlasa21.GetDecimal(14);
                        Klasa21Input.IznosOdPremijaPoIFGodishnoEdnokratno = readerKlasa21.IsDBNull(15) ? null : readerKlasa21.GetDecimal(15);
                        Klasa21Input.IznosOdPremijaVoOsiguruvanjeGodishnoEdnokratno = readerKlasa21.IsDBNull(16) ? null : readerKlasa21.GetDecimal(16);
                        Klasa21Input.VkupenIznosNaPremijaZaOsiguruvanje = readerKlasa21.IsDBNull(17) ? null : readerKlasa21.GetDecimal(17);
                        Klasa21Input.VkupenIznosNaPremijaZaIF = readerKlasa21.IsDBNull(18) ? null : readerKlasa21.GetDecimal(18);
                        Klasa21Input.VkupenIznosNaTroshociOdPremija = readerKlasa21.IsDBNull(19) ? null : readerKlasa21.GetDecimal(19);
                        Klasa21Input.OsigurenaSumaZaDozhivuvanje = readerKlasa21.IsDBNull(20) ? null : readerKlasa21.GetString(20);
                        Klasa21Input.OsigurenaSumaZaSmrtOdBolest = readerKlasa21.IsDBNull(21) ? null : readerKlasa21.GetString(21);
                        Klasa21Input.OsigurenaSumaZaSmrtOdNezgoda = readerKlasa21.IsDBNull(22) ? null : readerKlasa21.GetString(22);
                        Klasa21Input.PrivremenaOsiguritelnaZastita = readerKlasa21.IsDBNull(23) ? null : readerKlasa21.GetString(23);
                        Klasa21Input.PremijaGodishnaEdnokratna = readerKlasa21.IsDBNull(24) ? null : readerKlasa21.GetString(24);
                        Klasa21Input.BrojNaUdeliPoIF = readerKlasa21.IsDBNull(25) ? null : readerKlasa21.GetInt64(25);
                        Klasa21Input.VkupnaPremijaGodishna = readerKlasa21.IsDBNull(26) ? null : readerKlasa21.GetDecimal(26);
                        Klasa21Input.VkupnaPremijaEdnokratna = readerKlasa21.IsDBNull(27) ? null : readerKlasa21.GetDecimal(27);
                        Klasa21Input.DoplatociZaPodgodishnoPlakjanje = readerKlasa21.IsDBNull(28) ? null : readerKlasa21.GetDecimal(28);
                    }
                }

                // Load supplementary insurance data - Klasa1
                using (SqlCommand cmdKlasa1 = new SqlCommand(@"
                    SELECT OsigurenaSumaZaSmrtOdNezgoda, OsigurenaSumaZa100ProcTraenInvaliditet,
                           OsigurenaSumaZaTraenInvaliditet, DnevenNadomest, PremijaNezgodaGodishna
                    FROM PolisiKlasa21DopolnitelnoKlasa1
                    WHERE PolisiKlasa21IdPolisa = (SELECT Id FROM PolisiKlasa21 WHERE PolisaId = @PolisaID)", connection))
                {
                    cmdKlasa1.Parameters.AddWithValue("@PolisaID", id);
                    using SqlDataReader readerKlasa1 = await cmdKlasa1.ExecuteReaderAsync();
                    while (await readerKlasa1.ReadAsync())
                    {
                        Klasa1Entries.Add(new PolisaKlasa21DopolnitelnoKlasa1ViewModel
                        {
                            OsigurenaSumaZaSmrtOdNezgoda = readerKlasa1.IsDBNull(0) ? null : readerKlasa1.GetDecimal(0),
                            OsigurenaSumaZa100ProcTraenInvaliditet = readerKlasa1.IsDBNull(1) ? null : readerKlasa1.GetDecimal(1),
                            OsigurenaSumaZaTraenInvaliditet = readerKlasa1.IsDBNull(2) ? null : readerKlasa1.GetDecimal(2),
                            DnevenNadomest = readerKlasa1.IsDBNull(3) ? null : readerKlasa1.GetDecimal(3),
                            PremijaNezgodaGodishna = readerKlasa1.IsDBNull(4) ? null : readerKlasa1.GetDecimal(4)
                        });
                    }
                }

                // Load supplementary insurance data - Klasa2
                using (SqlCommand cmdKlasa2 = new SqlCommand(@"
                    SELECT OsigurenaSumaZaTeskoBolniSostojbi, PremijaTeshkoBolniSostojbiGodishna,
                           OsigurenaSumaZaOperacii, PremijaZaOperaciiGodishna,
                           OsigurenaSumaZaTrajnaNesposobnost, PremijaZaTrajnaNesposobnostGodishna,
                           OsigurenaSumaZaHirushkiIntervencii, PremijaZaHirushkiIntervenciiGodishna
                    FROM PolisiKlasa21DopolnitelnoKlasa2
                    WHERE PolisiKlasa21IdPolisa = (SELECT Id FROM PolisiKlasa21 WHERE PolisaId = @PolisaID)", connection))
                {
                    cmdKlasa2.Parameters.AddWithValue("@PolisaID", id);
                    using SqlDataReader readerKlasa2 = await cmdKlasa2.ExecuteReaderAsync();
                    while (await readerKlasa2.ReadAsync())
                    {
                        Klasa2Entries.Add(new PolisaKlasa21DopolnitelnoKlasa2ViewModel
                        {
                            OsigurenaSumaZaTeskoBolniSostojbi = readerKlasa2.IsDBNull(0) ? null : readerKlasa2.GetDecimal(0),
                            PremijaTeshkoBolniSostojbiGodishna = readerKlasa2.IsDBNull(1) ? null : readerKlasa2.GetDecimal(1),
                            OsigurenaSumaZaOperacii = readerKlasa2.IsDBNull(2) ? null : readerKlasa2.GetDecimal(2),
                            PremijaZaOperaciiGodishna = readerKlasa2.IsDBNull(3) ? null : readerKlasa2.GetDecimal(3),
                            OsigurenaSumaZaTrajnaNesposobnost = readerKlasa2.IsDBNull(4) ? null : readerKlasa2.GetDecimal(4),
                            PremijaZaTrajnaNesposobnostGodishna = readerKlasa2.IsDBNull(5) ? null : readerKlasa2.GetDecimal(5),
                            OsigurenaSumaZaHirushkiIntervencii = readerKlasa2.IsDBNull(6) ? null : readerKlasa2.GetDecimal(6),
                            PremijaZaHirushkiIntervenciiGodishna = readerKlasa2.IsDBNull(7) ? null : readerKlasa2.GetDecimal(7)
                        });
                    }
                }
            }

            // Load additional data
            await LoadFiles();
            await LoadKarticaData(id);
            await LoadZadolzuvanjeData();
            await LoadOZInformation();
            await LoadDogovoruvacOsigurenikEMNGMB();

            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!await HasPageAccess("ViewEditPolisaKlasa21Admin"))
            {
                return RedirectToAccessDenied();
            }

            if (!ModelState.IsValid)
            {
                // Reload dropdown data
                await LoadOsiguriteli();
                await LoadKlasiOsiguruvanje();
                await LoadProdukti();
                await LoadValuti();
                await LoadNaciniNaPlakanje();
                await LoadTipoviNaPlakanje();
                await LoadBanki();
                LoadTipoviNaFaktura();
                await LoadInvesticiskiFondovi();
                await LoadInvesticiskaDrushtva();

                // Reload additional data
                await LoadFiles();
                await LoadKarticaData(Input.Id);
                await LoadZadolzuvanjeData();
                await LoadOZInformation();
                await LoadDogovoruvacOsigurenikEMNGMB();

                return Page();
            }

            string username = HttpContext.Session.GetString("Username");
            if (string.IsNullOrEmpty(username))
            {
                TempData["ErrorMessage"] = "Вашата сесија е истечена. Најавете се повторно.";
                return RedirectToPage("/Account/Login");
            }

            try
            {
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // Update main Polisi table
                            await UpdatePolisi(connection, transaction, username);

                            // Update PolisiKlasa21 table
                            await UpdatePolisiKlasa21(connection, transaction, username);

                            transaction.Commit();
                            TempData["SuccessMessage"] = "Полисата е успешно ажурирана.";
                            return RedirectToPage(new { id = Input.Id });
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            ModelState.AddModelError("", "Настана грешка при ажурирање на полисата. Ве молиме обидете се повторно.");
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "Настана грешка при ажурирање на полисата.";

                // Reload dropdown data
                await LoadOsiguriteli();
                await LoadKlasiOsiguruvanje();
                await LoadProdukti();
                await LoadValuti();
                await LoadNaciniNaPlakanje();
                await LoadTipoviNaPlakanje();
                await LoadBanki();
                LoadTipoviNaFaktura();
                await LoadInvesticiskiFondovi();
                await LoadInvesticiskaDrushtva();

                // Reload additional data
                await LoadFiles();
                await LoadKarticaData(Input.Id);
                await LoadZadolzuvanjeData();
                await LoadOZInformation();
                await LoadDogovoruvacOsigurenikEMNGMB();

                return Page();
            }
        }

        private async Task UpdatePolisi(SqlConnection connection, SqlTransaction transaction, string username)
        {
            string updateSql = @"
                UPDATE Polisi SET
                    DateModified = GETDATE(),
                    UsernameModified = @UsernameModified,
                    KlientiIdSorabotnik = @KlientiIdSorabotnik,
                    Faktoring = @Faktoring,
                    SifrarnikValutiIdFranshizaValuta = @SifrarnikValutiIdFranshizaValuta,
                    ProcentFranshiza = @ProcentFranshiza,
                    FranshizaIznos = @FranshizaIznos,
                    ProcentFinansiski = @ProcentFinansiski,
                    KoregiranaStapkaNaProvizija = @KoregiranaStapkaNaProvizija,
                    Storno = @Storno,
                    PricinaZaStorno = @PricinaZaStorno,
                    Zabeleska = @Zabeleska
                WHERE Id = @Id";

            using (SqlCommand cmd = new SqlCommand(updateSql, connection, transaction))
            {
                cmd.Parameters.AddWithValue("@UsernameModified", username);
                cmd.Parameters.AddWithValue("@KlientiIdSorabotnik", (object)Input.KlientiIdSorabotnik ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@Faktoring", Input.Faktoring);
                cmd.Parameters.AddWithValue("@SifrarnikValutiIdFranshizaValuta", (object)Input.SifrarnikValutiIdFranshizaValuta ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@ProcentFranshiza", (object)Input.ProcentFranshiza ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@FranshizaIznos", (object)Input.FranshizaIznos ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@ProcentFinansiski", (object)Input.ProcentFinansiski ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@KoregiranaStapkaNaProvizija", (object)Input.KoregiranaStapkaNaProvizija ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@Storno", Input.Storno);
                cmd.Parameters.AddWithValue("@PricinaZaStorno", (object)Input.PricinaZaStorno ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@Zabeleska", (object)Input.Zabeleska ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@Id", Input.Id);

                await cmd.ExecuteNonQueryAsync();
            }
        }

        private async Task UpdatePolisiKlasa21(SqlConnection connection, SqlTransaction transaction, string username)
        {
            string updateSql = @"
                UPDATE PolisiKlasa21 SET
                    DateModified = GETDATE(),
                    UsernameModified = @UsernameModified,
                    ProcentNaPopustZaFakturaVoRok = @ProcentNaPopustZaFakturaVoRok,
                    IznosZaPlakjanjeVoRok = @IznosZaPlakjanjeVoRok,
                    ProcentKomercijalenPopust = @ProcentKomercijalenPopust,
                    ProcentFinansiski = @ProcentFinansiski,
                    PremijaZaNaplata = @PremijaZaNaplata
                WHERE PolisaId = @PolisaId";

            using (SqlCommand cmd = new SqlCommand(updateSql, connection, transaction))
            {
                cmd.Parameters.AddWithValue("@UsernameModified", username);
                cmd.Parameters.AddWithValue("@ProcentNaPopustZaFakturaVoRok", (object)Klasa21Input.ProcentNaPopustZaFakturaVoRok ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@IznosZaPlakjanjeVoRok", (object)Klasa21Input.IznosZaPlakjanjeVoRok ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@ProcentKomercijalenPopust", (object)Klasa21Input.ProcentKomercijalenPopust ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@ProcentFinansiski", (object)Klasa21Input.ProcentFinansiski ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@PremijaZaNaplata", (object)Klasa21Input.PremijaZaNaplata ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@PolisaId", Input.Id);

                await cmd.ExecuteNonQueryAsync();
            }
        }
    }
}
