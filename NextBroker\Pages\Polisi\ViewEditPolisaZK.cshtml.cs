using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Renci.SshNet;

namespace NextBroker.Pages.Polisi
{
    public class ViewEditPolisaZKModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;
        public bool HasAdminAccess { get; private set; }
        public bool HasStornoAccess { get; private set; }

        public ViewEditPolisaZKModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        [BindProperty]
        public PolisaViewModel Input { get; set; } = new();

        public IEnumerable<SelectListItem> Osiguriteli { get; set; }
        public IEnumerable<SelectListItem> KlasiOsiguruvanje { get; set; }
        public IEnumerable<SelectListItem> Produkti { get; set; }
        public IEnumerable<SelectListItem> Valuti { get; set; }
        public IEnumerable<SelectListItem> NaciniNaPlakjanje { get; set; }
        public IEnumerable<SelectListItem> TipoviNaPlakanje { get; set; }
        public IEnumerable<SelectListItem> Banki { get; set; }

        public class PolisaViewModel
        {
            public long Id { get; set; }
            public DateTime? DateCreated { get; set; }
            public string UsernameCreated { get; set; }
            public DateTime? DateModified { get; set; }
            public string UsernameModified { get; set; }
            public long? KlientiIdOsiguritel { get; set; }
            public int? KlasiOsiguruvanjeIdKlasa { get; set; }
            public int? ProduktiIdProizvod { get; set; }
            public string? BrojNaPolisa { get; set; }
            public string? BrojNaAOPolisa { get; set; }
            public long? BrojNaPonuda { get; set; }
            public long? KlientiIdDogovoruvac { get; set; }
            public long? KlientiIdOsigurenik { get; set; }
            public bool Kolektivna { get; set; }
            public bool KolektivnaNeodredenBrOsigurenici { get; set; }
            public string NeodredenBrOsigureniciZabeleska { get; set; }
            public DateTime? DatumVaziOd { get; set; }
            public DateTime? DatumVaziDo { get; set; }
            public DateTime? DatumNaIzdavanje { get; set; }
            public int? VremetraenjeNaPolisa { get; set; }
            public int? PeriodNaUplata { get; set; }
            public long SifrarnikValutiIdValuta { get; set; }
            public long? SifrarnikValutiIdFranshizaValuta { get; set; }
            public long? KlientiIdSorabotnik { get; set; }
            public bool Faktoring { get; set; }
            public decimal? ProcentFranshiza { get; set; }
            public decimal? FranshizaIznos { get; set; }
            public decimal? ProcentFinansiski { get; set; }
            public decimal? KoregiranaStapkaNaProvizija { get; set; }
            public long? SifrarnikNacinNaPlakjanjeId { get; set; }
            public string TipNaFaktura { get; set; }
            public string BrojNaFakturaVlezna { get; set; }
            public DateTime? DatumNaFakturaVlezna { get; set; }
            public DateTime? RokNaPlakjanjeFakturaVlezna { get; set; }
            public long? SifrarnikTipNaPlakanjeId { get; set; }
            public long? SifrarnikBankiIdBanka { get; set; }
            public bool GeneriranaFakturaIzlezna { get; set; }
            public string BrojNaFakturaIzlezna { get; set; }
            public DateTime? DatumNaIzleznaFaktura { get; set; }
            public DateTime? RokNaPlakjanjeFakturaIzlezna { get; set; }
            public bool Storno { get; set; }
            public string PricinaZaStorno { get; set; }
            public string Zabeleska { get; set; }
        }

        // Add new class for Zelen Karton data
        public class PolisaZKViewModel
        {
            public DateTime? DateCreated { get; set; }
            public string UsernameCreated { get; set; }
            public DateTime? DateModified { get; set; }
            public string UsernameModified { get; set; }
            public decimal? OsnovnaPremija { get; set; }
            public decimal? Bonus { get; set; }
            public decimal? Malus { get; set; }
            public decimal? Doplatoci { get; set; }
            public decimal? Popusti { get; set; }
            public decimal? VkupnoOsnovnaPremijaZK { get; set; }
            public bool KlasiOsiguruvanjeKlasa1 { get; set; }
            public decimal? OsigurenaSumaNezgoda { get; set; }
            public decimal? PremijaZaNezgoda { get; set; }
            public bool KlasiOsiguruvanjeKlasa8 { get; set; }
            public decimal? PremijaKrsenjeStakloZK { get; set; }
            public decimal? VkupnaPremija { get; set; }
            public decimal? ProcentNaPopustZaFakturaVoRok { get; set; }
            public decimal? IznosZaPlakjanjeVoRok { get; set; }
            public decimal? ProcentKomercijalenPopust { get; set; }
            public decimal? ProcentFinansiskiPopust { get; set; }
            public decimal? PremijaZaNaplata { get; set; }
            public decimal? Uplateno { get; set; }
            public decimal? DolznaPremija { get; set; }
            public long? BrojNaAOPolisa { get; set; }
        }

        [BindProperty]
        public PolisaZKViewModel ZKInput { get; set; } = new();

        // Add new class for Soobrakjajna data
        public class PolisaSoobrakjajnaViewModel
        {
            public DateTime? DateCreated { get; set; }
            public string UsernameCreated { get; set; }
            public DateTime? DateModified { get; set; }
            public string UsernameModified { get; set; }
            public string RegisterskaOznaka { get; set; }
            public string Marka { get; set; }
            public long? SifrarnikTipNaVozilo { get; set; }
            public string KomercijalnaOznaka { get; set; }
            public string Shasija { get; set; }
            public int? GodinaNaProizvodstvo { get; set; }
            public int? ZafatninaNaMotorotcm3 { get; set; }
            public int? SilinaNaMotorotKW { get; set; }
            public int? BrojNaSedista { get; set; }
            public string BojaNaVoziloto { get; set; }
            public int? NosivostKG { get; set; }
            public DateTime? DatumNaRegistracija { get; set; }
            public string BrojNaVpisot { get; set; }
            public DateTime? DatumNaPrvataRegistracija { get; set; }
            public string PrezimeNazivNaKorisnikot { get; set; }
            public string Ime { get; set; }
            public string AdresaNaPostojanoZivealiste { get; set; }
            public string EMBNaKorisnikot { get; set; }
            public string DatumNaPrvaRegistracijaVoRSM { get; set; }
            public string DozvolataJaIzdal { get; set; }
            public string OznakaNaOdobrenie { get; set; }
            public string BrojNAEUPotvrdaZaSoobraznost { get; set; }
            public string PrezimeNazivNaSopstvenikot { get; set; }
            public string ImeSopstvenik { get; set; }
            public string AdresaNaPostojanoZivealisteSediste { get; set; }
            public string EMBNaFizickoLiceEMBNaPravnoLice { get; set; }
            public string KategorijaIVidNaVoziloto { get; set; }
            public string OblikINamenaNaKaroserijata { get; set; }
            public string TipNaMotorot { get; set; }
            public string VidNaGorivo { get; set; }
            public string BrojNaVrtezi { get; set; }
            public string IdentifikacionenBrojNaMotorot { get; set; }
            public string MaksimalnaBrzinaKM { get; set; }
            public string OdnosSilinaMasa { get; set; }
            public string MasaNaVoziloto { get; set; }
            public string NajgolemaKonstruktivnaVkupnaMasaNaVozilotoKG { get; set; }
            public string NajgolemaLegalnaVkupnaMasaNaVozilotoPriRegistracijaKG { get; set; }
            public string NajgolemaLegalnaVkupnaMasaNaGrupaVozilaPriRegistracijaKG { get; set; }
            public int? BrojNaOski { get; set; }
            public string RaspredelbaNaNajgolemataKonstruktivnaVkupnaMasaPoOskiKGINaPriklucnataTocka { get; set; }
            public string NajgolemoKonstruktivnoOsnoOptovaruvaweKgiNaPriklucnataTocka { get; set; }
            public int? Dolzhina { get; set; }
            public int? Visina { get; set; }
            public string NajgolemaKonstruktivnaVkupnaMasaNaKocenaPrikolkaKG { get; set; }
            public string NajgolemaKonstruktivnaVkupnaMasaNaNEkocenaPrikolkaKG { get; set; }
            public int? BrojNaMestaZaStoenje { get; set; }
            public string DozvoleniPnevmaticiINaplatki { get; set; }
            public string BrojNaMestazaLezenje { get; set; }
            public string CO2 { get; set; }
            public string NajgolemoKonstruktivnoOptovaruvanjeNaPriklucokotZaPrikolka { get; set; }
            public string StacionarnaBucavost { get; set; }
        }

        [BindProperty]
        public PolisaSoobrakjajnaViewModel SoobrakjajnaInput { get; set; } = new();

        // Add property to hold dropdown items
        public IEnumerable<SelectListItem> TipoviNaVozilo { get; set; }

        // Add after other properties
        public class FileInfo
        {
            public long Id { get; set; }
            public string FileName { get; set; }
            public DateTime DateCreated { get; set; }
            public string UsernameCreated { get; set; }
        }

        public List<FileInfo> Files { get; set; }

        public System.Data.DataTable KarticaData { get; set; }

        // Add this property to store the AO policy number for display
        public string AOPolisaBroj { get; set; }

        // OZ Information properties
        public decimal OZIznosIzleznaFakturaPremija { get; set; }
        public decimal OZIznosPolisa { get; set; }

        // New properties for database function results
        public string? DogovoruvacEMNGMB { get; set; }
        public string? OsigurenikEMNGMB { get; set; }

        private async Task LoadOsiguriteli()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Naziv 
                    FROM Klienti 
                    WHERE Osiguritel = 1 
                    ORDER BY Naziv", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Naziv"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Osiguriteli = items;
                }
            }
        }

        private async Task LoadKlasiOsiguruvanje()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, CONCAT(KlasaBroj, ' - ', KlasaIme) as DisplayName 
                    FROM KlasiOsiguruvanje 
                    WHERE KlasaBroj = 10 
                    AND (Disabled = 0 OR Disabled IS NULL)", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["DisplayName"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    KlasiOsiguruvanje = items;
                }
            }
        }

        private async Task LoadProdukti()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Ime 
                    FROM Produkti 
                    WHERE Id = 16", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Ime"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Produkti = items;
                }
            }
        }

        private async Task LoadValuti()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Valuta 
                    FROM SifrarnikValuti 
                    ORDER BY Valuta", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Valuta"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Valuti = items;
                }
            }
        }

        private async Task LoadNaciniNaPlakjanje()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, NacinNaPlakanje 
                    FROM SifrarnikNacinNaPlakanje 
                    ORDER BY NacinNaPlakanje", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["NacinNaPlakanje"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    NaciniNaPlakjanje = items;
                }
            }
        }

        private async Task LoadTipoviNaPlakanje()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, TipNaPlakanje 
                    FROM SifrarnikTipNaPlakanje 
                    ORDER BY TipNaPlakanje", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["TipNaPlakanje"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    TipoviNaPlakanje = items;
                }
            }
        }

        private async Task LoadBanki()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Banka 
                    FROM SifrarnikBanki 
                    ORDER BY Banka", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Banka"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Banki = items;
                }
            }
        }

        private async Task LoadTipoviNaVozilo()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, TipNaVozilo 
                    FROM SifrarnikTipNaVozilo 
                    ORDER BY TipNaVozilo", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["TipNaVozilo"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    TipoviNaVozilo = items;
                }
            }
        }

        private async Task LoadFiles()
        {
            Files = new List<FileInfo>();
            string connectionString = _configuration.GetConnectionString("DefaultConnection");

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand command = new SqlCommand(
                    "SELECT Id, FileName, DateCreated, UsernameCreated " +
                    "FROM PolisiFileSystem " +
                    "WHERE PolisaId = @PolisaId " +
                    "ORDER BY DateCreated DESC", connection))
                {
                    command.Parameters.AddWithValue("@PolisaId", Input.Id);

                    using (SqlDataReader reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            Files.Add(new FileInfo
                            {
                                Id = reader.GetInt64(0),
                                FileName = reader.GetString(1),
                                DateCreated = reader.GetDateTime(2),
                                UsernameCreated = reader.GetString(3)
                            });
                        }
                    }
                }
            }
        }

        private async Task LoadKarticaData(long polisaId)
        {
            using (SqlConnection conn = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await conn.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT 
                        [ID],
                        [Рата број],
                        [Датум на доспевање],
                        [Износ на рата],
                        [Датум на уплата],
                        [Уплатен износ],
                        [Затворена рата],
                        [Сторно]
                    FROM PolisaKartica 
                    WHERE PolisaId = @PolisaId
                    ORDER BY [Рата број]", conn))
                {
                    cmd.Parameters.AddWithValue("@PolisaId", polisaId);
                    using (SqlDataAdapter adapter = new SqlDataAdapter(cmd))
                    {
                        KarticaData = new System.Data.DataTable();
                        adapter.Fill(KarticaData);
                    }
                }
            }
        }

        private async Task LoadOZInformation()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                try
                {
                    // Load OZ Iznos Izlezna Faktura Premija
                    using (SqlCommand cmd = new SqlCommand("SELECT dbo.VraziOZIznosIzleznaFakturaPremija((select top 1 BrojNaFakturaIzlezna from polisi where id = @polisaId order by DateCreated Desc))", connection))
                    {
                        cmd.Parameters.AddWithValue("@polisaId", Input.Id);
                        var result = await cmd.ExecuteScalarAsync();
                        OZIznosIzleznaFakturaPremija = result != null && result != DBNull.Value ? Convert.ToDecimal(result) : 0;
                    }

                    // Load OZ Iznos Polisa
                    using (SqlCommand cmd = new SqlCommand("SELECT dbo.VraziOZIznosPolisa(@polisaId)", connection))
                    {
                        cmd.Parameters.AddWithValue("@polisaId", Input.Id);
                        var result = await cmd.ExecuteScalarAsync();
                        OZIznosPolisa = result != null && result != DBNull.Value ? Convert.ToDecimal(result) : 0;
                    }
                }
                catch (Exception)
                {
                    // In case of any error, set values to 0
                    OZIznosIzleznaFakturaPremija = 0;
                    OZIznosPolisa = 0;
                }
            }
        }

        private async Task LoadDogovoruvacOsigurenikEMNGMB()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                try
                {
                    // Load Dogovoruvac EMNMGB
                    using (SqlCommand cmd = new SqlCommand("SELECT dbo.VratiPolisaDogovoruvacEMNGMB(@polisaId)", connection))
                    {
                        cmd.Parameters.AddWithValue("@polisaId", Input.Id);
                        var result = await cmd.ExecuteScalarAsync();
                        DogovoruvacEMNGMB = result != null && result != DBNull.Value ? result.ToString() : null;
                    }

                    // Load Osigurenik EMNMGB
                    using (SqlCommand cmd = new SqlCommand("SELECT dbo.VratiPolisaOsigurenikEMNGMB(@polisaId)", connection))
                    {
                        cmd.Parameters.AddWithValue("@polisaId", Input.Id);
                        var result = await cmd.ExecuteScalarAsync();
                        OsigurenikEMNGMB = result != null && result != DBNull.Value ? result.ToString() : null;
                    }
                }
                catch (Exception ex)
                {
                    // Log the error or handle it appropriately
                    Console.WriteLine($"Error loading Dogovoruvac/Osigurenik EMNMGB: {ex.Message}");
                    DogovoruvacEMNGMB = null;
                    OsigurenikEMNGMB = null;
                }
            }
        }

        public async Task<JsonResult> OnGetSearchKlienti(string mb)
        {
            if (string.IsNullOrWhiteSpace(mb))
                return new JsonResult(new List<object>());

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            var results = new List<object>();

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT TOP 10 
                        Id, 
                        ISNULL(MB, '') as MB,
                        ISNULL(EDB, '') as EDB,
                        ISNULL(EMBG, '') as EMBG,
                        CASE 
                            WHEN KlientFizickoPravnoLice = 'P' THEN Naziv
                            ELSE CONCAT(ISNULL(Ime, ''), ' ', ISNULL(Prezime, ''))
                        END as Naziv,
                        KlientFizickoPravnoLice,
                        ISNULL(Ime, '') as Ime,
                        ISNULL(Prezime, '') as Prezime
                    FROM Klienti 
                    WHERE MB LIKE @Search + '%'
                       OR EDB LIKE @Search + '%'
                       OR EMBG LIKE @Search + '%'
                       OR Naziv LIKE '%' + @Search + '%'
                       OR Ime LIKE '%' + @Search + '%'
                       OR Prezime LIKE '%' + @Search + '%'
                    ORDER BY 
                        CASE 
                            WHEN MB LIKE @Search + '%' THEN 1
                            WHEN EDB LIKE @Search + '%' THEN 2
                            WHEN EMBG LIKE @Search + '%' THEN 3
                            ELSE 4
                        END,
                        MB, Naziv", connection))
                {
                    cmd.Parameters.AddWithValue("@Search", mb);
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            id = reader["Id"].ToString(),
                            naziv = reader["Naziv"].ToString(),
                            mb = reader["MB"].ToString(),
                            edb = reader["EDB"].ToString(),
                            embg = reader["EMBG"].ToString(),
                            fizickoPravno = reader["KlientFizickoPravnoLice"].ToString(),
                            ime = reader["Ime"].ToString(),
                            prezime = reader["Prezime"].ToString()
                        });
                    }
                }
            }

            return new JsonResult(results);
        }

        public async Task<IActionResult> OnGetAsync(long id)
        {
            if (!await HasPageAccess("ViewEditPolisaZK"))
            {
                return RedirectToAccessDenied();
            }

            // Check admin access
            HasAdminAccess = await HasPageAccess("ViewEditPolisaZKAdmin");

            // Check storno access
            HasStornoAccess = await HasPageAccess("StorniranjePolisi");

            await LoadOsiguriteli();
            await LoadKlasiOsiguruvanje();
            await LoadProdukti();
            await LoadValuti();
            await LoadNaciniNaPlakjanje();
            await LoadTipoviNaPlakanje();
            await LoadBanki();
            await LoadTipoviNaVozilo();

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT  p.Id, p.DateCreated, p.UsernameCreated, p.DateModified, p.UsernameModified, 
       p.KlientiIdOsiguritel, p.KlasiOsiguruvanjeIdKlasa, p.ProduktiIdProizvod,
       p.BrojNaPolisa, 
    ((select BrojNaPolisa from Polisi where id = (select Polisaid from Polisiavtoodgovornost where id = pzk.BrojNaAOPolisa))   ) as BrojNaAOPolisa,
        p.BrojNaPonuda, p.KlientiIdDogovoruvac, p.KlientiIdOsigurenik,
       p.Kolektivna, p.KolektivnaNeodredenBrOsigurenici, p.NeodredenBrOsigureniciZabeleska,
       p.DatumVaziOd, p.DatumVaziDo, p.DatumNaIzdavanje, p.VremetraenjeNaPolisa, p.PeriodNaUplata,
       p.SifrarnikValutiIdValuta, p.KlientiIdSorabotnik, p.Faktoring, p.SifrarnikValutiIdFranshizaValuta,
       p.ProcentFranshiza, p.FranshizaIznos, p.ProcentFinansiski, p.KoregiranaStapkaNaProvizija,
       p.SifrarnikNacinNaPlakjanjeId, p.TipNaFaktura, p.BrojNaFakturaVlezna,
       p.DatumNaFakturaVlezna, p.RokNaPlakjanjeFakturaVlezna,
       p.SifrarnikTipNaPlakanjeId,
       p.SifrarnikBankiIdBanka,
       p.GeneriranaFakturaIzlezna, p.BrojNaFakturaIzlezna,
       p.DatumNaIzleznaFaktura, p.RokNaPlakjanjeFakturaIzlezna,
       p.Storno, p.PricinaZaStorno, p.Zabeleska,
       CASE 
           WHEN k.KlientFizickoPravnoLice = 'P' THEN k.Naziv
           ELSE CONCAT(ISNULL(k.Ime, ''), ' ', ISNULL(k.Prezime, ''))
       END as DogovoruvacNaziv,
       CASE 
           WHEN k2.KlientFizickoPravnoLice = 'P' THEN k2.Naziv
           ELSE CONCAT(ISNULL(k2.Ime, ''), ' ', ISNULL(k2.Prezime, ''))
       END as OsigurenikNaziv,
       CASE 
           WHEN k3.KlientFizickoPravnoLice = 'P' THEN k3.Naziv
           ELSE CONCAT(ISNULL(k3.Ime, ''), ' ', ISNULL(k3.Prezime, ''))
       END as SorabotnikNaziv
FROM Polisi p
LEFT JOIN Klienti k ON p.KlientiIdDogovoruvac = k.Id
LEFT JOIN Klienti k2 ON p.KlientiIdOsigurenik = k2.Id
LEFT JOIN Klienti k3 ON p.KlientiIdSorabotnik = k3.Id
LEFT JOIN PolisiAvtoOdgovornost pao ON p.Id = pao.PolisaId  -- Added join
left join polisizelenkarton pzk on p.id = pzk.polisaid 
WHERE p.Id = @Id;
", connection))
                {
                    cmd.Parameters.AddWithValue("@Id", id);
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    if (await reader.ReadAsync())
                    {
                        Input.Id = reader.GetInt64(0);
                        Input.DateCreated = reader.IsDBNull(1) ? null : reader.GetDateTime(1);
                        Input.UsernameCreated = reader.GetString(2);
                        Input.DateModified = reader.IsDBNull(3) ? null : reader.GetDateTime(3);
                        Input.UsernameModified = reader.IsDBNull(4) ? null : reader.GetString(4);
                        Input.KlientiIdOsiguritel = reader.IsDBNull(5) ? null : reader.GetInt64(5);
                        Input.KlasiOsiguruvanjeIdKlasa = reader.IsDBNull(6) ? null : reader.GetInt32(6);
                        Input.ProduktiIdProizvod = reader.IsDBNull(7) ? null : reader.GetInt32(7);
                        Input.BrojNaPolisa = reader.IsDBNull(8) ? null : reader.GetString(8);
                        Input.BrojNaAOPolisa = reader.IsDBNull(9) ? null : reader.GetString(9);
                        Input.BrojNaPonuda = reader.IsDBNull(10) ? null : reader.GetInt64(10);
                        Input.KlientiIdDogovoruvac = reader.IsDBNull(11) ? null : reader.GetInt64(11);
                        Input.KlientiIdOsigurenik = reader.IsDBNull(12) ? null : reader.GetInt64(12);
                        Input.Kolektivna = !reader.IsDBNull(13) && reader.GetBoolean(13);
                        Input.KolektivnaNeodredenBrOsigurenici = !reader.IsDBNull(14) && reader.GetBoolean(14);
                        Input.NeodredenBrOsigureniciZabeleska = reader.IsDBNull(15) ? null : reader.GetString(15);
                        Input.DatumVaziOd = reader.IsDBNull(16) ? null : reader.GetDateTime(16);
                        Input.DatumVaziDo = reader.IsDBNull(17) ? null : reader.GetDateTime(17);
                        Input.DatumNaIzdavanje = reader.IsDBNull(18) ? null : reader.GetDateTime(18);
                        Input.VremetraenjeNaPolisa = reader.IsDBNull(19) ? null : reader.GetInt32(19);
                        Input.PeriodNaUplata = reader.IsDBNull(20) ? null : reader.GetInt32(20);
                        Input.SifrarnikValutiIdValuta = reader.IsDBNull(21) ? 0 : reader.GetInt64(21);
                        Input.KlientiIdSorabotnik = reader.IsDBNull(22) ? null : reader.GetInt64(22);
                        Input.Faktoring = !reader.IsDBNull(23) && reader.GetBoolean(23);
                        Input.SifrarnikValutiIdFranshizaValuta = reader.IsDBNull(24) ? null : reader.GetInt64(24);
                        Input.ProcentFranshiza = reader.IsDBNull(25) ? null : reader.GetDecimal(25);
                        Input.FranshizaIznos = reader.IsDBNull(26) ? null : reader.GetDecimal(26);
                        Input.ProcentFinansiski = reader.IsDBNull(27) ? null : reader.GetDecimal(27);
                        Input.KoregiranaStapkaNaProvizija = reader.IsDBNull(28) ? null : reader.GetDecimal(28);
                        Input.SifrarnikNacinNaPlakjanjeId = reader.IsDBNull(29) ? null : reader.GetInt64(29);
                        Input.TipNaFaktura = reader.IsDBNull(30) ? null : reader.GetString(30);
                        Input.BrojNaFakturaVlezna = reader.IsDBNull(31) ? null : reader.GetString(31);
                        Input.DatumNaFakturaVlezna = reader.IsDBNull(32) ? null : reader.GetDateTime(32);
                        Input.RokNaPlakjanjeFakturaVlezna = reader.IsDBNull(33) ? null : reader.GetDateTime(33);
                        Input.SifrarnikTipNaPlakanjeId = reader.IsDBNull(34) ? null : reader.GetInt64(34);
                        Input.SifrarnikBankiIdBanka = reader.IsDBNull(35) ? null : reader.GetInt64(35);
                        Input.GeneriranaFakturaIzlezna = !reader.IsDBNull(36) && reader.GetBoolean(36);
                        Input.BrojNaFakturaIzlezna = reader.IsDBNull(37) ? null : reader.GetString(37);
                        Input.DatumNaIzleznaFaktura = reader.IsDBNull(38) ? null : reader.GetDateTime(38);
                        Input.RokNaPlakjanjeFakturaIzlezna = reader.IsDBNull(39) ? null : reader.GetDateTime(39);
                        Input.Storno = !reader.IsDBNull(40) && reader.GetBoolean(40);
                        Input.PricinaZaStorno = reader.IsDBNull(41) ? null : reader.GetString(41);
                        Input.Zabeleska = reader.IsDBNull(42) ? null : reader.GetString(42);
                        ViewData["DogovoruvacNaziv"] = reader.IsDBNull(43) ? "" : reader.GetString(43);
                        ViewData["OsigurenikNaziv"] = reader.IsDBNull(44) ? "" : reader.GetString(44);
                        ViewData["SorabotnikNaziv"] = reader.IsDBNull(45) ? "" : reader.GetString(45);
                    }
                }

                // After loading the main polisa data, load the ZK data
                using (SqlCommand cmdZK = new SqlCommand(@"
                    SELECT DateCreated, UsernameCreated, DateModified, UsernameModified,
                           OsnovnaPremija, Bonus, Malus, Doplatoci, Popusti, VkupnoOsnovnaPremijaZK,
                           KlasiOsiguruvanjeKlasa1, OsigurenaSumaNezgoda, PremijaZaNezgoda,
                           KlasiOsiguruvanjeKlasa8, PremijaKrsenjeStakloZK, VkupnaPremija,
                           ProcentNaPopustZaFakturaVoRok, IznosZaPlakjanjeVoRok, ProcentKomercijalenPopust,
                           ProcentFinansiskiPopust, PremijaZaNaplata, Uplateno, DolznaPremija,
                           BrojNaAOPolisa
                    FROM PolisiZelenKarton 
                    WHERE PolisaId = @PolisaId", connection))
                {
                    cmdZK.Parameters.AddWithValue("@PolisaId", id);
                    using SqlDataReader readerZK = await cmdZK.ExecuteReaderAsync();
                    if (await readerZK.ReadAsync())
                    {
                        ZKInput.DateCreated = readerZK.IsDBNull(0) ? null : readerZK.GetDateTime(0);
                        ZKInput.UsernameCreated = readerZK.IsDBNull(1) ? null : readerZK.GetString(1);
                        ZKInput.DateModified = readerZK.IsDBNull(2) ? null : readerZK.GetDateTime(2);
                        ZKInput.UsernameModified = readerZK.IsDBNull(3) ? null : readerZK.GetString(3);
                        ZKInput.OsnovnaPremija = readerZK.IsDBNull(4) ? null : readerZK.GetDecimal(4);
                        ZKInput.Bonus = readerZK.IsDBNull(5) ? null : readerZK.GetDecimal(5);
                        ZKInput.Malus = readerZK.IsDBNull(6) ? null : readerZK.GetDecimal(6);
                        ZKInput.Doplatoci = readerZK.IsDBNull(7) ? null : readerZK.GetDecimal(7);
                        ZKInput.Popusti = readerZK.IsDBNull(8) ? null : readerZK.GetDecimal(8);
                        ZKInput.VkupnoOsnovnaPremijaZK = readerZK.IsDBNull(9) ? null : readerZK.GetDecimal(9);
                        ZKInput.KlasiOsiguruvanjeKlasa1 = readerZK.GetBoolean(10);
                        ZKInput.OsigurenaSumaNezgoda = readerZK.IsDBNull(11) ? null : readerZK.GetDecimal(11);
                        ZKInput.PremijaZaNezgoda = readerZK.IsDBNull(12) ? null : readerZK.GetDecimal(12);
                        ZKInput.KlasiOsiguruvanjeKlasa8 = readerZK.GetBoolean(13);
                        ZKInput.PremijaKrsenjeStakloZK = readerZK.IsDBNull(14) ? null : readerZK.GetDecimal(14);
                        ZKInput.VkupnaPremija = readerZK.IsDBNull(15) ? null : readerZK.GetDecimal(15);
                        ZKInput.ProcentNaPopustZaFakturaVoRok = readerZK.IsDBNull(16) ? null : readerZK.GetDecimal(16);
                        ZKInput.IznosZaPlakjanjeVoRok = readerZK.IsDBNull(17) ? null : readerZK.GetDecimal(17);
                        ZKInput.ProcentKomercijalenPopust = readerZK.IsDBNull(18) ? null : readerZK.GetDecimal(18);
                        ZKInput.ProcentFinansiskiPopust = readerZK.IsDBNull(19) ? null : readerZK.GetDecimal(19);
                        ZKInput.PremijaZaNaplata = readerZK.IsDBNull(20) ? null : readerZK.GetDecimal(20);
                        ZKInput.Uplateno = readerZK.IsDBNull(21) ? null : readerZK.GetDecimal(21);
                        ZKInput.DolznaPremija = readerZK.IsDBNull(22) ? null : readerZK.GetDecimal(22);
                        ZKInput.BrojNaAOPolisa = readerZK.IsDBNull(23) ? null : readerZK.GetInt64(23);
                    }
                }

                // Add query for Soobrakjajna data
                using (SqlCommand cmdSoobrakjajna = new SqlCommand(@"
                    SELECT DateCreated, UsernameCreated, DateModified, UsernameModified,
                           RegisterskaOznaka, Marka, SifrarnikTipNaVozilo,
                           KomercijalnaOznaka, Shasija, GodinaNaProizvodstvo,
                           ZafatninaNaMotorotcm3, SilinaNaMotorotKW, BrojNaSedista,
                           BojaNaVoziloto, NosivostKG, DatumNaRegistracija,
                           BrojNaVpisot, DatumNaPrvataRegistracija,
                           PrezimeNazivNaKorisnikot, Ime,
                           AdresaNaPostojanoZivealiste, EMBNaKorisnikot,
                           DatumNaPrvaRegistracijaVoRSM, DozvolataJaIzdal,
                           OznakaNaOdobrenie, BrojNAEUPotvrdaZaSoobraznost,
                           PrezimeNazivNaSopstvenikot, ImeSopstvenik,
                           AdresaNaPostojanoZivealisteSediste, EMBNaFizickoLiceEMBNaPravnoLice,
                           KategorijaIVidNaVoziloto, OblikINamenaNaKaroserijata,
                           TipNaMotorot, VidNaGorivo, BrojNaVrtezi,
                           IdentifikacionenBrojNaMotorot, MaksimalnaBrzinaKM, OdnosSilinaMasa,
                           MasaNaVoziloto, NajgolemaKonstruktivnaVkupnaMasaNaVozilotoKG,
                           NajgolemaLegalnaVkupnaMasaNaVozilotoPriRegistracijaKG,
                           NajgolemaLegalnaVkupnaMasaNaGrupaVozilaPriRegistracijaKG,
                           BrojNaOski, RaspredelbaNaNajgolemataKonstruktivnaVkupnaMasaPoOskiKGINaPriklucnataTocka,
                           NajgolemoKonstruktivnoOsnoOptovaruvaweKgiNaPriklucnataTocka,
                           Dolzhina, Visina, NajgolemaKonstruktivnaVkupnaMasaNaKocenaPrikolkaKG,
                           NajgolemaKonstruktivnaVkupnaMasaNaNEkocenaPrikolkaKG,
                           BrojNaMestaZaStoenje, DozvoleniPnevmaticiINaplatki,
                           BrojNaMestazaLezenje, CO2,
                           NajgolemoKonstruktivnoOptovaruvanjeNaPriklucokotZaPrikolka,
                           StacionarnaBucavost
                    FROM PolisiZelenKartonSoobrakajna 
                    WHERE PolisaId = @PolisaId", connection))
                {
                    cmdSoobrakjajna.Parameters.AddWithValue("@PolisaId", id);
                    using SqlDataReader readerSoobrakjajna = await cmdSoobrakjajna.ExecuteReaderAsync();
                    if (await readerSoobrakjajna.ReadAsync())
                    {
                        SoobrakjajnaInput.DateCreated = readerSoobrakjajna.IsDBNull(0) ? null : readerSoobrakjajna.GetDateTime(0);
                        SoobrakjajnaInput.UsernameCreated = readerSoobrakjajna.IsDBNull(1) ? null : readerSoobrakjajna.GetString(1);
                        SoobrakjajnaInput.DateModified = readerSoobrakjajna.IsDBNull(2) ? null : readerSoobrakjajna.GetDateTime(2);
                        SoobrakjajnaInput.UsernameModified = readerSoobrakjajna.IsDBNull(3) ? null : readerSoobrakjajna.GetString(3);
                        SoobrakjajnaInput.RegisterskaOznaka = readerSoobrakjajna.IsDBNull(4) ? null : readerSoobrakjajna.GetString(4);
                        SoobrakjajnaInput.Marka = readerSoobrakjajna.IsDBNull(5) ? null : readerSoobrakjajna.GetString(5);
                        SoobrakjajnaInput.SifrarnikTipNaVozilo = readerSoobrakjajna.IsDBNull(6) ? null : readerSoobrakjajna.GetInt64(6);
                        SoobrakjajnaInput.KomercijalnaOznaka = readerSoobrakjajna.IsDBNull(7) ? null : readerSoobrakjajna.GetString(7);
                        SoobrakjajnaInput.Shasija = readerSoobrakjajna.IsDBNull(8) ? null : readerSoobrakjajna.GetString(8);
                        SoobrakjajnaInput.GodinaNaProizvodstvo = readerSoobrakjajna.IsDBNull(9) ? null : readerSoobrakjajna.GetInt32(9);
                        SoobrakjajnaInput.ZafatninaNaMotorotcm3 = readerSoobrakjajna.IsDBNull(10) ? null : readerSoobrakjajna.GetInt32(10);
                        SoobrakjajnaInput.SilinaNaMotorotKW = readerSoobrakjajna.IsDBNull(11) ? null : readerSoobrakjajna.GetInt32(11);
                        SoobrakjajnaInput.BrojNaSedista = readerSoobrakjajna.IsDBNull(12) ? null : readerSoobrakjajna.GetInt32(12);
                        SoobrakjajnaInput.BojaNaVoziloto = readerSoobrakjajna.IsDBNull(13) ? null : readerSoobrakjajna.GetString(13);
                        SoobrakjajnaInput.NosivostKG = readerSoobrakjajna.IsDBNull(14) ? null : readerSoobrakjajna.GetInt32(14);
                        SoobrakjajnaInput.DatumNaRegistracija = readerSoobrakjajna.IsDBNull(15) ? null : readerSoobrakjajna.GetDateTime(15);
                        SoobrakjajnaInput.BrojNaVpisot = readerSoobrakjajna.IsDBNull(16) ? null : readerSoobrakjajna.GetString(16);
                        SoobrakjajnaInput.DatumNaPrvataRegistracija = readerSoobrakjajna.IsDBNull(17) ? null : readerSoobrakjajna.GetDateTime(17);
                        SoobrakjajnaInput.PrezimeNazivNaKorisnikot = readerSoobrakjajna.IsDBNull(18) ? null : readerSoobrakjajna.GetString(18);
                        SoobrakjajnaInput.Ime = readerSoobrakjajna.IsDBNull(19) ? null : readerSoobrakjajna.GetString(19);
                        SoobrakjajnaInput.AdresaNaPostojanoZivealiste = readerSoobrakjajna.IsDBNull(20) ? null : readerSoobrakjajna.GetString(20);
                        SoobrakjajnaInput.EMBNaKorisnikot = readerSoobrakjajna.IsDBNull(21) ? null : readerSoobrakjajna.GetString(21);
                        SoobrakjajnaInput.DatumNaPrvaRegistracijaVoRSM = readerSoobrakjajna.IsDBNull(22) ? null : readerSoobrakjajna.GetString(22);
                        SoobrakjajnaInput.DozvolataJaIzdal = readerSoobrakjajna.IsDBNull(23) ? null : readerSoobrakjajna.GetString(23);
                        SoobrakjajnaInput.OznakaNaOdobrenie = readerSoobrakjajna.IsDBNull(24) ? null : readerSoobrakjajna.GetString(24);
                        SoobrakjajnaInput.BrojNAEUPotvrdaZaSoobraznost = readerSoobrakjajna.IsDBNull(25) ? null : readerSoobrakjajna.GetString(25);
                        SoobrakjajnaInput.PrezimeNazivNaSopstvenikot = readerSoobrakjajna.IsDBNull(26) ? null : readerSoobrakjajna.GetString(26);
                        SoobrakjajnaInput.ImeSopstvenik = readerSoobrakjajna.IsDBNull(27) ? null : readerSoobrakjajna.GetString(27);
                        SoobrakjajnaInput.AdresaNaPostojanoZivealisteSediste = readerSoobrakjajna.IsDBNull(28) ? null : readerSoobrakjajna.GetString(28);
                        SoobrakjajnaInput.EMBNaFizickoLiceEMBNaPravnoLice = readerSoobrakjajna.IsDBNull(29) ? null : readerSoobrakjajna.GetString(29);
                        SoobrakjajnaInput.KategorijaIVidNaVoziloto = readerSoobrakjajna.IsDBNull(30) ? null : readerSoobrakjajna.GetString(30);
                        SoobrakjajnaInput.OblikINamenaNaKaroserijata = readerSoobrakjajna.IsDBNull(31) ? null : readerSoobrakjajna.GetString(31);
                        SoobrakjajnaInput.TipNaMotorot = readerSoobrakjajna.IsDBNull(32) ? null : readerSoobrakjajna.GetString(32);
                        SoobrakjajnaInput.VidNaGorivo = readerSoobrakjajna.IsDBNull(33) ? null : readerSoobrakjajna.GetString(33);
                        SoobrakjajnaInput.BrojNaVrtezi = readerSoobrakjajna.IsDBNull(34) ? null : readerSoobrakjajna.GetString(34);
                        SoobrakjajnaInput.IdentifikacionenBrojNaMotorot = readerSoobrakjajna.IsDBNull(35) ? null : readerSoobrakjajna.GetString(35);
                        SoobrakjajnaInput.MaksimalnaBrzinaKM = readerSoobrakjajna.IsDBNull(36) ? null : readerSoobrakjajna.GetString(36);
                        SoobrakjajnaInput.OdnosSilinaMasa = readerSoobrakjajna.IsDBNull(37) ? null : readerSoobrakjajna.GetString(37);
                        SoobrakjajnaInput.MasaNaVoziloto = readerSoobrakjajna.IsDBNull(38) ? null : readerSoobrakjajna.GetString(38);
                        SoobrakjajnaInput.NajgolemaKonstruktivnaVkupnaMasaNaVozilotoKG = readerSoobrakjajna.IsDBNull(39) ? null : readerSoobrakjajna.GetString(39);
                        SoobrakjajnaInput.NajgolemaLegalnaVkupnaMasaNaVozilotoPriRegistracijaKG = readerSoobrakjajna.IsDBNull(40) ? null : readerSoobrakjajna.GetString(40);
                        SoobrakjajnaInput.NajgolemaLegalnaVkupnaMasaNaGrupaVozilaPriRegistracijaKG = readerSoobrakjajna.IsDBNull(41) ? null : readerSoobrakjajna.GetString(41);
                        SoobrakjajnaInput.BrojNaOski = readerSoobrakjajna.IsDBNull(42) ? null : readerSoobrakjajna.GetInt32(42);
                        SoobrakjajnaInput.RaspredelbaNaNajgolemataKonstruktivnaVkupnaMasaPoOskiKGINaPriklucnataTocka = readerSoobrakjajna.IsDBNull(43) ? null : readerSoobrakjajna.GetString(43);
                        SoobrakjajnaInput.NajgolemoKonstruktivnoOsnoOptovaruvaweKgiNaPriklucnataTocka = readerSoobrakjajna.IsDBNull(44) ? null : readerSoobrakjajna.GetString(44);
                        SoobrakjajnaInput.Dolzhina = readerSoobrakjajna.IsDBNull(45) ? null : readerSoobrakjajna.GetInt32(45);
                        SoobrakjajnaInput.Visina = readerSoobrakjajna.IsDBNull(46) ? null : readerSoobrakjajna.GetInt32(46);
                        SoobrakjajnaInput.NajgolemaKonstruktivnaVkupnaMasaNaKocenaPrikolkaKG = readerSoobrakjajna.IsDBNull(47) ? null : readerSoobrakjajna.GetString(47);
                        SoobrakjajnaInput.NajgolemaKonstruktivnaVkupnaMasaNaNEkocenaPrikolkaKG = readerSoobrakjajna.IsDBNull(48) ? null : readerSoobrakjajna.GetString(48);
                        SoobrakjajnaInput.BrojNaMestaZaStoenje = readerSoobrakjajna.IsDBNull(49) ? null : readerSoobrakjajna.GetInt32(49);
                        SoobrakjajnaInput.DozvoleniPnevmaticiINaplatki = readerSoobrakjajna.IsDBNull(50) ? null : readerSoobrakjajna.GetString(50);
                        SoobrakjajnaInput.BrojNaMestazaLezenje = readerSoobrakjajna.IsDBNull(51) ? null : readerSoobrakjajna.GetString(51);
                        SoobrakjajnaInput.CO2 = readerSoobrakjajna.IsDBNull(52) ? null : readerSoobrakjajna.GetString(52);
                        SoobrakjajnaInput.NajgolemoKonstruktivnoOptovaruvanjeNaPriklucokotZaPrikolka = readerSoobrakjajna.IsDBNull(53) ? null : readerSoobrakjajna.GetString(53);
                        SoobrakjajnaInput.StacionarnaBucavost = readerSoobrakjajna.IsDBNull(54) ? null : readerSoobrakjajna.GetString(54);
                    }
                }
            }

            await LoadFiles();

            await LoadKarticaData(id);
            await LoadOZInformation();
            await LoadDogovoruvacOsigurenikEMNGMB();

            // Add this line to load AOPolisaBroj
            await LoadAOPolisaInfo();

            return Page();
        }

        private async Task LoadAOPolisaInfo()
        {
            if (!string.IsNullOrEmpty(Input.BrojNaAOPolisa))
            {
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    using (SqlCommand cmd = new SqlCommand(@"
                        SELECT p.BrojNaPolisa
                        FROM Polisi p
                        WHERE p.BrojNaPolisa = @BrojNaPolisa", connection))
                    {
                        cmd.Parameters.AddWithValue("@BrojNaPolisa", Input.BrojNaAOPolisa);
                        var result = await cmd.ExecuteScalarAsync();
                        if (result != null)
                        {
                            AOPolisaBroj = result.ToString();
                        }
                    }
                }
            }
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!await HasPageAccess("ViewEditPolisaZK"))
            {
                return RedirectToAccessDenied();
            }

            // Add admin access check
            if (!await HasPageAccess("ViewEditPolisaZKAdmin"))
            {
                return RedirectToAccessDenied();
            }

            string currentUsername = HttpContext.Session.GetString("Username");
            if (string.IsNullOrEmpty(currentUsername))
            {
                return RedirectToPage("/Login");
            }

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    UPDATE Polisi 
                    SET DateModified = GETDATE(),
                        UsernameModified = @UsernameModified,
                        KlientiIdOsiguritel = @KlientiIdOsiguritel,
                        KlasiOsiguruvanjeIdKlasa = @KlasiOsiguruvanjeIdKlasa,
                        ProduktiIdProizvod = @ProduktiIdProizvod,
                        BrojNaPolisa = @BrojNaPolisa,
                        
                        BrojNaPonuda = @BrojNaPonuda,
                        KlientiIdDogovoruvac = @KlientiIdDogovoruvac,
                        KlientiIdOsigurenik = @KlientiIdOsigurenik,
                        Kolektivna = @Kolektivna,
                        KolektivnaNeodredenBrOsigurenici = @KolektivnaNeodredenBrOsigurenici,
                        NeodredenBrOsigureniciZabeleska = @NeodredenBrOsigureniciZabeleska,
                        DatumVaziOd = @DatumVaziOd,
                        DatumVaziDo = @DatumVaziDo,
                        DatumNaIzdavanje = @DatumNaIzdavanje,
                        VremetraenjeNaPolisa = @VremetraenjeNaPolisa,
                        PeriodNaUplata = @PeriodNaUplata,
                        SifrarnikValutiIdValuta = @SifrarnikValutiIdValuta,
                        KlientiIdSorabotnik = @KlientiIdSorabotnik,
                        Faktoring = @Faktoring,
                        SifrarnikValutiIdFranshizaValuta = @SifrarnikValutiIdFranshizaValuta,
                        ProcentFranshiza = @ProcentFranshiza,
                        FranshizaIznos = @FranshizaIznos,
                        ProcentFinansiski = @ProcentFinansiski,
                        KoregiranaStapkaNaProvizija = @KoregiranaStapkaNaProvizija,
                        SifrarnikNacinNaPlakjanjeId = @SifrarnikNacinNaPlakjanjeId,
                        TipNaFaktura = @TipNaFaktura,
                        BrojNaFakturaVlezna = @BrojNaFakturaVlezna,
                        DatumNaFakturaVlezna = @DatumNaFakturaVlezna,
                        RokNaPlakjanjeFakturaVlezna = @RokNaPlakjanjeFakturaVlezna,
                        SifrarnikTipNaPlakanjeId = @SifrarnikTipNaPlakanjeId,
                        SifrarnikBankiIdBanka = @SifrarnikBankiIdBanka,
                        GeneriranaFakturaIzlezna = @GeneriranaFakturaIzlezna,
                        BrojNaFakturaIzlezna = @BrojNaFakturaIzlezna,
                        DatumNaIzleznaFaktura = @DatumNaIzleznaFaktura,
                        RokNaPlakjanjeFakturaIzlezna = @RokNaPlakjanjeFakturaIzlezna,
                        Storno = @Storno,
                        PricinaZaStorno = @PricinaZaStorno,
                        Zabeleska = @Zabeleska
                    WHERE Id = @Id", connection))
                {
                    cmd.Parameters.AddWithValue("@Id", Input.Id);
                    cmd.Parameters.AddWithValue("@UsernameModified", currentUsername);
                    cmd.Parameters.AddWithValue("@KlientiIdOsiguritel", Input.KlientiIdOsiguritel ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@KlasiOsiguruvanjeIdKlasa", Input.KlasiOsiguruvanjeIdKlasa ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@ProduktiIdProizvod", Input.ProduktiIdProizvod ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@BrojNaPolisa", Input.BrojNaPolisa ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@BrojNaPonuda", Input.BrojNaPonuda ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@KlientiIdDogovoruvac", Input.KlientiIdDogovoruvac ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@KlientiIdOsigurenik", Input.KlientiIdOsigurenik ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@Kolektivna", Input.Kolektivna);
                    cmd.Parameters.AddWithValue("@KolektivnaNeodredenBrOsigurenici", Input.KolektivnaNeodredenBrOsigurenici);
                    cmd.Parameters.AddWithValue("@NeodredenBrOsigureniciZabeleska", 
                        (object)Input.NeodredenBrOsigureniciZabeleska ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@DatumVaziOd", 
                        (object)Input.DatumVaziOd ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@DatumVaziDo", 
                        (object)Input.DatumVaziDo ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@DatumNaIzdavanje", 
                        (object)Input.DatumNaIzdavanje ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@VremetraenjeNaPolisa", 
                        (object)Input.VremetraenjeNaPolisa ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@PeriodNaUplata", 
                        (object)Input.PeriodNaUplata ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@SifrarnikValutiIdValuta", Input.SifrarnikValutiIdValuta);
                    cmd.Parameters.AddWithValue("@KlientiIdSorabotnik", 
                        Input.KlientiIdSorabotnik ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@Faktoring", Input.Faktoring);
                    cmd.Parameters.AddWithValue("@SifrarnikValutiIdFranshizaValuta", 
                        Input.SifrarnikValutiIdFranshizaValuta ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@ProcentFranshiza", 
                        Input.ProcentFranshiza ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@FranshizaIznos", 
                        Input.FranshizaIznos ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@ProcentFinansiski", 
                        Input.ProcentFinansiski ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@KoregiranaStapkaNaProvizija", 
                        Input.KoregiranaStapkaNaProvizija ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@SifrarnikNacinNaPlakjanjeId", 
                        Input.SifrarnikNacinNaPlakjanjeId ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@TipNaFaktura", 
                        (object)Input.TipNaFaktura ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@BrojNaFakturaVlezna", 
                        (object)Input.BrojNaFakturaVlezna ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@DatumNaFakturaVlezna", 
                        (object)Input.DatumNaFakturaVlezna ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@RokNaPlakjanjeFakturaVlezna", 
                        (object)Input.RokNaPlakjanjeFakturaVlezna ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@SifrarnikTipNaPlakanjeId", 
                        Input.SifrarnikTipNaPlakanjeId ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@SifrarnikBankiIdBanka", 
                        Input.SifrarnikBankiIdBanka ?? (object)DBNull.Value);
                    cmd.Parameters.AddWithValue("@GeneriranaFakturaIzlezna", Input.GeneriranaFakturaIzlezna);
                    cmd.Parameters.AddWithValue("@BrojNaFakturaIzlezna", 
                        (object)Input.BrojNaFakturaIzlezna ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@DatumNaIzleznaFaktura", 
                        (object)Input.DatumNaIzleznaFaktura ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@RokNaPlakjanjeFakturaIzlezna", 
                        (object)Input.RokNaPlakjanjeFakturaIzlezna ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@Storno", Input.Storno);
                    cmd.Parameters.AddWithValue("@PricinaZaStorno", 
                        (object)Input.PricinaZaStorno ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@Zabeleska", 
                        (object)Input.Zabeleska ?? DBNull.Value);
                    await cmd.ExecuteNonQueryAsync();
                }

                // Add update for PolisiZelenKarton table
                using (SqlCommand cmdZK = new SqlCommand(@"
                    IF EXISTS (SELECT 1 FROM PolisiZelenKarton WHERE PolisaId = @PolisaId)
                        UPDATE PolisiZelenKarton 
                        SET DateModified = GETDATE(),
                            UsernameModified = @UsernameModified,
                            OsnovnaPremija = @OsnovnaPremija,
                            Bonus = @Bonus,
                            Malus = @Malus,
                            Doplatoci = @Doplatoci,
                            Popusti = @Popusti,
                            VkupnoOsnovnaPremijaZK = @VkupnoOsnovnaPremijaZK,
                            KlasiOsiguruvanjeKlasa1 = @KlasiOsiguruvanjeKlasa1,
                            OsigurenaSumaNezgoda = @OsigurenaSumaNezgoda,
                            PremijaZaNezgoda = @PremijaZaNezgoda,
                            KlasiOsiguruvanjeKlasa8 = @KlasiOsiguruvanjeKlasa8,
                            PremijaKrsenjeStakloZK = @PremijaKrsenjeStakloZK,
                            VkupnaPremija = @VkupnaPremija,
                            ProcentNaPopustZaFakturaVoRok = @ProcentNaPopustZaFakturaVoRok,
                            IznosZaPlakjanjeVoRok = @IznosZaPlakjanjeVoRok,
                            ProcentKomercijalenPopust = @ProcentKomercijalenPopust,
                            ProcentFinansiskiPopust = @ProcentFinansiskiPopust,
                            PremijaZaNaplata = @PremijaZaNaplata,
                            Uplateno = @Uplateno,
                            DolznaPremija = @DolznaPremija,
                            BrojNaAOPolisa = @BrojNaAOPolisa
                        WHERE PolisaId = @PolisaId
                    ELSE
                        INSERT INTO PolisiZelenKarton (
                            PolisaId, DateCreated, UsernameCreated, DateModified, UsernameModified,
                            OsnovnaPremija, Bonus, Malus, Doplatoci, Popusti, VkupnoOsnovnaPremijaZK,
                            KlasiOsiguruvanjeKlasa1, OsigurenaSumaNezgoda, PremijaZaNezgoda,
                            KlasiOsiguruvanjeKlasa8, PremijaKrsenjeStakloZK, VkupnaPremija,
                            ProcentNaPopustZaFakturaVoRok, IznosZaPlakjanjeVoRok, ProcentKomercijalenPopust,
                            ProcentFinansiskiPopust, PremijaZaNaplata, Uplateno, DolznaPremija,
                            BrojNaAOPolisa)
                        VALUES (
                            @PolisaId, GETDATE(), @UsernameModified, GETDATE(), @UsernameModified,
                            @OsnovnaPremija, @Bonus, @Malus, @Doplatoci, @Popusti, @VkupnoOsnovnaPremijaZK,
                            @KlasiOsiguruvanjeKlasa1, @OsigurenaSumaNezgoda, @PremijaZaNezgoda,
                            @KlasiOsiguruvanjeKlasa8, @PremijaKrsenjeStakloZK, @VkupnaPremija,
                            @ProcentNaPopustZaFakturaVoRok, @IznosZaPlakjanjeVoRok, @ProcentKomercijalenPopust,
                            @ProcentFinansiskiPopust, @PremijaZaNaplata, @Uplateno, @DolznaPremija,
                            @BrojNaAOPolisa)", connection))
                {
                    cmdZK.Parameters.AddWithValue("@PolisaId", Input.Id);
                    cmdZK.Parameters.AddWithValue("@UsernameModified", currentUsername);
                    cmdZK.Parameters.AddWithValue("@OsnovnaPremija", (object)ZKInput.OsnovnaPremija ?? DBNull.Value);
                    cmdZK.Parameters.AddWithValue("@Bonus", (object)ZKInput.Bonus ?? DBNull.Value);
                    cmdZK.Parameters.AddWithValue("@Malus", (object)ZKInput.Malus ?? DBNull.Value);
                    cmdZK.Parameters.AddWithValue("@Doplatoci", (object)ZKInput.Doplatoci ?? DBNull.Value);
                    cmdZK.Parameters.AddWithValue("@Popusti", (object)ZKInput.Popusti ?? DBNull.Value);
                    cmdZK.Parameters.AddWithValue("@VkupnoOsnovnaPremijaZK", (object)ZKInput.VkupnoOsnovnaPremijaZK ?? DBNull.Value);
                    cmdZK.Parameters.AddWithValue("@KlasiOsiguruvanjeKlasa1", ZKInput.KlasiOsiguruvanjeKlasa1);
                    cmdZK.Parameters.AddWithValue("@OsigurenaSumaNezgoda", (object)ZKInput.OsigurenaSumaNezgoda ?? DBNull.Value);
                    cmdZK.Parameters.AddWithValue("@PremijaZaNezgoda", (object)ZKInput.PremijaZaNezgoda ?? DBNull.Value);
                    cmdZK.Parameters.AddWithValue("@KlasiOsiguruvanjeKlasa8", ZKInput.KlasiOsiguruvanjeKlasa8);
                    cmdZK.Parameters.AddWithValue("@PremijaKrsenjeStakloZK", (object)ZKInput.PremijaKrsenjeStakloZK ?? DBNull.Value);
                    cmdZK.Parameters.AddWithValue("@VkupnaPremija", (object)ZKInput.VkupnaPremija ?? DBNull.Value);
                    cmdZK.Parameters.AddWithValue("@ProcentNaPopustZaFakturaVoRok", (object)ZKInput.ProcentNaPopustZaFakturaVoRok ?? DBNull.Value);
                    cmdZK.Parameters.AddWithValue("@IznosZaPlakjanjeVoRok", (object)ZKInput.IznosZaPlakjanjeVoRok ?? DBNull.Value);
                    cmdZK.Parameters.AddWithValue("@ProcentKomercijalenPopust", (object)ZKInput.ProcentKomercijalenPopust ?? DBNull.Value);
                    cmdZK.Parameters.AddWithValue("@ProcentFinansiskiPopust", (object)ZKInput.ProcentFinansiskiPopust ?? DBNull.Value);
                    cmdZK.Parameters.AddWithValue("@PremijaZaNaplata", (object)ZKInput.PremijaZaNaplata ?? DBNull.Value);
                    cmdZK.Parameters.AddWithValue("@Uplateno", (object)ZKInput.Uplateno ?? DBNull.Value);
                    cmdZK.Parameters.AddWithValue("@DolznaPremija", (object)ZKInput.DolznaPremija ?? DBNull.Value);
                    cmdZK.Parameters.AddWithValue("@BrojNaAOPolisa", (object)ZKInput.BrojNaAOPolisa ?? DBNull.Value);
                    await cmdZK.ExecuteNonQueryAsync();
                }

                // Add update for PolisiZelenKartonSoobrakajna table
                using (SqlCommand cmdSoobrakjajna = new SqlCommand(@"
                    IF EXISTS (SELECT 1 FROM PolisiZelenKartonSoobrakajna WHERE PolisaId = @PolisaId)
                        UPDATE PolisiZelenKartonSoobrakajna 
                        SET DateModified = GETDATE(),
                            UsernameModified = @UsernameModified,
                            RegisterskaOznaka = @RegisterskaOznaka,
                            Marka = @Marka,
                            SifrarnikTipNaVozilo = @SifrarnikTipNaVozilo,
                            KomercijalnaOznaka = @KomercijalnaOznaka,
                            Shasija = @Shasija,
                            GodinaNaProizvodstvo = @GodinaNaProizvodstvo,
                            ZafatninaNaMotorotcm3 = @ZafatninaNaMotorotcm3,
                            SilinaNaMotorotKW = @SilinaNaMotorotKW,
                            BrojNaSedista = @BrojNaSedista,
                            BojaNaVoziloto = @BojaNaVoziloto,
                            NosivostKG = @NosivostKG,
                            DatumNaRegistracija = @DatumNaRegistracija,
                            BrojNaVpisot = @BrojNaVpisot,
                            DatumNaPrvataRegistracija = @DatumNaPrvataRegistracija,
                            PrezimeNazivNaKorisnikot = @PrezimeNazivNaKorisnikot,
                            Ime = @Ime,
                            AdresaNaPostojanoZivealiste = @AdresaNaPostojanoZivealiste,
                            EMBNaKorisnikot = @EMBNaKorisnikot,
                            DatumNaPrvaRegistracijaVoRSM = @DatumNaPrvaRegistracijaVoRSM,
                            DozvolataJaIzdal = @DozvolataJaIzdal,
                            OznakaNaOdobrenie = @OznakaNaOdobrenie,
                            BrojNAEUPotvrdaZaSoobraznost = @BrojNAEUPotvrdaZaSoobraznost,
                            PrezimeNazivNaSopstvenikot = @PrezimeNazivNaSopstvenikot,
                            ImeSopstvenik = @ImeSopstvenik,
                            AdresaNaPostojanoZivealisteSediste = @AdresaNaPostojanoZivealisteSediste,
                            EMBNaFizickoLiceEMBNaPravnoLice = @EMBNaFizickoLiceEMBNaPravnoLice,
                            KategorijaIVidNaVoziloto = @KategorijaIVidNaVoziloto,
                            OblikINamenaNaKaroserijata = @OblikINamenaNaKaroserijata,
                            TipNaMotorot = @TipNaMotorot,
                            VidNaGorivo = @VidNaGorivo,
                            BrojNaVrtezi = @BrojNaVrtezi,
                            IdentifikacionenBrojNaMotorot = @IdentifikacionenBrojNaMotorot,
                            MaksimalnaBrzinaKM = @MaksimalnaBrzinaKM,
                            OdnosSilinaMasa = @OdnosSilinaMasa,
                            MasaNaVoziloto = @MasaNaVoziloto,
                            NajgolemaKonstruktivnaVkupnaMasaNaVozilotoKG = @NajgolemaKonstruktivnaVkupnaMasaNaVozilotoKG,
                            NajgolemaLegalnaVkupnaMasaNaVozilotoPriRegistracijaKG = @NajgolemaLegalnaVkupnaMasaNaVozilotoPriRegistracijaKG,
                            NajgolemaLegalnaVkupnaMasaNaGrupaVozilaPriRegistracijaKG = @NajgolemaLegalnaVkupnaMasaNaGrupaVozilaPriRegistracijaKG,
                            BrojNaOski = @BrojNaOski,
                            RaspredelbaNaNajgolemataKonstruktivnaVkupnaMasaPoOskiKGINaPriklucnataTocka = @RaspredelbaNaNajgolemataKonstruktivnaVkupnaMasaPoOskiKGINaPriklucnataTocka,
                            NajgolemoKonstruktivnoOsnoOptovaruvaweKgiNaPriklucnataTocka = @NajgolemoKonstruktivnoOsnoOptovaruvaweKgiNaPriklucnataTocka,
                            Dolzhina = @Dolzhina,
                            Visina = @Visina,
                            NajgolemaKonstruktivnaVkupnaMasaNaKocenaPrikolkaKG = @NajgolemaKonstruktivnaVkupnaMasaNaKocenaPrikolkaKG,
                            NajgolemaKonstruktivnaVkupnaMasaNaNEkocenaPrikolkaKG = @NajgolemaKonstruktivnaVkupnaMasaNaNEkocenaPrikolkaKG,
                            BrojNaMestaZaStoenje = @BrojNaMestaZaStoenje,
                            DozvoleniPnevmaticiINaplatki = @DozvoleniPnevmaticiINaplatki,
                            BrojNaMestazaLezenje = @BrojNaMestazaLezenje,
                            CO2 = @CO2,
                            NajgolemoKonstruktivnoOptovaruvanjeNaPriklucokotZaPrikolka = @NajgolemoKonstruktivnoOptovaruvanjeNaPriklucokotZaPrikolka,
                            StacionarnaBucavost = @StacionarnaBucavost
                        WHERE PolisaId = @PolisaId
                    ELSE
                        INSERT INTO PolisiZelenKartonSoobrakajna (
                            PolisaId, DateCreated, UsernameCreated, DateModified, UsernameModified,
                            RegisterskaOznaka, Marka, SifrarnikTipNaVozilo,
                            KomercijalnaOznaka, Shasija, GodinaNaProizvodstvo,
                            ZafatninaNaMotorotcm3, SilinaNaMotorotKW, BrojNaSedista,
                            BojaNaVoziloto, NosivostKG, DatumNaRegistracija,
                            BrojNaVpisot, DatumNaPrvataRegistracija,
                            PrezimeNazivNaKorisnikot, Ime,
                            AdresaNaPostojanoZivealiste, EMBNaKorisnikot,
                            DatumNaPrvaRegistracijaVoRSM, DozvolataJaIzdal,
                            OznakaNaOdobrenie, BrojNAEUPotvrdaZaSoobraznost,
                            PrezimeNazivNaSopstvenikot, ImeSopstvenik,
                            AdresaNaPostojanoZivealisteSediste, EMBNaFizickoLiceEMBNaPravnoLice,
                            KategorijaIVidNaVoziloto, OblikINamenaNaKaroserijata,
                            TipNaMotorot, VidNaGorivo, BrojNaVrtezi,
                            IdentifikacionenBrojNaMotorot, MaksimalnaBrzinaKM, OdnosSilinaMasa,
                            MasaNaVoziloto, NajgolemaKonstruktivnaVkupnaMasaNaVozilotoKG,
                            NajgolemaLegalnaVkupnaMasaNaVozilotoPriRegistracijaKG,
                            NajgolemaLegalnaVkupnaMasaNaGrupaVozilaPriRegistracijaKG,
                            BrojNaOski, RaspredelbaNaNajgolemataKonstruktivnaVkupnaMasaPoOskiKGINaPriklucnataTocka,
                            NajgolemoKonstruktivnoOsnoOptovaruvaweKgiNaPriklucnataTocka,
                            Dolzhina, Visina, NajgolemaKonstruktivnaVkupnaMasaNaKocenaPrikolkaKG,
                            NajgolemaKonstruktivnaVkupnaMasaNaNEkocenaPrikolkaKG,
                            BrojNaMestaZaStoenje, DozvoleniPnevmaticiINaplatki,
                            BrojNaMestazaLezenje, CO2,
                            NajgolemoKonstruktivnoOptovaruvanjeNaPriklucokotZaPrikolka,
                            StacionarnaBucavost)
                        VALUES (
                            @PolisaId, GETDATE(), @UsernameModified, GETDATE(), @UsernameModified,
                            @RegisterskaOznaka, @Marka, @SifrarnikTipNaVozilo,
                            @KomercijalnaOznaka, @Shasija, @GodinaNaProizvodstvo,
                            @ZafatninaNaMotorotcm3, @SilinaNaMotorotKW, @BrojNaSedista,
                            @BojaNaVoziloto, @NosivostKG, @DatumNaRegistracija,
                            @BrojNaVpisot, @DatumNaPrvataRegistracija,
                            @PrezimeNazivNaKorisnikot, @Ime,
                            @AdresaNaPostojanoZivealiste, @EMBNaKorisnikot,
                            @DatumNaPrvaRegistracijaVoRSM, @DozvolataJaIzdal,
                            @OznakaNaOdobrenie, @BrojNAEUPotvrdaZaSoobraznost,
                            @PrezimeNazivNaSopstvenikot, @ImeSopstvenik,
                            @AdresaNaPostojanoZivealisteSediste, @EMBNaFizickoLiceEMBNaPravnoLice,
                            @KategorijaIVidNaVoziloto, @OblikINamenaNaKaroserijata,
                            @TipNaMotorot, @VidNaGorivo, @BrojNaVrtezi,
                            @IdentifikacionenBrojNaMotorot, @MaksimalnaBrzinaKM, @OdnosSilinaMasa,
                            @MasaNaVoziloto, @NajgolemaKonstruktivnaVkupnaMasaNaVozilotoKG,
                            @NajgolemaLegalnaVkupnaMasaNaVozilotoPriRegistracijaKG,
                            @NajgolemaLegalnaVkupnaMasaNaGrupaVozilaPriRegistracijaKG,
                            @BrojNaOski, @RaspredelbaNaNajgolemataKonstruktivnaVkupnaMasaPoOskiKGINaPriklucnataTocka,
                            @NajgolemoKonstruktivnoOsnoOptovaruvaweKgiNaPriklucnataTocka,
                            @Dolzhina, @Visina, @NajgolemaKonstruktivnaVkupnaMasaNaKocenaPrikolkaKG,
                            @NajgolemaKonstruktivnaVkupnaMasaNaNEkocenaPrikolkaKG,
                            @BrojNaMestaZaStoenje, @DozvoleniPnevmaticiINaplatki,
                            @BrojNaMestazaLezenje, @CO2,
                            @NajgolemoKonstruktivnoOptovaruvanjeNaPriklucokotZaPrikolka,
                            @StacionarnaBucavost)", connection))
                {
                    cmdSoobrakjajna.Parameters.AddWithValue("@PolisaId", Input.Id);
                    cmdSoobrakjajna.Parameters.AddWithValue("@UsernameModified", currentUsername);
                    cmdSoobrakjajna.Parameters.AddWithValue("@RegisterskaOznaka", (object)SoobrakjajnaInput.RegisterskaOznaka ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@Marka", (object)SoobrakjajnaInput.Marka ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@SifrarnikTipNaVozilo", (object)SoobrakjajnaInput.SifrarnikTipNaVozilo ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@KomercijalnaOznaka", (object)SoobrakjajnaInput.KomercijalnaOznaka ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@Shasija", (object)SoobrakjajnaInput.Shasija ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@GodinaNaProizvodstvo", (object)SoobrakjajnaInput.GodinaNaProizvodstvo ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@ZafatninaNaMotorotcm3", (object)SoobrakjajnaInput.ZafatninaNaMotorotcm3 ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@SilinaNaMotorotKW", (object)SoobrakjajnaInput.SilinaNaMotorotKW ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@BrojNaSedista", (object)SoobrakjajnaInput.BrojNaSedista ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@BojaNaVoziloto", (object)SoobrakjajnaInput.BojaNaVoziloto ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@NosivostKG", (object)SoobrakjajnaInput.NosivostKG ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@DatumNaRegistracija", (object)SoobrakjajnaInput.DatumNaRegistracija ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@BrojNaVpisot", (object)SoobrakjajnaInput.BrojNaVpisot ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@DatumNaPrvataRegistracija", (object)SoobrakjajnaInput.DatumNaPrvataRegistracija ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@PrezimeNazivNaKorisnikot", (object)SoobrakjajnaInput.PrezimeNazivNaKorisnikot ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@Ime", (object)SoobrakjajnaInput.Ime ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@AdresaNaPostojanoZivealiste", (object)SoobrakjajnaInput.AdresaNaPostojanoZivealiste ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@EMBNaKorisnikot", (object)SoobrakjajnaInput.EMBNaKorisnikot ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@DatumNaPrvaRegistracijaVoRSM", (object)SoobrakjajnaInput.DatumNaPrvaRegistracijaVoRSM ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@DozvolataJaIzdal", (object)SoobrakjajnaInput.DozvolataJaIzdal ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@OznakaNaOdobrenie", (object)SoobrakjajnaInput.OznakaNaOdobrenie ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@BrojNAEUPotvrdaZaSoobraznost", (object)SoobrakjajnaInput.BrojNAEUPotvrdaZaSoobraznost ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@PrezimeNazivNaSopstvenikot", (object)SoobrakjajnaInput.PrezimeNazivNaSopstvenikot ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@ImeSopstvenik", (object)SoobrakjajnaInput.ImeSopstvenik ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@AdresaNaPostojanoZivealisteSediste", (object)SoobrakjajnaInput.AdresaNaPostojanoZivealisteSediste ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@EMBNaFizickoLiceEMBNaPravnoLice", (object)SoobrakjajnaInput.EMBNaFizickoLiceEMBNaPravnoLice ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@KategorijaIVidNaVoziloto", (object)SoobrakjajnaInput.KategorijaIVidNaVoziloto ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@OblikINamenaNaKaroserijata", (object)SoobrakjajnaInput.OblikINamenaNaKaroserijata ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@TipNaMotorot", (object)SoobrakjajnaInput.TipNaMotorot ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@VidNaGorivo", (object)SoobrakjajnaInput.VidNaGorivo ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@BrojNaVrtezi", (object)SoobrakjajnaInput.BrojNaVrtezi ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@IdentifikacionenBrojNaMotorot", (object)SoobrakjajnaInput.IdentifikacionenBrojNaMotorot ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@MaksimalnaBrzinaKM", (object)SoobrakjajnaInput.MaksimalnaBrzinaKM ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@OdnosSilinaMasa", (object)SoobrakjajnaInput.OdnosSilinaMasa ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@MasaNaVoziloto", (object)SoobrakjajnaInput.MasaNaVoziloto ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@NajgolemaKonstruktivnaVkupnaMasaNaVozilotoKG", (object)SoobrakjajnaInput.NajgolemaKonstruktivnaVkupnaMasaNaVozilotoKG ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@NajgolemaLegalnaVkupnaMasaNaVozilotoPriRegistracijaKG", (object)SoobrakjajnaInput.NajgolemaLegalnaVkupnaMasaNaVozilotoPriRegistracijaKG ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@NajgolemaLegalnaVkupnaMasaNaGrupaVozilaPriRegistracijaKG", (object)SoobrakjajnaInput.NajgolemaLegalnaVkupnaMasaNaGrupaVozilaPriRegistracijaKG ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@BrojNaOski", (object)SoobrakjajnaInput.BrojNaOski ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@RaspredelbaNaNajgolemataKonstruktivnaVkupnaMasaPoOskiKGINaPriklucnataTocka", (object)SoobrakjajnaInput.RaspredelbaNaNajgolemataKonstruktivnaVkupnaMasaPoOskiKGINaPriklucnataTocka ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@NajgolemoKonstruktivnoOsnoOptovaruvaweKgiNaPriklucnataTocka", (object)SoobrakjajnaInput.NajgolemoKonstruktivnoOsnoOptovaruvaweKgiNaPriklucnataTocka ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@Dolzhina", (object)SoobrakjajnaInput.Dolzhina ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@Visina", (object)SoobrakjajnaInput.Visina ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@NajgolemaKonstruktivnaVkupnaMasaNaKocenaPrikolkaKG", (object)SoobrakjajnaInput.NajgolemaKonstruktivnaVkupnaMasaNaKocenaPrikolkaKG ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@NajgolemaKonstruktivnaVkupnaMasaNaNEkocenaPrikolkaKG", (object)SoobrakjajnaInput.NajgolemaKonstruktivnaVkupnaMasaNaNEkocenaPrikolkaKG ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@BrojNaMestaZaStoenje", (object)SoobrakjajnaInput.BrojNaMestaZaStoenje ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@DozvoleniPnevmaticiINaplatki", (object)SoobrakjajnaInput.DozvoleniPnevmaticiINaplatki ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@BrojNaMestazaLezenje", (object)SoobrakjajnaInput.BrojNaMestazaLezenje ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@CO2", (object)SoobrakjajnaInput.CO2 ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@NajgolemoKonstruktivnoOptovaruvanjeNaPriklucokotZaPrikolka", (object)SoobrakjajnaInput.NajgolemoKonstruktivnoOptovaruvanjeNaPriklucokotZaPrikolka ?? DBNull.Value);
                    cmdSoobrakjajna.Parameters.AddWithValue("@StacionarnaBucavost", (object)SoobrakjajnaInput.StacionarnaBucavost ?? DBNull.Value);
                    await cmdSoobrakjajna.ExecuteNonQueryAsync();
                }
            }

            // In the OnPostAsync method, find and comment out this section:
            if (!string.IsNullOrEmpty(Input.BrojNaAOPolisa))
            {
                using (SqlConnection connectionAO = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connectionAO.OpenAsync();

                    using (SqlCommand cmdUpdateAO = new SqlCommand(@"
                        DECLARE @AOPolisaId bigint;
                        
                        -- First find the PolisiAvtoOdgovornost ID
                        SELECT @AOPolisaId = pao.Id
                        FROM PolisiAvtoOdgovornost pao
                        INNER JOIN Polisi p ON pao.PolisaId = p.Id
                        WHERE p.BrojNaPolisa = @BrojNaPolisa;

                        IF @AOPolisaId IS NOT NULL
                        BEGIN
                            -- Update if exists
                            UPDATE PolisiZelenKarton 
                            SET BrojNaAOPolisa = @AOPolisaId,
                                DateModified = GETDATE(),
                                UsernameModified = @UsernameModified
                            WHERE PolisaId = @PolisaId;

                            -- Insert if doesn't exist
                            IF @@ROWCOUNT = 0
                            BEGIN
                                INSERT INTO PolisiZelenKarton (
                                    PolisaId, 
                                    BrojNaAOPolisa,
                                    DateCreated,
                                    UsernameCreated,
                                    DateModified,
                                    UsernameModified
                                )
                                VALUES (
                                    @PolisaId,
                                    @AOPolisaId,
                                    GETDATE(),
                                    @UsernameModified,
                                    GETDATE(),
                                    @UsernameModified
                                );
                            END
                        END", connectionAO))
                    {
                        cmdUpdateAO.Parameters.AddWithValue("@PolisaId", Input.Id);
                        cmdUpdateAO.Parameters.AddWithValue("@BrojNaPolisa", Input.BrojNaAOPolisa);
                        cmdUpdateAO.Parameters.AddWithValue("@UsernameModified", currentUsername);
                        
                        await cmdUpdateAO.ExecuteNonQueryAsync();
                    }
                }
            }

	            TempData["SuccessMessage"] = "Промените се успешно зачувани.";
            return RedirectToPage(new { id = Input.Id });
        }

        public async Task<JsonResult> OnPostNullifySorabotnikAsync(long id)
        {
            if (!await HasPageAccess("ViewEditPolisaZK"))
            {
                return new JsonResult(new { success = false });
            }

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    UPDATE Polisi 
                    SET KlientiIdSorabotnik = NULL,
                        DateModified = GETDATE(),
                        UsernameModified = @UsernameModified
                    WHERE Id = @Id", connection))
                {
                    cmd.Parameters.AddWithValue("@Id", id);
                    cmd.Parameters.AddWithValue("@UsernameModified", HttpContext.Session.GetString("Username"));
                    await cmd.ExecuteNonQueryAsync();
                }
            }

            return new JsonResult(new { success = true });
        }

        public async Task<IActionResult> OnGetDownloadFileAsync(long fileId)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            string filePath = null;
            string fileName = null;

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand command = new SqlCommand(
                    "SELECT FilePath, FileName FROM PolisiFileSystem WHERE Id = @FileId", connection))
                {
                    command.Parameters.AddWithValue("@FileId", fileId);
                    using (SqlDataReader reader = await command.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                            filePath = reader.GetString(0);
                            fileName = reader.GetString(1);
                        }
                    }
                }
            }

            if (string.IsNullOrEmpty(filePath))
            {
                return NotFound();
            }

            var sftpConfig = _configuration.GetSection("SftpConfig");
            using (var client = new SftpClient(
                sftpConfig["Host"],
                int.Parse(sftpConfig["Port"]),
                sftpConfig["Username"],
                sftpConfig["Password"]))
            {
                client.Connect();

                using (var memoryStream = new MemoryStream())
                {
                    client.DownloadFile(filePath, memoryStream);
                    client.Disconnect();
                    memoryStream.Position = 0;
                    return File(memoryStream.ToArray(), "application/octet-stream", fileName);
                }
            }
        }

        // Add this method to the ViewEditPolisaZKModel class
        public async Task<JsonResult> OnGetSearchPolisiAOAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return new JsonResult(new List<object>());

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT TOP 10 
                        p.Id,
                        p.BrojNaPolisa,
                        k.Naziv as OsigurenikNaziv,
                        k.Ime as OsigurenikIme,
                        k.Prezime as OsigurenikPrezime,
                        p.DatumVaziOd,
                        p.DatumVaziDo
                    FROM Polisi p
                    LEFT JOIN Klienti k ON p.KlientiIdOsigurenik = k.Id
                    WHERE p.KlasiOsiguruvanjeIdKlasa = 10 
                    AND p.ProduktiIdProizvod = 15
                    AND (
                        CAST(p.BrojNaPolisa as varchar) LIKE '%' + @Search + '%'
                    )
                    ORDER BY 
                        CASE 
                            WHEN CAST(p.BrojNaPolisa as varchar) = @Search THEN 1
                            WHEN CAST(p.BrojNaPolisa as varchar) LIKE @Search + '%' THEN 2
                            ELSE 3
                        END,
                        p.BrojNaPolisa DESC", connection))
                {
                    cmd.Parameters.AddWithValue("@Search", searchTerm);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var results = new List<object>();
                    while (await reader.ReadAsync())
                    {
                        var osigurenikDisplay = reader["OsigurenikNaziv"].ToString();
                        if (string.IsNullOrEmpty(osigurenikDisplay))
                        {
                            osigurenikDisplay = $"{reader["OsigurenikIme"]} {reader["OsigurenikPrezime"]}".Trim();
                        }

                        results.Add(new
                        {
                            id = reader["Id"],
                            brojPolisa = reader["BrojNaPolisa"],
                            osigurenik = osigurenikDisplay,
                            datumOd = ((DateTime?)reader["DatumVaziOd"])?.ToString("dd.MM.yyyy"),
                            datumDo = ((DateTime?)reader["DatumVaziDo"])?.ToString("dd.MM.yyyy")
                        });
                    }
                    return new JsonResult(results);
                }
            }
        }
    }
}