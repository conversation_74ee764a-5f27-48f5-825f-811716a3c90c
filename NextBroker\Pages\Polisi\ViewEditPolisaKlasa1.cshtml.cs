using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Renci.SshNet;
using System.IO;
using System.Text;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Runtime.Serialization;
using System.ComponentModel.DataAnnotations;
using System.Reflection;

namespace NextBroker.Pages.Polisi
{
    public class ViewEditPolisaKlasa1Model : SecurePageModel
    {
        private readonly IConfiguration _configuration;
        private readonly IHttpContextAccessor _httpContextAccessor;
        public bool HasAdminAccess { get; private set; }
        public bool HasStornoAccess { get; private set; }

        public ViewEditPolisaKlasa1Model(IConfiguration configuration, IHttpContextAccessor httpContextAccessor)
            : base(configuration)
        {
            _configuration = configuration;
            _httpContextAccessor = httpContextAccessor;
        }

        [BindProperty]
        public PolisaViewModel Input { get; set; } = new();

        [BindProperty]
        public PolisaKlasa1ViewModel InputKlasa1 { get; set; } = new();

        [BindProperty]
        public PolisaKlasa1SoobrakajnaViewModel InputSoobrakajna { get; set; } = new();

        public IEnumerable<SelectListItem> Osiguriteli { get; set; }
        public IEnumerable<SelectListItem> KlasiOsiguruvanje { get; set; }
        public IEnumerable<SelectListItem> Produkti { get; set; }
        public IEnumerable<SelectListItem> Valuti { get; set; }
        public IEnumerable<SelectListItem> NaciniNaPlakjanje { get; set; }
        public IEnumerable<SelectListItem> TipoviNaPlakanje { get; set; }
        public IEnumerable<SelectListItem> Banki { get; set; }
        public IEnumerable<SelectListItem> ListaDejnosti { get; set; }
        public IEnumerable<SelectListItem> TipoviNaVozila { get; set; }
        public IEnumerable<SelectListItem> TipoviNaFaktura { get; set; }

        public List<FileInfo> Files { get; set; }

        // Add this property after other properties
        public System.Data.DataTable KarticaData { get; set; }

        public class ZadolzuvanjeInfo
        {
            public string Zadolzen { get; set; }
            public DateTime? DatumNaZadolzuvanje { get; set; }
            public string OsnovZaRazdolzuvanje { get; set; }
            public bool PotvrdenoRazdolzuvanjeKajBroker { get; set; }
            public DateTime? DatumNaRazdolzuvanjeKajBroker { get; set; }
            public bool PotvrdenoRazdolzuvanjeKajOsiguritel { get; set; }
            public DateTime? DatumNaRazdolzuvanjeKajOsiguritel { get; set; }
        }

        public ZadolzuvanjeInfo ZadolzuvanjeData { get; set; }

        // OZ Information properties
        public decimal OZIznosIzleznaFakturaPremija { get; set; }
        public decimal OZIznosPolisa { get; set; }

        // New properties for database function results
        public string? DogovoruvacEMNGMB { get; set; }
        public string? OsigurenikEMNGMB { get; set; }

        public class PolisaViewModel
        {
            public long Id { get; set; }
            public DateTime? DateCreated { get; set; }
            public string? UsernameCreated { get; set; }
            public DateTime? DateModified { get; set; }
            public string? UsernameModified { get; set; }
            public long? KlientiIdOsiguritel { get; set; }
            public int? KlasiOsiguruvanjeIdKlasa { get; set; }
            public int? ProduktiIdProizvod { get; set; }
            public string? BrojNaPolisa { get; set; }
            public long? BrojNaPonuda { get; set; }
            public long? KlientiIdDogovoruvac { get; set; }
            public long? KlientiIdOsigurenik { get; set; }
            public bool Kolektivna { get; set; }
            public bool KolektivnaNeodredenBrOsigurenici { get; set; }
            public string? NeodredenBrOsigureniciZabeleska { get; set; }
            public DateTime? DatumVaziOd { get; set; }
            public DateTime? DatumVaziDo { get; set; }
            public DateTime? DatumNaIzdavanje { get; set; }
            public int? VremetraenjeNaPolisa { get; set; }
            public int? PeriodNaUplata { get; set; }
            public long SifrarnikValutiIdValuta { get; set; }
            public long? SifrarnikValutiIdFranshizaValuta { get; set; }
            public long? KlientiIdSorabotnik { get; set; }
            public bool Faktoring { get; set; }
            public decimal? ProcentFranshiza { get; set; }
            public decimal? ProcentFinansiski { get; set; }
            public decimal? KoregiranaStapkaNaProvizija { get; set; }
            public long? SifrarnikNacinNaPlakjanjeId { get; set; }
            public string? TipNaFaktura { get; set; }
            public string? BrojNaFakturaVlezna { get; set; }
            public DateTime? DatumNaFakturaVlezna { get; set; }
            public DateTime? RokNaPlakjanjeFakturaVlezna { get; set; }
            public long? SifrarnikTipNaPlakanjeId { get; set; }
            public long? SifrarnikBankiIdBanka { get; set; }
            public bool GeneriranaFakturaIzlezna { get; set; }
            public string? BrojNaFakturaIzlezna { get; set; }
            public DateTime? DatumNaIzleznaFaktura { get; set; }
            public DateTime? RokNaPlakjanjeFakturaIzlezna { get; set; }
            public bool Storno { get; set; }
            public string? PricinaZaStorno { get; set; }
            public string? Zabeleska { get; set; }
            public decimal? FranshizaIznos { get; set; }
        }

        public class PolisaKlasa1ViewModel
        {
            public long Id { get; set; }
            public DateTime? DateCreated { get; set; }
            public string? UsernameCreated { get; set; }
            public DateTime? DateModified { get; set; }
            public string? UsernameModified { get; set; }
            public long PolisaId { get; set; }
            public int? ListaDejnostiId { get; set; }
            public int? BrojNaOsigurenici { get; set; }
            public decimal? PremijaZaEdnoLice { get; set; }
            public decimal? VkupnaPremija { get; set; }
            public decimal? SmrtOdNesrekjenSlucaj { get; set; }
            public decimal? TraenInvaliditet { get; set; }
            public decimal? TraenInvaliditet100Procenti { get; set; }
            public decimal? DnevenNadomest { get; set; }
            public decimal? Lekuvanje { get; set; }
            public decimal? TeskiBolesti { get; set; }
            public decimal? TrosociZaPogrebZaSmrtOdNezgoda { get; set; }
            public decimal? TrosociZaOstavinskaPostapka { get; set; }
            public decimal? TrosociZaOperacijaNezgoda { get; set; }
            public decimal? TrosociZaObrazovanie { get; set; }
            public decimal? ProcentNaPopustZaFakturaVoRok { get; set; }
            public decimal? IznosZaPlakjanjeVoRok { get; set; }
            public decimal? ProcentKomercijalenPopust { get; set; }
            public decimal? ProcentFinansiski { get; set; }
            public decimal? PremijaZaNaplata { get; set; }
        }

        public class PolisaKlasa1SoobrakajnaViewModel
        {
            public long Id { get; set; }
            public DateTime? DateCreated { get; set; }
            public string? UsernameCreated { get; set; }
            public DateTime? DateModified { get; set; }
            public string? UsernameModified { get; set; }
            public long PolisaId { get; set; }
            public string? RegisterskaOznaka { get; set; }
            public string? Marka { get; set; }
            public long? SifrarnikTipNaVozilo { get; set; }
            public string? KomercijalnaOznaka { get; set; }
            public string? Shasija { get; set; }
            public int? GodinaNaProizvodstvo { get; set; }
            public int? ZafatninaNaMotorotcm3 { get; set; }
            public int? SilinaNaMotorotKW { get; set; }
            public int? BrojNaSedista { get; set; }
            public string? BojaNaVoziloto { get; set; }
            public int? NosivostKG { get; set; }
            public DateTime? DatumNaRegistracija { get; set; }
            public string? BrojNaVpisot { get; set; }
            public DateTime? DatumNaPrvataRegistracija { get; set; }
            public string? PrezimeNazivNaKorisnikot { get; set; }
            public string? Ime { get; set; }
            public string? AdresaNaPostojanoZivealiste { get; set; }
            public string? EMBNaKorisnikot { get; set; }
            public string? DatumNaPrvaRegistracijaVoRSM { get; set; }
            public string? DozvolataJaIzdal { get; set; }
            public string? OznakaNaOdobrenie { get; set; }
            public string? BrojNAEUPotvrdaZaSoobraznost { get; set; }
            public string? PrezimeNazivNaSopstvenikot { get; set; }
            public string? ImeSopstvenik { get; set; }
            public string? AdresaNaPostojanoZivealisteSediste { get; set; }
            public string? EMBNaFizickoLiceEMBNaPravnoLice { get; set; }
            public string? KategorijaIVidNaVoziloto { get; set; }
            public string? OblikINamenaNaKaroserijata { get; set; }
            public string? TipNaMotorot { get; set; }
            public string? VidNaGorivo { get; set; }
            public string? BrojNaVrtezi { get; set; }
            public string? IdentifikacionenBrojNaMotorot { get; set; }
            public string? MaksimalnaBrzinaKM { get; set; }
            public string? OdnosSilinaMasa { get; set; }
            public string? MasaNaVoziloto { get; set; }
            public string? NajgolemaKonstruktivnaVkupnaMasaNaVozilotoKG { get; set; }
            public string? NajgolemaLegalnaVkupnaMasaNaVozilotoPriRegistracijaKG { get; set; }
            public string? NajgolemaLegalnaVkupnaMasaNaGrupaVozilaPriRegistracijaKG { get; set; }
            public int? BrojNaOski { get; set; }
            public string? RaspredelbaNaNajgolemataKonstruktivnaVkupnaMasaPoOskiKGINaPriklucnataTocka { get; set; }
            public string? NajgolemoKonstruktivnoOsnoOptovaruwaweKgiNaPriklucnataTocka { get; set; }
            public int? Dolzhina { get; set; }
            public int? Visina { get; set; }
            public string? NajgolemaKonstruktivnaVkupnaMasaNaKocenaPrikolkaKG { get; set; }
            public string? NajgolemaKonstruktivnaVkupnaMasaNaNEkocenaPrikolkaKG { get; set; }
            public int? BrojNaMestaZaStoenje { get; set; }
            public string? DozvoleniPnevmaticiINaplatki { get; set; }
            public int? BrojNaMestazaLezenje { get; set; }
            public string? CO2 { get; set; }
            public string? NajgolemoKonstruktivnoOptovaruvanjeNaPriklucokotZaPrikolka { get; set; }
            public string? StacionarnaBucavost { get; set; }
        }

        public class FileInfo
        {
            public long Id { get; set; }
            public string FileName { get; set; }
            public DateTime DateCreated { get; set; }
            public string UsernameCreated { get; set; }
        }

        public class OsigurenikKolektivno
        {
            public long Id { get; set; }
            public string Ime { get; set; }
            public string Prezime { get; set; }
            public string EMBG { get; set; }
            public string BrojNaLicnaKarta { get; set; }
            public string BrojNaPasos { get; set; }
            public string ListaOpstiniId { get; set; }
            public string OpstinaNaziv { get; set; }
            public string Adresa { get; set; }
            public string Broj { get; set; }
            public string Telefon { get; set; }
            public string Email { get; set; }
        }

        public List<OsigurenikKolektivno> OsigureniciKolektivno { get; set; }

        private async Task LoadOsiguriteli()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Naziv 
                    FROM Klienti 
                    WHERE Osiguritel = 1 
                    ORDER BY Naziv", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Naziv"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Osiguriteli = items;
                }
            }
        }

        private async Task LoadKlasiOsiguruvanje()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, CONCAT(KlasaBroj, ' - ', KlasaIme) as DisplayName 
                    FROM KlasiOsiguruvanje 
                    WHERE KlasaBroj = 1 
                    AND (Disabled = 0 OR Disabled IS NULL)", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["DisplayName"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    KlasiOsiguruvanje = items;
                }
            }
        }

        private async Task LoadProdukti()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Ime 
                    FROM Produkti 
                    WHERE KlasaOsiguruvanjeId = 1", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Ime"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Produkti = items;
                }
            }
        }

        private async Task LoadValuti()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Valuta 
                    FROM SifrarnikValuti 
                    ORDER BY Valuta", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Valuta"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Valuti = items;
                }
            }
        }

        private async Task LoadNaciniNaPlakjanje()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, NacinNaPlakanje 
                    FROM SifrarnikNacinNaPlakanje 
                    ORDER BY NacinNaPlakanje", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["NacinNaPlakanje"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    NaciniNaPlakjanje = items;
                }
            }
        }

        private async Task LoadTipoviNaPlakanje()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, TipNaPlakanje 
                    FROM SifrarnikTipNaPlakanje 
                    ORDER BY TipNaPlakanje", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["TipNaPlakanje"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    TipoviNaPlakanje = items;
                }
            }
        }

        private async Task LoadBanki()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Banka 
                    FROM SifrarnikBanki 
                    ORDER BY Banka", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Banka"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Banki = items;
                }
            }
        }

        private async Task LoadListaDejnosti()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, NazivDejnost 
                    FROM ListaDejnosti 
                    ORDER BY NazivDejnost", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["NazivDejnost"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    ListaDejnosti = items;
                }
            }
        }

        private async Task LoadTipoviNaVozila()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, TipNaVozilo 
                    FROM SifrarnikTipNaVozilo 
                    ORDER BY TipNaVozilo", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["TipNaVozilo"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    TipoviNaVozila = items;
                }
            }
        }

        private async Task LoadFiles()
        {
            Files = new List<FileInfo>();
            string connectionString = _configuration.GetConnectionString("DefaultConnection");

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand command = new SqlCommand(
                    "SELECT Id, FileName, DateCreated, UsernameCreated " +
                    "FROM PolisiFileSystem " +
                    "WHERE PolisaId = @PolisaId " +
                    "ORDER BY DateCreated DESC", connection))
                {
                    command.Parameters.AddWithValue("@PolisaId", Input.Id);

                    using (SqlDataReader reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            Files.Add(new FileInfo
                            {
                                Id = reader.GetInt64(0),
                                FileName = reader.GetString(1),
                                DateCreated = reader.GetDateTime(2),
                                UsernameCreated = reader.GetString(3)
                            });
                        }
                    }
                }
            }
        }

        private async Task LoadOsigureniciKolektivno()
        {
            OsigureniciKolektivno = new List<OsigurenikKolektivno>();
            
            using (SqlConnection connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();

                string query = @"
                    SELECT 
                        pok.*,
                        lo.Opstina as OpstinaNaziv
                    FROM PolisiOsigureniciKolektivno pok
                    LEFT JOIN ListaOpstini lo ON lo.Id = TRY_CAST(pok.ListaOpstiniId AS INT)
                    WHERE pok.PolisaId = @PolisaId
                    ORDER BY pok.Id";

                using (SqlCommand command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@PolisaId", Input.Id);

                    using (SqlDataReader reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            OsigureniciKolektivno.Add(new OsigurenikKolektivno
                            {
                                Id = reader.GetInt64(reader.GetOrdinal("Id")),
                                Ime = reader.IsDBNull(reader.GetOrdinal("Ime")) ? null : reader.GetString(reader.GetOrdinal("Ime")),
                                Prezime = reader.IsDBNull(reader.GetOrdinal("Prezime")) ? null : reader.GetString(reader.GetOrdinal("Prezime")),
                                EMBG = reader.IsDBNull(reader.GetOrdinal("EMBG")) ? null : reader.GetString(reader.GetOrdinal("EMBG")),
                                BrojNaLicnaKarta = reader.IsDBNull(reader.GetOrdinal("BrojNaLicnaKarta")) ? null : reader.GetString(reader.GetOrdinal("BrojNaLicnaKarta")),
                                BrojNaPasos = reader.IsDBNull(reader.GetOrdinal("BrojNaPasos")) ? null : reader.GetString(reader.GetOrdinal("BrojNaPasos")),
                                ListaOpstiniId = reader.IsDBNull(reader.GetOrdinal("ListaOpstiniId")) ? null : reader.GetString(reader.GetOrdinal("ListaOpstiniId")),
                                OpstinaNaziv = reader.IsDBNull(reader.GetOrdinal("OpstinaNaziv")) ? null : reader.GetString(reader.GetOrdinal("OpstinaNaziv")),
                                Adresa = reader.IsDBNull(reader.GetOrdinal("Adresa")) ? null : reader.GetString(reader.GetOrdinal("Adresa")),
                                Broj = reader.IsDBNull(reader.GetOrdinal("Broj")) ? null : reader.GetString(reader.GetOrdinal("Broj")),
                                Telefon = reader.IsDBNull(reader.GetOrdinal("Telefon")) ? null : reader.GetString(reader.GetOrdinal("Telefon")),
                                Email = reader.IsDBNull(reader.GetOrdinal("Email")) ? null : reader.GetString(reader.GetOrdinal("Email"))
                            });
                        }
                    }
                }
            }
        }

        private async Task LoadKarticaData(long polisaId)
        {
            using (SqlConnection conn = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await conn.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT 
                        [ID],
                        [Рата број],
                        [Датум на доспевање],
                        [Износ на рата],
                        [Датум на уплата],
                        [Уплатен износ],
                        [Затворена рата],
                        [Сторно]
                    FROM PolisaKartica 
                    WHERE PolisaID = @PolisaID
                    ORDER BY [Рата број]", conn))
                {
                    cmd.Parameters.AddWithValue("@PolisaID", polisaId);
                    using (SqlDataAdapter adapter = new SqlDataAdapter(cmd))
                    {
                        KarticaData = new System.Data.DataTable();
                        adapter.Fill(KarticaData);
                    }
                }
            }
        }

        private async Task LoadZadolzuvanjeData()
        {
            if (string.IsNullOrEmpty(Input.BrojNaPolisa)) return;
            
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT
                        CAST(
                            COALESCE(kl.Ime, '') + ' ' + 
                            COALESCE(kl.Prezime, '') + ' ' + 
                            COALESCE(kl.Naziv, '') 
                        AS VARCHAR(MAX)) AS Zadolzen,
                        pzr.DatumNaZadolzuvanje,
                        sozr.OsnovZaRazdolzuvanje,
                        pzr.PotvrdenoRazdolzuvanjeKajBroker,
                        pzr.DatumNaRazdolzuvanjeKajBroker,
                        pzr.PotvrdenoRazdolzuvanjeKajOsiguritel,
                        pzr.DatumNaRazdolzuvanjeKajOsiguritel
                    FROM PolisiZadolzuvanjeRazdolzuvanje pzr
                    LEFT JOIN Klienti kl on pzr.KlientiIdZadolzen = kl.Id
                    LEFT JOIN SifrarnikOsnovZaRazdolzuvanje sozr on pzr.SifrarnikOsnovZaRazdolzuvanjeId = sozr.Id
                    LEFT JOIN Polisi p on pzr.BrojNaPolisa = p.BrojNaPolisa
                    WHERE pzr.BrojNaPolisa = @brojnapolisa
                    AND (p.Storno is null or p.Storno != 1)", connection))
                {
                    cmd.Parameters.AddWithValue("@brojnapolisa", Input.BrojNaPolisa);
                    using (var reader = await cmd.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                            ZadolzuvanjeData = new ZadolzuvanjeInfo
                            {
                                Zadolzen = reader.IsDBNull(0) ? null : reader.GetString(0),
                                DatumNaZadolzuvanje = reader.IsDBNull(1) ? null : reader.GetDateTime(1),
                                OsnovZaRazdolzuvanje = reader.IsDBNull(2) ? null : reader.GetString(2),
                                PotvrdenoRazdolzuvanjeKajBroker = !reader.IsDBNull(3) && reader.GetBoolean(3),
                                DatumNaRazdolzuvanjeKajBroker = reader.IsDBNull(4) ? null : reader.GetDateTime(4),
                                PotvrdenoRazdolzuvanjeKajOsiguritel = !reader.IsDBNull(5) && reader.GetBoolean(5),
                                DatumNaRazdolzuvanjeKajOsiguritel = reader.IsDBNull(6) ? null : reader.GetDateTime(6)
                            };
                        }
                    }
                }
            }
        }

        private async Task LoadOZInformation()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                try
                {
                    // Load OZ Iznos Izlezna Faktura Premija
                    using (SqlCommand cmd = new SqlCommand("SELECT dbo.VraziOZIznosIzleznaFakturaPremija((select top 1 BrojNaFakturaIzlezna from polisi where id = @polisaId order by DateCreated Desc))", connection))
                    {
                        cmd.Parameters.AddWithValue("@polisaId", Input.Id);
                        var result = await cmd.ExecuteScalarAsync();
                        OZIznosIzleznaFakturaPremija = result != null && result != DBNull.Value ? Convert.ToDecimal(result) : 0;
                    }

                    // Load OZ Iznos Polisa
                    using (SqlCommand cmd = new SqlCommand("SELECT dbo.VraziOZIznosPolisa(@polisaId)", connection))
                    {
                        cmd.Parameters.AddWithValue("@polisaId", Input.Id);
                        var result = await cmd.ExecuteScalarAsync();
                        OZIznosPolisa = result != null && result != DBNull.Value ? Convert.ToDecimal(result) : 0;
                    }
                }
                catch (Exception ex)
                {
                    // Handle any errors gracefully
                    OZIznosIzleznaFakturaPremija = 0;
                    OZIznosPolisa = 0;
                }
            }
        }

        private async Task LoadDogovoruvacOsigurenikEMNGMB()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                try
                {
                    // Load Dogovoruvac EMNMGB
                    using (SqlCommand cmd = new SqlCommand("SELECT dbo.VratiPolisaDogovoruvacEMNGMB(@polisaId)", connection))
                    {
                        cmd.Parameters.AddWithValue("@polisaId", Input.Id);
                        var result = await cmd.ExecuteScalarAsync();
                        DogovoruvacEMNGMB = result != null && result != DBNull.Value ? result.ToString() : null;
                    }

                    // Load Osigurenik EMNMGB
                    using (SqlCommand cmd = new SqlCommand("SELECT dbo.VratiPolisaOsigurenikEMNGMB(@polisaId)", connection))
                    {
                        cmd.Parameters.AddWithValue("@polisaId", Input.Id);
                        var result = await cmd.ExecuteScalarAsync();
                        OsigurenikEMNGMB = result != null && result != DBNull.Value ? result.ToString() : null;
                    }
                }
                catch (Exception ex)
                {
                    // Log the error or handle it appropriately
                    Console.WriteLine($"Error loading Dogovoruvac/Osigurenik EMNMGB: {ex.Message}");
                    DogovoruvacEMNGMB = null;
                    OsigurenikEMNGMB = null;
                }
            }
        }

        private void LoadTipoviNaFaktura()
        {
            TipoviNaFaktura = Enum.GetValues(typeof(TipNaFakturaenumKlasa1))
                .Cast<TipNaFakturaenumKlasa1>()
                .Select(e => new SelectListItem
                {
                    Text = e.GetType()
                        .GetMember(e.ToString())
                        .First()
                        .GetCustomAttribute<DisplayAttribute>()
                        ?.Name ?? e.ToString(),
                    Value = e.GetType()
                        .GetMember(e.ToString())
                        .First()
                        .GetCustomAttribute<EnumMemberAttribute>()
                        ?.Value ?? e.ToString()
                })
                .ToList();
        }

        public async Task<IActionResult> OnGetAsync(long id)
        {
            if (!await HasPageAccess("ViewEditPolisaKlasa1"))
            {
                return RedirectToAccessDenied();
            }

            // Check admin access
            HasAdminAccess = await HasPageAccess("ViewEditPolisaKlasa1Admin");

            // Check storno access
            HasStornoAccess = await HasPageAccess("StorniranjePolisi");

            await LoadOsiguriteli();
            await LoadKlasiOsiguruvanje();
            await LoadProdukti();
            await LoadValuti();
            await LoadNaciniNaPlakjanje();
            await LoadTipoviNaPlakanje();
            await LoadBanki();
            await LoadListaDejnosti();
            await LoadTipoviNaVozila();
            LoadTipoviNaFaktura();

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT p.Id, p.DateCreated, p.UsernameCreated, p.DateModified, p.UsernameModified, 
                           p.KlientiIdOsiguritel, p.KlasiOsiguruvanjeIdKlasa, p.ProduktiIdProizvod,
                           p.BrojNaPolisa, p.BrojNaPonuda, p.KlientiIdDogovoruvac, p.KlientiIdOsigurenik,
                           p.Kolektivna, p.KolektivnaNeodredenBrOsigurenici, p.NeodredenBrOsigureniciZabeleska,
                           p.DatumVaziOd, p.DatumVaziDo, p.DatumNaIzdavanje, p.VremetraenjeNaPolisa, p.PeriodNaUplata,
                           p.SifrarnikValutiIdValuta, p.KlientiIdSorabotnik, p.Faktoring, p.SifrarnikValutiIdFranshizaValuta,
                           p.ProcentFranshiza, p.ProcentFinansiski, p.KoregiranaStapkaNaProvizija,
                           p.SifrarnikNacinNaPlakjanjeId,
                           p.TipNaFaktura, p.BrojNaFakturaVlezna,
                           p.DatumNaFakturaVlezna, p.RokNaPlakjanjeFakturaVlezna,
                           p.SifrarnikTipNaPlakanjeId,
                           p.SifrarnikBankiIdBanka,
                           p.GeneriranaFakturaIzlezna, p.BrojNaFakturaIzlezna,
                           p.DatumNaIzleznaFaktura, p.RokNaPlakjanjeFakturaIzlezna,
                           p.Storno, p.PricinaZaStorno, p.Zabeleska, p.FranshizaIznos,
                           CASE 
                               WHEN k.KlientFizickoPravnoLice = 'P' THEN k.Naziv
                               ELSE CONCAT(ISNULL(k.Ime, ''), ' ', ISNULL(k.Prezime, ''))
                           END as DogovoruvacNaziv,
                           CASE 
                               WHEN k2.KlientFizickoPravnoLice = 'P' THEN k2.Naziv
                               ELSE CONCAT(ISNULL(k2.Ime, ''), ' ', ISNULL(k2.Prezime, ''))
                           END as OsigurenikNaziv,
                           CASE 
                               WHEN k3.KlientFizickoPravnoLice = 'P' THEN k3.Naziv
                               ELSE CONCAT(ISNULL(k3.Ime, ''), ' ', ISNULL(k3.Prezime, ''))
                           END as SorabotnikNaziv
                    FROM Polisi p
                    LEFT JOIN Klienti k ON p.KlientiIdDogovoruvac = k.Id
                    LEFT JOIN Klienti k2 ON p.KlientiIdOsigurenik = k2.Id
                    LEFT JOIN Klienti k3 ON p.KlientiIdSorabotnik = k3.Id
                    WHERE p.Id = @Id", connection))
                {
                    cmd.Parameters.AddWithValue("@Id", id);
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    if (await reader.ReadAsync())
                    {
                        Input.Id = reader.GetInt64(0);
                        Input.DateCreated = reader.IsDBNull(1) ? null : reader.GetDateTime(1);
                        Input.UsernameCreated = reader.GetString(2);
                        Input.DateModified = reader.IsDBNull(3) ? null : reader.GetDateTime(3);
                        Input.UsernameModified = reader.IsDBNull(4) ? null : reader.GetString(4);
                        Input.KlientiIdOsiguritel = reader.IsDBNull(5) ? null : reader.GetInt64(5);
                        Input.KlasiOsiguruvanjeIdKlasa = reader.IsDBNull(6) ? null : reader.GetInt32(6);
                        Input.ProduktiIdProizvod = reader.IsDBNull(7) ? null : reader.GetInt32(7);
                        Input.BrojNaPolisa = reader.IsDBNull(8) ? null : reader.GetString(8);
                        Input.BrojNaPonuda = reader.IsDBNull(9) ? null : reader.GetInt64(9);
                        Input.KlientiIdDogovoruvac = reader.IsDBNull(10) ? null : reader.GetInt64(10);
                        Input.KlientiIdOsigurenik = reader.IsDBNull(11) ? null : reader.GetInt64(11);
                        Input.Kolektivna = !reader.IsDBNull(12) && reader.GetBoolean(12);
                        Input.KolektivnaNeodredenBrOsigurenici = !reader.IsDBNull(13) && reader.GetBoolean(13);
                        Input.NeodredenBrOsigureniciZabeleska = reader.IsDBNull(14) ? null : reader.GetString(14);
                        Input.DatumVaziOd = reader.IsDBNull(15) ? null : reader.GetDateTime(15);
                        Input.DatumVaziDo = reader.IsDBNull(16) ? null : reader.GetDateTime(16);
                        Input.DatumNaIzdavanje = reader.IsDBNull(17) ? null : reader.GetDateTime(17);
                        Input.VremetraenjeNaPolisa = reader.IsDBNull(18) ? null : reader.GetInt32(18);
                        Input.PeriodNaUplata = reader.IsDBNull(19) ? null : reader.GetInt32(19);
                        Input.SifrarnikValutiIdValuta = reader.IsDBNull(20) ? 0 : reader.GetInt64(20);
                        Input.KlientiIdSorabotnik = reader.IsDBNull(21) ? null : reader.GetInt64(21);
                        Input.Faktoring = !reader.IsDBNull(22) && reader.GetBoolean(22);
                        Input.SifrarnikValutiIdFranshizaValuta = reader.IsDBNull(23) ? null : reader.GetInt64(23);
                        Input.ProcentFranshiza = reader.IsDBNull(24) ? null : reader.GetDecimal(24);
                        Input.ProcentFinansiski = reader.IsDBNull(25) ? null : reader.GetDecimal(25);
                        Input.KoregiranaStapkaNaProvizija = reader.IsDBNull(26) ? null : reader.GetDecimal(26);
                        Input.SifrarnikNacinNaPlakjanjeId = reader.IsDBNull(27) ? null : reader.GetInt64(27);
                        Input.TipNaFaktura = reader.IsDBNull(28) ? null : reader.GetString(28);
                        Input.BrojNaFakturaVlezna = reader.IsDBNull(29) ? null : reader.GetString(29);
                        Input.DatumNaFakturaVlezna = reader.IsDBNull(30) ? null : reader.GetDateTime(30);
                        Input.RokNaPlakjanjeFakturaVlezna = reader.IsDBNull(31) ? null : reader.GetDateTime(31);
                        Input.SifrarnikTipNaPlakanjeId = reader.IsDBNull(32) ? null : reader.GetInt64(32);
                        Input.SifrarnikBankiIdBanka = reader.IsDBNull(33) ? null : reader.GetInt64(33);
                        Input.GeneriranaFakturaIzlezna = !reader.IsDBNull(34) && reader.GetBoolean(34);
                        Input.BrojNaFakturaIzlezna = reader.IsDBNull(35) ? null : reader.GetString(35);
                        Input.DatumNaIzleznaFaktura = reader.IsDBNull(36) ? null : reader.GetDateTime(36);
                        Input.RokNaPlakjanjeFakturaIzlezna = reader.IsDBNull(37) ? null : reader.GetDateTime(37);
                        Input.Storno = !reader.IsDBNull(38) && reader.GetBoolean(38);
                        Input.PricinaZaStorno = reader.IsDBNull(39) ? null : reader.GetString(39);
                        Input.Zabeleska = reader.IsDBNull(40) ? null : reader.GetString(40);
                        Input.FranshizaIznos = reader.IsDBNull(41) ? null : reader.GetDecimal(41);
                        ViewData["DogovoruvacNaziv"] = reader.IsDBNull(42) ? "" : reader.GetString(42);
                        ViewData["OsigurenikNaziv"] = reader.IsDBNull(43) ? "" : reader.GetString(43);
                        ViewData["SorabotnikNaziv"] = reader.IsDBNull(44) ? "" : reader.GetString(44);
                    }
                }
            }

            // Load PolisiKlasa1 data
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, DateCreated, UsernameCreated, DateModified, UsernameModified,
                           PolisaId, ListaDejnostiId, BrojNaOsigurenici, PremijaZaEdnoLice,
                           VkupnaPremija, SmrtOdNesrekjenSlucaj, TraenInvaliditet,
                           TraenInvaliditet100Procenti, DnevenNadomest, Lekuvanje,
                           TeskiBolesti, TrosociZaPogrebZaSmrtOdNezgoda,
                           TrosociZaOstavinskaPostapka, TrosociZaOperacijaNezgoda,
                           TrosociZaObrazovanie, ProcentNaPopustZaFakturaVoRok,
                           IznosZaPlakjanjeVoRok, ProcentKomercijalenPopust,
                           ProcentFinansiski, PremijaZaNaplata
                    FROM PolisiKlasa1
                    WHERE PolisaId = @PolisaId", connection))
                {
                    cmd.Parameters.AddWithValue("@PolisaId", id);
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    if (await reader.ReadAsync())
                    {
                        InputKlasa1.Id = reader.GetInt64(0);
                        InputKlasa1.DateCreated = reader.IsDBNull(1) ? null : reader.GetDateTime(1);
                        InputKlasa1.UsernameCreated = reader.IsDBNull(2) ? null : reader.GetString(2);
                        InputKlasa1.DateModified = reader.IsDBNull(3) ? null : reader.GetDateTime(3);
                        InputKlasa1.UsernameModified = reader.IsDBNull(4) ? null : reader.GetString(4);
                        InputKlasa1.PolisaId = reader.GetInt64(5);
                        InputKlasa1.ListaDejnostiId = reader.IsDBNull(6) ? null : reader.GetInt32(6);
                        InputKlasa1.BrojNaOsigurenici = reader.IsDBNull(7) ? null : reader.GetInt32(7);
                        InputKlasa1.PremijaZaEdnoLice = reader.IsDBNull(8) ? null : reader.GetDecimal(8);
                        InputKlasa1.VkupnaPremija = reader.IsDBNull(9) ? null : reader.GetDecimal(9);
                        InputKlasa1.SmrtOdNesrekjenSlucaj = reader.IsDBNull(10) ? null : reader.GetDecimal(10);
                        InputKlasa1.TraenInvaliditet = reader.IsDBNull(11) ? null : reader.GetDecimal(11);
                        InputKlasa1.TraenInvaliditet100Procenti = reader.IsDBNull(12) ? null : reader.GetDecimal(12);
                        InputKlasa1.DnevenNadomest = reader.IsDBNull(13) ? null : reader.GetDecimal(13);
                        InputKlasa1.Lekuvanje = reader.IsDBNull(14) ? null : reader.GetDecimal(14);
                        InputKlasa1.TeskiBolesti = reader.IsDBNull(15) ? null : reader.GetDecimal(15);
                        InputKlasa1.TrosociZaPogrebZaSmrtOdNezgoda = reader.IsDBNull(16) ? null : reader.GetDecimal(16);
                        InputKlasa1.TrosociZaOstavinskaPostapka = reader.IsDBNull(17) ? null : reader.GetDecimal(17);
                        InputKlasa1.TrosociZaOperacijaNezgoda = reader.IsDBNull(18) ? null : reader.GetDecimal(18);
                        InputKlasa1.TrosociZaObrazovanie = reader.IsDBNull(19) ? null : reader.GetDecimal(19);
                        InputKlasa1.ProcentNaPopustZaFakturaVoRok = reader.IsDBNull(20) ? null : reader.GetDecimal(20);
                        InputKlasa1.IznosZaPlakjanjeVoRok = reader.IsDBNull(21) ? null : reader.GetDecimal(21);
                        InputKlasa1.ProcentKomercijalenPopust = reader.IsDBNull(22) ? null : reader.GetDecimal(22);
                        InputKlasa1.ProcentFinansiski = reader.IsDBNull(23) ? null : reader.GetDecimal(23);
                        InputKlasa1.PremijaZaNaplata = reader.IsDBNull(24) ? null : reader.GetDecimal(24);
                    }
                    else
                    {
                        // If no record exists, create a new one with the PolisaId
                        InputKlasa1.PolisaId = id;
                    }
                }
            }

            // Load PolisiKlasa1Soobrakajna data
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, DateCreated, UsernameCreated, DateModified, UsernameModified,
                           PolisaId, RegisterskaOznaka, Marka, SifrarnikTipNaVozilo, KomercijalnaOznaka,
                           Shasija, GodinaNaProizvodstvo, ZafatninaNaMotorotcm3, SilinaNaMotorotKW,
                           BrojNaSedista, BojaNaVoziloto, NosivostKG, DatumNaRegistracija,
                           BrojNaVpisot, DatumNaPrvataRegistracija, PrezimeNazivNaKorisnikot,
                           Ime, AdresaNaPostojanoZivealiste, EMBNaKorisnikot,
                           DatumNaPrvaRegistracijaVoRSM, DozvolataJaIzdal, OznakaNaOdobrenie,
                           BrojNAEUPotvrdaZaSoobraznost, PrezimeNazivNaSopstvenikot,
                           ImeSopstvenik, AdresaNaPostojanoZivealisteSediste,
                           EMBNaFizickoLiceEMBNaPravnoLice, KategorijaIVidNaVoziloto,
                           OblikINamenaNaKaroserijata, TipNaMotorot, VidNaGorivo,
                           BrojNaVrtezi, IdentifikacionenBrojNaMotorot, MaksimalnaBrzinaKM,
                           OdnosSilinaMasa, MasaNaVoziloto,
                           NajgolemaKonstruktivnaVkupnaMasaNaVozilotoKG,
                           NajgolemaLegalnaVkupnaMasaNaVozilotoPriRegistracijaKG,
                           NajgolemaLegalnaVkupnaMasaNaGrupaVozilaPriRegistracijaKG,
                           BrojNaOski,
                           RaspredelbaNaNajgolemataKonstruktivnaVkupnaMasaPoOskiKGINaPriklucnataTocka,
                           NajgolemoKonstruktivnoOsnoOptovaruvaweKgiNaPriklucnataTocka,
                           Dolzhina, Visina,
                           NajgolemaKonstruktivnaVkupnaMasaNaKocenaPrikolkaKG,
                           NajgolemaKonstruktivnaVkupnaMasaNaNEkocenaPrikolkaKG,
                           BrojNaMestaZaStoenje, DozvoleniPnevmaticiINaplatki,
                           BrojNaMestazaLezenje, CO2,
                           NajgolemoKonstruktivnoOptovaruvanjeNaPriklucokotZaPrikolka,
                           StacionarnaBucavost
                    FROM PolisiKlasa1Soobrakajna
                    WHERE PolisaId = @PolisaId", connection))
                {
                    cmd.Parameters.AddWithValue("@PolisaId", id);
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    if (await reader.ReadAsync())
                    {
                        InputSoobrakajna.Id = reader.GetInt64(0);
                        InputSoobrakajna.DateCreated = reader.IsDBNull(1) ? null : reader.GetDateTime(1);
                        InputSoobrakajna.UsernameCreated = reader.IsDBNull(2) ? null : reader.GetString(2);
                        InputSoobrakajna.DateModified = reader.IsDBNull(3) ? null : reader.GetDateTime(3);
                        InputSoobrakajna.UsernameModified = reader.IsDBNull(4) ? null : reader.GetString(4);
                        InputSoobrakajna.PolisaId = reader.GetInt64(5);
                        InputSoobrakajna.RegisterskaOznaka = reader.IsDBNull(6) ? null : reader.GetString(6);
                        InputSoobrakajna.Marka = reader.IsDBNull(7) ? null : reader.GetString(7);
                        InputSoobrakajna.SifrarnikTipNaVozilo = reader.IsDBNull(8) ? null : reader.GetInt64(8);
                        InputSoobrakajna.KomercijalnaOznaka = reader.IsDBNull(9) ? null : reader.GetString(9);
                        InputSoobrakajna.Shasija = reader.IsDBNull(10) ? null : reader.GetString(10);
                        InputSoobrakajna.GodinaNaProizvodstvo = reader.IsDBNull(11) ? null : reader.GetInt32(11);
                        InputSoobrakajna.ZafatninaNaMotorotcm3 = reader.IsDBNull(12) ? null : reader.GetInt32(12);
                        InputSoobrakajna.SilinaNaMotorotKW = reader.IsDBNull(13) ? null : reader.GetInt32(13);
                        InputSoobrakajna.BrojNaSedista = reader.IsDBNull(14) ? null : reader.GetInt32(14);
                        InputSoobrakajna.BojaNaVoziloto = reader.IsDBNull(15) ? null : reader.GetString(15);
                        InputSoobrakajna.NosivostKG = reader.IsDBNull(16) ? null : reader.GetInt32(16);
                        InputSoobrakajna.DatumNaRegistracija = reader.IsDBNull(17) ? null : reader.GetDateTime(17);
                        InputSoobrakajna.BrojNaVpisot = reader.IsDBNull(18) ? null : reader.GetString(18);
                        InputSoobrakajna.DatumNaPrvataRegistracija = reader.IsDBNull(19) ? null : reader.GetDateTime(19);
                        InputSoobrakajna.PrezimeNazivNaKorisnikot = reader.IsDBNull(20) ? null : reader.GetString(20);
                        InputSoobrakajna.Ime = reader.IsDBNull(21) ? null : reader.GetString(21);
                        InputSoobrakajna.AdresaNaPostojanoZivealiste = reader.IsDBNull(22) ? null : reader.GetString(22);
                        InputSoobrakajna.EMBNaKorisnikot = reader.IsDBNull(23) ? null : reader.GetString(23);
                        InputSoobrakajna.DatumNaPrvaRegistracijaVoRSM = reader.IsDBNull(24) ? null : reader.GetString(24);
                        InputSoobrakajna.DozvolataJaIzdal = reader.IsDBNull(25) ? null : reader.GetString(25);
                        InputSoobrakajna.OznakaNaOdobrenie = reader.IsDBNull(26) ? null : reader.GetString(26);
                        InputSoobrakajna.BrojNAEUPotvrdaZaSoobraznost = reader.IsDBNull(27) ? null : reader.GetString(27);
                        InputSoobrakajna.PrezimeNazivNaSopstvenikot = reader.IsDBNull(28) ? null : reader.GetString(28);
                        InputSoobrakajna.ImeSopstvenik = reader.IsDBNull(29) ? null : reader.GetString(29);
                        InputSoobrakajna.AdresaNaPostojanoZivealisteSediste = reader.IsDBNull(30) ? null : reader.GetString(30);
                        InputSoobrakajna.EMBNaFizickoLiceEMBNaPravnoLice = reader.IsDBNull(31) ? null : reader.GetString(31);
                        InputSoobrakajna.KategorijaIVidNaVoziloto = reader.IsDBNull(32) ? null : reader.GetString(32);
                        InputSoobrakajna.OblikINamenaNaKaroserijata = reader.IsDBNull(33) ? null : reader.GetString(33);
                        InputSoobrakajna.TipNaMotorot = reader.IsDBNull(34) ? null : reader.GetString(34);
                        InputSoobrakajna.VidNaGorivo = reader.IsDBNull(35) ? null : reader.GetString(35);
                        InputSoobrakajna.BrojNaVrtezi = reader.IsDBNull(36) ? null : reader.GetString(36);
                        InputSoobrakajna.IdentifikacionenBrojNaMotorot = reader.IsDBNull(37) ? null : reader.GetString(37);
                        InputSoobrakajna.MaksimalnaBrzinaKM = reader.IsDBNull(38) ? null : reader.GetString(38);
                        InputSoobrakajna.OdnosSilinaMasa = reader.IsDBNull(39) ? null : reader.GetString(39);
                        InputSoobrakajna.MasaNaVoziloto = reader.IsDBNull(40) ? null : reader.GetString(40);
                        InputSoobrakajna.NajgolemaKonstruktivnaVkupnaMasaNaVozilotoKG = reader.IsDBNull(41) ? null : reader.GetString(41);
                        InputSoobrakajna.NajgolemaLegalnaVkupnaMasaNaVozilotoPriRegistracijaKG = reader.IsDBNull(42) ? null : reader.GetString(42);
                        InputSoobrakajna.NajgolemaLegalnaVkupnaMasaNaGrupaVozilaPriRegistracijaKG = reader.IsDBNull(43) ? null : reader.GetString(43);
                        InputSoobrakajna.BrojNaOski = reader.IsDBNull(44) ? null : reader.GetInt32(44);
                        InputSoobrakajna.RaspredelbaNaNajgolemataKonstruktivnaVkupnaMasaPoOskiKGINaPriklucnataTocka = reader.IsDBNull(45) ? null : reader.GetString(45);
                        InputSoobrakajna.NajgolemoKonstruktivnoOsnoOptovaruwaweKgiNaPriklucnataTocka = reader.IsDBNull(46) ? null : reader.GetString(46);
                        InputSoobrakajna.Dolzhina = reader.IsDBNull(47) ? null : reader.GetInt32(47);
                        InputSoobrakajna.Visina = reader.IsDBNull(48) ? null : reader.GetInt32(48);
                        InputSoobrakajna.NajgolemaKonstruktivnaVkupnaMasaNaKocenaPrikolkaKG = reader.IsDBNull(49) ? null : reader.GetString(49);
                        InputSoobrakajna.NajgolemaKonstruktivnaVkupnaMasaNaNEkocenaPrikolkaKG = reader.IsDBNull(50) ? null : reader.GetString(50);
                        InputSoobrakajna.BrojNaMestaZaStoenje = reader.IsDBNull(51) ? null : reader.GetInt32(51);
                        InputSoobrakajna.DozvoleniPnevmaticiINaplatki = reader.IsDBNull(52) ? null : reader.GetString(52);
                        InputSoobrakajna.BrojNaMestazaLezenje = reader.IsDBNull(53) ? null : reader.GetInt32(53);
                        InputSoobrakajna.CO2 = reader.IsDBNull(54) ? null : reader.GetString(54);
                        InputSoobrakajna.NajgolemoKonstruktivnoOptovaruvanjeNaPriklucokotZaPrikolka = reader.IsDBNull(55) ? null : reader.GetString(55);
                        InputSoobrakajna.StacionarnaBucavost = reader.IsDBNull(56) ? null : reader.GetString(56);
                    }
                    else
                    {
                        // If no record exists, create a new one with the PolisaId
                        InputSoobrakajna.PolisaId = id;
                    }
                }
            }

            await LoadFiles();
            await LoadOsigureniciKolektivno();
            if (!string.IsNullOrEmpty(Input.BrojNaPolisa))
            {
                await LoadKarticaData(id);
                await LoadZadolzuvanjeData();
                await LoadOZInformation();
            }

            // Load Dogovoruvac and Osigurenik EMNMGB
            await LoadDogovoruvacOsigurenikEMNGMB();

            return Page();
        }

        public async Task<IActionResult> OnGetSearchKlientiAsync(string mb)
        {
            if (string.IsNullOrWhiteSpace(mb))
                return new JsonResult(new List<object>());

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT TOP 10 
                        Id, 
                        ISNULL(MB, '') as MB,
                        ISNULL(EDB, '') as EDB,
                        ISNULL(EMBG, '') as EMBG,
                        CASE 
                            WHEN KlientFizickoPravnoLice = 'P' THEN Naziv
                            ELSE CONCAT(ISNULL(Ime, ''), ' ', ISNULL(Prezime, ''))
                        END as Naziv,
                        KlientFizickoPravnoLice,
                        ISNULL(Ime, '') as Ime,
                        ISNULL(Prezime, '') as Prezime
                    FROM Klienti 
                    WHERE MB LIKE @Search + '%'
                       OR EDB LIKE @Search + '%'
                       OR EMBG LIKE @Search + '%'
                       OR Naziv LIKE '%' + @Search + '%'
                       OR Ime LIKE '%' + @Search + '%'
                       OR Prezime LIKE '%' + @Search + '%'
                    ORDER BY 
                        CASE 
                            WHEN MB LIKE @Search + '%' THEN 1
                            WHEN EDB LIKE @Search + '%' THEN 2
                            WHEN EMBG LIKE @Search + '%' THEN 3
                            ELSE 4
                        END,
                        MB, Naziv", connection))
                {
                    cmd.Parameters.AddWithValue("@Search", mb);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var results = new List<object>();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            id = reader["Id"],
                            mb = reader["MB"],
                            edb = reader["EDB"],
                            embg = reader["EMBG"],
                            naziv = reader["Naziv"],
                            ime = reader["Ime"],
                            prezime = reader["Prezime"],
                            tip = reader["KlientFizickoPravnoLice"].ToString()
                        });
                    }
                    return new JsonResult(results);
                }
            }
        }

        public async Task<IActionResult> OnGetSearchSorabotniciAsync(string mb)
        {
            if (string.IsNullOrWhiteSpace(mb))
                return new JsonResult(new List<object>());

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT TOP 10 
                        Id, 
                        ISNULL(MB, '') as MB,
                        ISNULL(EDB, '') as EDB,
                        ISNULL(EMBG, '') as EMBG,
                        CASE 
                            WHEN KlientFizickoPravnoLice = 'P' THEN Naziv
                            ELSE CONCAT(ISNULL(Ime, ''), ' ', ISNULL(Prezime, ''))
                        END as Naziv,
                        KlientFizickoPravnoLice,
                        ISNULL(Ime, '') as Ime,
                        ISNULL(Prezime, '') as Prezime
                    FROM Klienti 
                    WHERE (KlientSorabotnik = 1 or KlientVraboten = 1)
                        AND (MB LIKE @Search + '%'
                        OR EDB LIKE @Search + '%'
                        OR EMBG LIKE @Search + '%'
                        OR Naziv LIKE '%' + @Search + '%'
                        OR Ime LIKE '%' + @Search + '%'
                        OR Prezime LIKE '%' + @Search + '%')
                    ORDER BY 
                        CASE 
                            WHEN MB LIKE @Search + '%' THEN 1
                            WHEN EDB LIKE @Search + '%' THEN 2
                            WHEN EMBG LIKE @Search + '%' THEN 3
                            ELSE 4
                        END,
                        MB, Naziv", connection))
                {
                    cmd.Parameters.AddWithValue("@Search", mb);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var results = new List<object>();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            id = reader["Id"],
                            mb = reader["MB"],
                            edb = reader["EDB"],
                            embg = reader["EMBG"],
                            naziv = reader["Naziv"],
                            ime = reader["Ime"],
                            prezime = reader["Prezime"],
                            tip = reader["KlientFizickoPravnoLice"].ToString()
                        });
                    }
                    return new JsonResult(results);
                }
            }
        }

        public async Task<IActionResult> OnPostAsync()
        {
            try
            {
                var debugInfo = new System.Text.StringBuilder();
                var username = _httpContextAccessor.HttpContext.Session.GetString("Username");
                debugInfo.AppendLine($"User from session: {username}");

                if (string.IsNullOrEmpty(username))
                {
                    TempData["ErrorMessage"] = "Cannot save changes - no user is logged in. Please log in again.";
                    debugInfo.AppendLine("Username not found in session - aborting save");
                    TempData["DebugInfo"] = debugInfo.ToString();
                    return Page();
                }

                debugInfo.AppendLine($"Final username: {username}");
                debugInfo.AppendLine($"ModelState.IsValid: {ModelState.IsValid}");
                debugInfo.AppendLine($"Input.Id: {Input?.Id}");

                if (!await HasPageAccess("ViewEditPolisaKlasa1"))
                {
                    TempData["ErrorMessage"] = "Access denied to ViewEditPolisaKlasa1";
                    return RedirectToAccessDenied();
                }

                if (!ModelState.IsValid)
                {
                    debugInfo.AppendLine("ModelState errors:");
                    foreach (var modelStateEntry in ModelState.Values)
                    {
                        foreach (var error in modelStateEntry.Errors)
                        {
                            debugInfo.AppendLine($"- {error.ErrorMessage}");
                        }
                    }

                    TempData["DebugInfo"] = debugInfo.ToString();
                    await LoadOsiguriteli();
                    await LoadKlasiOsiguruvanje();
                    await LoadProdukti();
                    await LoadValuti();
                    await LoadNaciniNaPlakjanje();
                    await LoadTipoviNaPlakanje();
                    await LoadBanki();
                    await LoadListaDejnosti();
                    return Page();
                }

                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    debugInfo.AppendLine("Database connection opened successfully");

                    using (SqlCommand cmd = new SqlCommand(@"
                        UPDATE Polisi 
                        SET KlientiIdOsiguritel = @KlientiIdOsiguritel,
                            KlasiOsiguruvanjeIdKlasa = @KlasiOsiguruvanjeIdKlasa,
                            ProduktiIdProizvod = @ProduktiIdProizvod,
                            BrojNaPolisa = @BrojNaPolisa,
                            BrojNaPonuda = @BrojNaPonuda,
                            KlientiIdDogovoruvac = @KlientiIdDogovoruvac,
                            KlientiIdOsigurenik = @KlientiIdOsigurenik,
                            Kolektivna = @Kolektivna,
                            KolektivnaNeodredenBrOsigurenici = @KolektivnaNeodredenBrOsigurenici,
                            NeodredenBrOsigureniciZabeleska = @NeodredenBrOsigureniciZabeleska,
                            DatumVaziOd = @DatumVaziOd,
                            DatumVaziDo = @DatumVaziDo,
                            DatumNaIzdavanje = @DatumNaIzdavanje,
                            VremetraenjeNaPolisa = @VremetraenjeNaPolisa,
                            PeriodNaUplata = @PeriodNaUplata,
                            SifrarnikValutiIdValuta = @SifrarnikValutiIdValuta,
                            KlientiIdSorabotnik = @KlientiIdSorabotnik,
                            Faktoring = @Faktoring,
                            SifrarnikValutiIdFranshizaValuta = @SifrarnikValutiIdFranshizaValuta,
                            ProcentFranshiza = @ProcentFranshiza,
                            ProcentFinansiski = @ProcentFinansiski,
                            KoregiranaStapkaNaProvizija = @KoregiranaStapkaNaProvizija,
                            SifrarnikNacinNaPlakjanjeId = @SifrarnikNacinNaPlakjanjeId,
                            SifrarnikTipNaPlakanjeId = @SifrarnikTipNaPlakanjeId,
                            SifrarnikBankiIdBanka = @SifrarnikBankiIdBanka,
                            Zabeleska = @Zabeleska,
                            FranshizaIznos = @FranshizaIznos,
                            DateModified = GETDATE(),
                            UsernameModified = @UsernameModified,
                            TipNaFaktura = @TipNaFaktura,
                            BrojNaFakturaVlezna = @BrojNaFakturaVlezna,
                            DatumNaFakturaVlezna = @DatumNaFakturaVlezna,
                            RokNaPlakjanjeFakturaVlezna = @RokNaPlakjanjeFakturaVlezna,
                            GeneriranaFakturaIzlezna = @GeneriranaFakturaIzlezna,
                            BrojNaFakturaIzlezna = @BrojNaFakturaIzlezna,
                            DatumNaIzleznaFaktura = @DatumNaIzleznaFaktura,
                            RokNaPlakjanjeFakturaIzlezna = @RokNaPlakjanjeFakturaIzlezna,
                            Storno = @Storno,
                            PricinaZaStorno = @PricinaZaStorno                                                     

                        WHERE Id = @Id", connection))
                    {
                        debugInfo.AppendLine($"Updating policy with ID: {Input.Id}");

                        cmd.Parameters.AddWithValue("@Id", Input.Id);
                        cmd.Parameters.AddWithValue("@KlientiIdOsiguritel", Input.KlientiIdOsiguritel ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@KlasiOsiguruvanjeIdKlasa", Input.KlasiOsiguruvanjeIdKlasa ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@ProduktiIdProizvod", Input.ProduktiIdProizvod ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@BrojNaPolisa", Input.BrojNaPolisa ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@BrojNaPonuda", Input.BrojNaPonuda ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@KlientiIdDogovoruvac", Input.KlientiIdDogovoruvac ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@KlientiIdOsigurenik", Input.KlientiIdOsigurenik ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@Kolektivna", Input.Kolektivna);
                        cmd.Parameters.AddWithValue("@KolektivnaNeodredenBrOsigurenici", Input.KolektivnaNeodredenBrOsigurenici);
                        cmd.Parameters.AddWithValue("@NeodredenBrOsigureniciZabeleska", 
                            string.IsNullOrEmpty(Input.NeodredenBrOsigureniciZabeleska) ? (object)DBNull.Value : Input.NeodredenBrOsigureniciZabeleska);
                        cmd.Parameters.AddWithValue("@DatumVaziOd", Input.DatumVaziOd ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@DatumVaziDo", Input.DatumVaziDo ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@DatumNaIzdavanje", Input.DatumNaIzdavanje ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@VremetraenjeNaPolisa", Input.VremetraenjeNaPolisa ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@PeriodNaUplata", Input.PeriodNaUplata ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@SifrarnikValutiIdValuta", Input.SifrarnikValutiIdValuta);
                        cmd.Parameters.AddWithValue("@KlientiIdSorabotnik", Input.KlientiIdSorabotnik ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@Faktoring", Input.Faktoring);
                        cmd.Parameters.AddWithValue("@SifrarnikValutiIdFranshizaValuta", Input.SifrarnikValutiIdFranshizaValuta ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@ProcentFranshiza", Input.ProcentFranshiza ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@ProcentFinansiski", Input.ProcentFinansiski ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@KoregiranaStapkaNaProvizija", Input.KoregiranaStapkaNaProvizija ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@SifrarnikNacinNaPlakjanjeId", Input.SifrarnikNacinNaPlakjanjeId ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@SifrarnikTipNaPlakanjeId", Input.SifrarnikTipNaPlakanjeId ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@SifrarnikBankiIdBanka", Input.SifrarnikBankiIdBanka ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@Zabeleska", 
                            string.IsNullOrEmpty(Input.Zabeleska) ? (object)DBNull.Value : Input.Zabeleska);
                        cmd.Parameters.AddWithValue("@FranshizaIznos", Input.FranshizaIznos ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@UsernameModified", username);
                        cmd.Parameters.AddWithValue("@TipNaFaktura", Input.TipNaFaktura ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@BrojNaFakturaVlezna", Input.BrojNaFakturaVlezna ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@DatumNaFakturaVlezna", Input.DatumNaFakturaVlezna ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@RokNaPlakjanjeFakturaVlezna", Input.RokNaPlakjanjeFakturaVlezna ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@GeneriranaFakturaIzlezna", Input.GeneriranaFakturaIzlezna);
                        cmd.Parameters.AddWithValue("@BrojNaFakturaIzlezna", Input.BrojNaFakturaIzlezna ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@DatumNaIzleznaFaktura", Input.DatumNaIzleznaFaktura ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@RokNaPlakjanjeFakturaIzlezna", Input.RokNaPlakjanjeFakturaIzlezna ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@Storno", Input.Storno);
                        cmd.Parameters.AddWithValue("@PricinaZaStorno", Input.PricinaZaStorno ?? (object)DBNull.Value);

                        debugInfo.AppendLine("All parameters set");

                        var rowsAffected = await cmd.ExecuteNonQueryAsync();
                        debugInfo.AppendLine($"Rows affected: {rowsAffected}");

                        if (rowsAffected == 0)
                        {
                            TempData["ErrorMessage"] = "No records were updated. Please check if the policy exists.";
                            TempData["DebugInfo"] = debugInfo.ToString();
                            return Page();
                        }
                    }

                    // Update or Insert PolisiKlasa1 data
                    using (SqlCommand cmd = new SqlCommand(@"
                        IF EXISTS (SELECT 1 FROM PolisiKlasa1 WHERE PolisaId = @PolisaId)
                        BEGIN
                            UPDATE PolisiKlasa1 
                            SET ListaDejnostiId = @ListaDejnostiId,
                                BrojNaOsigurenici = @BrojNaOsigurenici,
                                PremijaZaEdnoLice = @PremijaZaEdnoLice,
                                VkupnaPremija = @VkupnaPremija,
                                SmrtOdNesrekjenSlucaj = @SmrtOdNesrekjenSlucaj,
                                TraenInvaliditet = @TraenInvaliditet,
                                TraenInvaliditet100Procenti = @TraenInvaliditet100Procenti,
                                DnevenNadomest = @DnevenNadomest,
                                Lekuvanje = @Lekuvanje,
                                TeskiBolesti = @TeskiBolesti,
                                TrosociZaPogrebZaSmrtOdNezgoda = @TrosociZaPogrebZaSmrtOdNezgoda,
                                TrosociZaOstavinskaPostapka = @TrosociZaOstavinskaPostapka,
                                TrosociZaOperacijaNezgoda = @TrosociZaOperacijaNezgoda,
                                TrosociZaObrazovanie = @TrosociZaObrazovanie,
                                ProcentNaPopustZaFakturaVoRok = @ProcentNaPopustZaFakturaVoRok,
                                IznosZaPlakjanjeVoRok = @IznosZaPlakjanjeVoRok,
                                ProcentKomercijalenPopust = @ProcentKomercijalenPopust,
                                ProcentFinansiski = @ProcentFinansiski,
                                PremijaZaNaplata = @PremijaZaNaplata,
                                DateModified = GETDATE(),
                                UsernameModified = @UsernameModified
                            WHERE PolisaId = @PolisaId
                        END
                        ELSE
                        BEGIN
                            INSERT INTO PolisiKlasa1 (
                                PolisaId, ListaDejnostiId, BrojNaOsigurenici,
                                PremijaZaEdnoLice, VkupnaPremija, SmrtOdNesrekjenSlucaj,
                                TraenInvaliditet, TraenInvaliditet100Procenti, DnevenNadomest,
                                Lekuvanje, TeskiBolesti, TrosociZaPogrebZaSmrtOdNezgoda,
                                TrosociZaOstavinskaPostapka, TrosociZaOperacijaNezgoda,
                                TrosociZaObrazovanie, ProcentNaPopustZaFakturaVoRok,
                                IznosZaPlakjanjeVoRok, ProcentKomercijalenPopust,
                                ProcentFinansiski, PremijaZaNaplata,
                                DateCreated, UsernameCreated
                            ) VALUES (
                                @PolisaId, @ListaDejnostiId, @BrojNaOsigurenici,
                                @PremijaZaEdnoLice, @VkupnaPremija, @SmrtOdNesrekjenSlucaj,
                                @TraenInvaliditet, @TraenInvaliditet100Procenti, @DnevenNadomest,
                                @Lekuvanje, @TeskiBolesti, @TrosociZaPogrebZaSmrtOdNezgoda,
                                @TrosociZaOstavinskaPostapka, @TrosociZaOperacijaNezgoda,
                                @TrosociZaObrazovanie, @ProcentNaPopustZaFakturaVoRok,
                                @IznosZaPlakjanjeVoRok, @ProcentKomercijalenPopust,
                                @ProcentFinansiski, @PremijaZaNaplata,
                                GETDATE(), @UsernameCreated
                            )
                        END", connection))
                    {
                        cmd.Parameters.AddWithValue("@PolisaId", Input.Id);
                        cmd.Parameters.AddWithValue("@ListaDejnostiId", InputKlasa1.ListaDejnostiId ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@BrojNaOsigurenici", InputKlasa1.BrojNaOsigurenici ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@PremijaZaEdnoLice", InputKlasa1.PremijaZaEdnoLice ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@VkupnaPremija", InputKlasa1.VkupnaPremija ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@SmrtOdNesrekjenSlucaj", InputKlasa1.SmrtOdNesrekjenSlucaj ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@TraenInvaliditet", InputKlasa1.TraenInvaliditet ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@TraenInvaliditet100Procenti", InputKlasa1.TraenInvaliditet100Procenti ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@DnevenNadomest", InputKlasa1.DnevenNadomest ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@Lekuvanje", InputKlasa1.Lekuvanje ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@TeskiBolesti", InputKlasa1.TeskiBolesti ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@TrosociZaPogrebZaSmrtOdNezgoda", InputKlasa1.TrosociZaPogrebZaSmrtOdNezgoda ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@TrosociZaOstavinskaPostapka", InputKlasa1.TrosociZaOstavinskaPostapka ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@TrosociZaOperacijaNezgoda", InputKlasa1.TrosociZaOperacijaNezgoda ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@TrosociZaObrazovanie", InputKlasa1.TrosociZaObrazovanie ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@ProcentNaPopustZaFakturaVoRok", InputKlasa1.ProcentNaPopustZaFakturaVoRok ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@IznosZaPlakjanjeVoRok", InputKlasa1.IznosZaPlakjanjeVoRok ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@ProcentKomercijalenPopust", InputKlasa1.ProcentKomercijalenPopust ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@ProcentFinansiski", InputKlasa1.ProcentFinansiski ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@PremijaZaNaplata", InputKlasa1.PremijaZaNaplata ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@UsernameModified", username);
                        cmd.Parameters.AddWithValue("@UsernameCreated", username);

                        await cmd.ExecuteNonQueryAsync();
                    }

                    // Update or Insert PolisiKlasa1Soobrakajna data
                    using (SqlCommand cmd = new SqlCommand(@"
                        IF EXISTS (SELECT 1 FROM PolisiKlasa1Soobrakajna WHERE PolisaId = @PolisaId)
                        BEGIN
                            UPDATE PolisiKlasa1Soobrakajna 
                            SET RegisterskaOznaka = @RegisterskaOznaka,
                                Marka = @Marka,
                                SifrarnikTipNaVozilo = @SifrarnikTipNaVozilo,
                                KomercijalnaOznaka = @KomercijalnaOznaka,
                                Shasija = @Shasija,
                                GodinaNaProizvodstvo = @GodinaNaProizvodstvo,
                                ZafatninaNaMotorotcm3 = @ZafatninaNaMotorotcm3,
                                SilinaNaMotorotKW = @SilinaNaMotorotKW,
                                BrojNaSedista = @BrojNaSedista,
                                BojaNaVoziloto = @BojaNaVoziloto,
                                NosivostKG = @NosivostKG,
                                DatumNaRegistracija = @DatumNaRegistracija,
                                BrojNaVpisot = @BrojNaVpisot,
                                DatumNaPrvataRegistracija = @DatumNaPrvataRegistracija,
                                PrezimeNazivNaKorisnikot = @PrezimeNazivNaKorisnikot,
                                Ime = @Ime,
                                AdresaNaPostojanoZivealiste = @AdresaNaPostojanoZivealiste,
                                EMBNaKorisnikot = @EMBNaKorisnikot,
                                DatumNaPrvaRegistracijaVoRSM = @DatumNaPrvaRegistracijaVoRSM,
                                DozvolataJaIzdal = @DozvolataJaIzdal,
                                OznakaNaOdobrenie = @OznakaNaOdobrenie,
                                BrojNAEUPotvrdaZaSoobraznost = @BrojNAEUPotvrdaZaSoobraznost,
                                PrezimeNazivNaSopstvenikot = @PrezimeNazivNaSopstvenikot,
                                ImeSopstvenik = @ImeSopstvenik,
                                AdresaNaPostojanoZivealisteSediste = @AdresaNaPostojanoZivealisteSediste,
                                EMBNaFizickoLiceEMBNaPravnoLice = @EMBNaFizickoLiceEMBNaPravnoLice,
                                KategorijaIVidNaVoziloto = @KategorijaIVidNaVoziloto,
                                OblikINamenaNaKaroserijata = @OblikINamenaNaKaroserijata,
                                TipNaMotorot = @TipNaMotorot,
                                VidNaGorivo = @VidNaGorivo,
                                BrojNaVrtezi = @BrojNaVrtezi,
                                IdentifikacionenBrojNaMotorot = @IdentifikacionenBrojNaMotorot,
                                MaksimalnaBrzinaKM = @MaksimalnaBrzinaKM,
                                OdnosSilinaMasa = @OdnosSilinaMasa,
                                MasaNaVoziloto = @MasaNaVoziloto,
                                NajgolemaKonstruktivnaVkupnaMasaNaVozilotoKG = @NajgolemaKonstruktivnaVkupnaMasaNaVozilotoKG,
                                NajgolemaLegalnaVkupnaMasaNaVozilotoPriRegistracijaKG = @NajgolemaLegalnaVkupnaMasaNaVozilotoPriRegistracijaKG,
                                NajgolemaLegalnaVkupnaMasaNaGrupaVozilaPriRegistracijaKG = @NajgolemaLegalnaVkupnaMasaNaGrupaVozilaPriRegistracijaKG,
                                BrojNaOski = @BrojNaOski,
                                RaspredelbaNaNajgolemataKonstruktivnaVkupnaMasaPoOskiKGINaPriklucnataTocka = @RaspredelbaNaNajgolemataKonstruktivnaVkupnaMasaPoOskiKGINaPriklucnataTocka,
                                NajgolemoKonstruktivnoOsnoOptovaruvaweKgiNaPriklucnataTocka = @NajgolemoKonstruktivnoOsnoOptovaruwaweKgiNaPriklucnataTocka,
                                Dolzhina = @Dolzhina,
                                Visina = @Visina,
                                NajgolemaKonstruktivnaVkupnaMasaNaKocenaPrikolkaKG = @NajgolemaKonstruktivnaVkupnaMasaNaKocenaPrikolkaKG,
                                NajgolemaKonstruktivnaVkupnaMasaNaNEkocenaPrikolkaKG = @NajgolemaKonstruktivnaVkupnaMasaNaNEkocenaPrikolkaKG,
                                BrojNaMestaZaStoenje = @BrojNaMestaZaStoenje,
                                DozvoleniPnevmaticiINaplatki = @DozvoleniPnevmaticiINaplatki,
                                BrojNaMestazaLezenje = @BrojNaMestazaLezenje,
                                CO2 = @CO2,
                                NajgolemoKonstruktivnoOptovaruvanjeNaPriklucokotZaPrikolka = @NajgolemoKonstruktivnoOptovaruvanjeNaPriklucokotZaPrikolka,
                                StacionarnaBucavost = @StacionarnaBucavost,
                                DateModified = GETDATE(),
                                UsernameModified = @UsernameModified
                            WHERE PolisaId = @PolisaId
                        END
                        ELSE
                        BEGIN
                            INSERT INTO PolisiKlasa1Soobrakajna (
                                PolisaId, RegisterskaOznaka, Marka, SifrarnikTipNaVozilo, KomercijalnaOznaka,
                                Shasija, GodinaNaProizvodstvo, ZafatninaNaMotorotcm3, SilinaNaMotorotKW,
                                BrojNaSedista, BojaNaVoziloto, NosivostKG, DatumNaRegistracija,
                                BrojNaVpisot, DatumNaPrvataRegistracija, PrezimeNazivNaKorisnikot,
                                Ime, AdresaNaPostojanoZivealiste, EMBNaKorisnikot,
                                DatumNaPrvaRegistracijaVoRSM, DozvolataJaIzdal, OznakaNaOdobrenie,
                                BrojNAEUPotvrdaZaSoobraznost, PrezimeNazivNaSopstvenikot,
                                ImeSopstvenik, AdresaNaPostojanoZivealisteSediste,
                                EMBNaFizickoLiceEMBNaPravnoLice, KategorijaIVidNaVoziloto,
                                OblikINamenaNaKaroserijata, TipNaMotorot, VidNaGorivo,
                                BrojNaVrtezi, IdentifikacionenBrojNaMotorot, MaksimalnaBrzinaKM,
                                OdnosSilinaMasa, MasaNaVoziloto,
                                NajgolemaKonstruktivnaVkupnaMasaNaVozilotoKG,
                                NajgolemaLegalnaVkupnaMasaNaVozilotoPriRegistracijaKG,
                                NajgolemaLegalnaVkupnaMasaNaGrupaVozilaPriRegistracijaKG,
                                BrojNaOski,
                                RaspredelbaNaNajgolemataKonstruktivnaVkupnaMasaPoOskiKGINaPriklucnataTocka,
                                NajgolemoKonstruktivnoOsnoOptovaruvaweKgiNaPriklucnataTocka,
                                Dolzhina, Visina,
                                NajgolemaKonstruktivnaVkupnaMasaNaKocenaPrikolkaKG,
                                NajgolemaKonstruktivnaVkupnaMasaNaNEkocenaPrikolkaKG,
                                BrojNaMestaZaStoenje, DozvoleniPnevmaticiINaplatki,
                                BrojNaMestazaLezenje, CO2,
                                NajgolemoKonstruktivnoOptovaruvanjeNaPriklucokotZaPrikolka,
                                StacionarnaBucavost,
                                DateCreated, UsernameCreated
                            ) VALUES (
                                @PolisaId, @RegisterskaOznaka, @Marka, @SifrarnikTipNaVozilo, @KomercijalnaOznaka,
                                @Shasija, @GodinaNaProizvodstvo, @ZafatninaNaMotorotcm3, @SilinaNaMotorotKW,
                                @BrojNaSedista, @BojaNaVoziloto, @NosivostKG, @DatumNaRegistracija,
                                @BrojNaVpisot, @DatumNaPrvataRegistracija, @PrezimeNazivNaKorisnikot,
                                @Ime, @AdresaNaPostojanoZivealiste, @EMBNaKorisnikot,
                                @DatumNaPrvaRegistracijaVoRSM, @DozvolataJaIzdal, @OznakaNaOdobrenie,
                                @BrojNAEUPotvrdaZaSoobraznost, @PrezimeNazivNaSopstvenikot,
                                @ImeSopstvenik, @AdresaNaPostojanoZivealisteSediste,
                                @EMBNaFizickoLiceEMBNaPravnoLice, @KategorijaIVidNaVoziloto,
                                @OblikINamenaNaKaroserijata, @TipNaMotorot, @VidNaGorivo,
                                @BrojNaVrtezi, @IdentifikacionenBrojNaMotorot, @MaksimalnaBrzinaKM,
                                @OdnosSilinaMasa, @MasaNaVoziloto,
                                @NajgolemaKonstruktivnaVkupnaMasaNaVozilotoKG,
                                @NajgolemaLegalnaVkupnaMasaNaVozilotoPriRegistracijaKG,
                                @NajgolemaLegalnaVkupnaMasaNaGrupaVozilaPriRegistracijaKG,
                                @BrojNaOski,
                                @RaspredelbaNaNajgolemataKonstruktivnaVkupnaMasaPoOskiKGINaPriklucnataTocka,
                                @NajgolemoKonstruktivnoOsnoOptovaruwaweKgiNaPriklucnataTocka,
                                @Dolzhina, @Visina,
                                @NajgolemaKonstruktivnaVkupnaMasaNaKocenaPrikolkaKG,
                                @NajgolemaKonstruktivnaVkupnaMasaNaNEkocenaPrikolkaKG,
                                @BrojNaMestaZaStoenje, @DozvoleniPnevmaticiINaplatki,
                                @BrojNaMestazaLezenje, @CO2,
                                @NajgolemoKonstruktivnoOptovaruvanjeNaPriklucokotZaPrikolka,
                                @StacionarnaBucavost,
                                GETDATE(), @UsernameCreated
                            )
                        END", connection))
                    {
                        cmd.Parameters.AddWithValue("@PolisaId", Input.Id);
                        cmd.Parameters.AddWithValue("@RegisterskaOznaka", (object?)InputSoobrakajna.RegisterskaOznaka ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Marka", (object?)InputSoobrakajna.Marka ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@SifrarnikTipNaVozilo", (object?)InputSoobrakajna.SifrarnikTipNaVozilo ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@KomercijalnaOznaka", (object?)InputSoobrakajna.KomercijalnaOznaka ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Shasija", (object?)InputSoobrakajna.Shasija ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@GodinaNaProizvodstvo", (object?)InputSoobrakajna.GodinaNaProizvodstvo ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@ZafatninaNaMotorotcm3", (object?)InputSoobrakajna.ZafatninaNaMotorotcm3 ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@SilinaNaMotorotKW", (object?)InputSoobrakajna.SilinaNaMotorotKW ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@BrojNaSedista", (object?)InputSoobrakajna.BrojNaSedista ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@BojaNaVoziloto", (object?)InputSoobrakajna.BojaNaVoziloto ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@NosivostKG", (object?)InputSoobrakajna.NosivostKG ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@DatumNaRegistracija", (object?)InputSoobrakajna.DatumNaRegistracija ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@BrojNaVpisot", (object?)InputSoobrakajna.BrojNaVpisot ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@DatumNaPrvataRegistracija", (object?)InputSoobrakajna.DatumNaPrvataRegistracija ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@PrezimeNazivNaKorisnikot", (object?)InputSoobrakajna.PrezimeNazivNaKorisnikot ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Ime", (object?)InputSoobrakajna.Ime ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@AdresaNaPostojanoZivealiste", (object?)InputSoobrakajna.AdresaNaPostojanoZivealiste ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@EMBNaKorisnikot", (object?)InputSoobrakajna.EMBNaKorisnikot ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@DatumNaPrvaRegistracijaVoRSM", (object?)InputSoobrakajna.DatumNaPrvaRegistracijaVoRSM ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@DozvolataJaIzdal", (object?)InputSoobrakajna.DozvolataJaIzdal ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@OznakaNaOdobrenie", (object?)InputSoobrakajna.OznakaNaOdobrenie ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@BrojNAEUPotvrdaZaSoobraznost", (object?)InputSoobrakajna.BrojNAEUPotvrdaZaSoobraznost ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@PrezimeNazivNaSopstvenikot", (object?)InputSoobrakajna.PrezimeNazivNaSopstvenikot ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@ImeSopstvenik", (object?)InputSoobrakajna.ImeSopstvenik ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@AdresaNaPostojanoZivealisteSediste", (object?)InputSoobrakajna.AdresaNaPostojanoZivealisteSediste ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@EMBNaFizickoLiceEMBNaPravnoLice", (object?)InputSoobrakajna.EMBNaFizickoLiceEMBNaPravnoLice ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@KategorijaIVidNaVoziloto", (object?)InputSoobrakajna.KategorijaIVidNaVoziloto ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@OblikINamenaNaKaroserijata", (object?)InputSoobrakajna.OblikINamenaNaKaroserijata ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@TipNaMotorot", (object?)InputSoobrakajna.TipNaMotorot ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@VidNaGorivo", (object?)InputSoobrakajna.VidNaGorivo ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@BrojNaVrtezi", (object?)InputSoobrakajna.BrojNaVrtezi ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@IdentifikacionenBrojNaMotorot", (object?)InputSoobrakajna.IdentifikacionenBrojNaMotorot ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@MaksimalnaBrzinaKM", (object?)InputSoobrakajna.MaksimalnaBrzinaKM ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@OdnosSilinaMasa", (object?)InputSoobrakajna.OdnosSilinaMasa ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@MasaNaVoziloto", (object?)InputSoobrakajna.MasaNaVoziloto ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@NajgolemaKonstruktivnaVkupnaMasaNaVozilotoKG", (object?)InputSoobrakajna.NajgolemaKonstruktivnaVkupnaMasaNaVozilotoKG ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@NajgolemaLegalnaVkupnaMasaNaVozilotoPriRegistracijaKG", (object?)InputSoobrakajna.NajgolemaLegalnaVkupnaMasaNaVozilotoPriRegistracijaKG ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@NajgolemaLegalnaVkupnaMasaNaGrupaVozilaPriRegistracijaKG", (object?)InputSoobrakajna.NajgolemaLegalnaVkupnaMasaNaGrupaVozilaPriRegistracijaKG ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@BrojNaOski", (object?)InputSoobrakajna.BrojNaOski ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@RaspredelbaNaNajgolemataKonstruktivnaVkupnaMasaPoOskiKGINaPriklucnataTocka", (object?)InputSoobrakajna.RaspredelbaNaNajgolemataKonstruktivnaVkupnaMasaPoOskiKGINaPriklucnataTocka ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@NajgolemoKonstruktivnoOsnoOptovaruwaweKgiNaPriklucnataTocka", (object?)InputSoobrakajna.NajgolemoKonstruktivnoOsnoOptovaruwaweKgiNaPriklucnataTocka ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Dolzhina", (object?)InputSoobrakajna.Dolzhina ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Visina", (object?)InputSoobrakajna.Visina ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@NajgolemaKonstruktivnaVkupnaMasaNaKocenaPrikolkaKG", (object?)InputSoobrakajna.NajgolemaKonstruktivnaVkupnaMasaNaKocenaPrikolkaKG ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@NajgolemaKonstruktivnaVkupnaMasaNaNEkocenaPrikolkaKG", (object?)InputSoobrakajna.NajgolemaKonstruktivnaVkupnaMasaNaNEkocenaPrikolkaKG ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@BrojNaMestaZaStoenje", (object?)InputSoobrakajna.BrojNaMestaZaStoenje ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@DozvoleniPnevmaticiINaplatki", (object?)InputSoobrakajna.DozvoleniPnevmaticiINaplatki ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@BrojNaMestazaLezenje", (object?)InputSoobrakajna.BrojNaMestazaLezenje ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@CO2", (object?)InputSoobrakajna.CO2 ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@NajgolemoKonstruktivnoOptovaruvanjeNaPriklucokotZaPrikolka", (object?)InputSoobrakajna.NajgolemoKonstruktivnoOptovaruvanjeNaPriklucokotZaPrikolka ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@StacionarnaBucavost", (object?)InputSoobrakajna.StacionarnaBucavost ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@UsernameModified", username);
                        cmd.Parameters.AddWithValue("@UsernameCreated", username);

                        await cmd.ExecuteNonQueryAsync();
                    }
                }

                TempData["SuccessMessage"] = "Полисата е успешно зачувана.";
                // Reload the page data
                await LoadOsiguriteli();
                await LoadKlasiOsiguruvanje();
                await LoadProdukti();
                await LoadValuti();
                await LoadNaciniNaPlakjanje();
                await LoadTipoviNaPlakanje();
                await LoadBanki();
                await LoadListaDejnosti();
                return RedirectToPage(new { id = Input.Id });
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "An error occurred while saving the policy.";
                TempData["DebugInfo"] = $"Exception: {ex.Message}\nStack Trace: {ex.StackTrace}";
                return Page();
            }
        }

        public async Task<IActionResult> OnGetDownloadFileAsync(long fileId)
        {
            if (!await HasPageAccess("ViewEditPolisaKlasa1"))
            {
                return RedirectToAccessDenied();
            }

            string filePath = null;
            string fileName = null;
            string connectionString = _configuration.GetConnectionString("DefaultConnection");

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand command = new SqlCommand(
                    "SELECT FilePath, FileName FROM PolisiFileSystem WHERE Id = @FileId", connection))
                {
                    command.Parameters.AddWithValue("@FileId", fileId);
                    using (SqlDataReader reader = await command.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                            filePath = reader.GetString(0);
                            fileName = reader.GetString(1);
                        }
                    }
                }
            }

            if (string.IsNullOrEmpty(filePath))
            {
                return NotFound();
            }

            var sftpConfig = _configuration.GetSection("SftpConfig");
            using (var client = new SftpClient(
                sftpConfig["Host"],
                int.Parse(sftpConfig["Port"]),
                sftpConfig["Username"],
                sftpConfig["Password"]))
            {
                client.Connect();

                using (var memoryStream = new MemoryStream())
                {
                    client.DownloadFile(filePath, memoryStream);
                    client.Disconnect();
                    memoryStream.Position = 0;
                    return File(memoryStream.ToArray(), "application/octet-stream", fileName);
                }
            }
        }
    }

    public enum TipNaFakturaenumKlasa1
    {
        [Display(Name = "Влезна фактура кон клиент")]
        [EnumMember(Value = "Влезна фактура кон клиент")]
        VleznaFakturaKonKlient,
        
        [Display(Name = "Влезна фактура кон брокер")]
        [EnumMember(Value = "Влезна фактура кон брокер")]
        VleznaFakturaKonBroker
    }
} 