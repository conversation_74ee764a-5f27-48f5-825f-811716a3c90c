@page
@model NextBroker.Pages.AdministrationPages.OnlinePortalSettingModel
@{
    ViewData["Title"] = "Поставки за онлајн портал";
}

<h1 style='text-align: center;'>@ViewData["Title"]</h1>

<div class="container-fluid mt-4 px-4">
    @Html.AntiForgeryToken()

    @if (!string.IsNullOrEmpty(TempData["SuccessMessage"]?.ToString()))
    {
        <div class="alert alert-success" id="successMessage">
            @TempData["SuccessMessage"]
        </div>
    }

    @if (!string.IsNullOrEmpty(TempData["ErrorMessage"]?.ToString()))
    {
        <div class="alert alert-danger" id="errorMessage">
            @TempData["ErrorMessage"]
        </div>
    }

    <!-- Portal Settings Card -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">Подесувања за портал</h5>
        </div>
        <div class="card-body">
            <div class="mb-3">
                <div class="d-flex justify-content-between align-items-center">
                    <label class="form-label mb-0">Вклучување/Исклучување на Online Portal за клиенти</label>
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="onlinePortalEnabledSwitch"
                               @(Model.OnlinePortalEnabled ? "checked" : "")
                               data-setting="OnlinePortalEnabled">
                    </div>
                </div>
            </div>
            <div class="mb-3">
                <div class="d-flex justify-content-between align-items-center">
                    <label class="form-label mb-0">Вклучување/Исклучување систем за online плаќање</label>
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="onlinePortalPaymentsSwitch"
                               @(Model.OnlinePortalPayments ? "checked" : "")
                               data-setting="OnlinePortalPayments">
                    </div>
                </div>
            </div>

            <!-- Email Setting Section -->
            <hr class="my-4">
            <div class="mb-3">
                <label class="form-label">Е-пошта за обнова на полиса</label>
                <div class="input-group">
                    <input type="email" class="form-control" id="emailZaObnovaNaPolisa"
                           value="@Model.EmailZaObnovaNaPolisa" readonly>
                    <button class="btn btn-outline-primary" type="button" id="editEmailBtn">
                        <i class="fas fa-edit"></i> Уреди
                    </button>
                    <button class="btn btn-success d-none" type="button" id="saveEmailBtn">
                        <i class="fas fa-save"></i> Зачувај
                    </button>
                    <button class="btn btn-secondary d-none" type="button" id="cancelEmailBtn">
                        <i class="fas fa-times"></i> Откажи
                    </button>
                </div>
                <div class="form-text">Оваа е-пошта се користи за праќање на известувања за обнова на полиси.</div>
            </div>
        </div>
    </div>

    <!-- User Management Card -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">Управување со корисници на онлајн портал и NextBroker Core</h5>
        </div>
        <div class="card-body">
            <!-- Search input -->
            <div class="row mb-3">
                <div class="col-md-6">
                    <input type="text" id="searchInput" class="form-control" placeholder="Пребарувај корисници...">
                </div>
            </div>

            <!-- Users table -->
            <div class="table-responsive">
                <table id="usersTable" class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th data-sort="id">ID <i class="fas fa-sort"></i></th>
                            <th data-sort="firstname">Име <i class="fas fa-sort"></i></th>
                            <th data-sort="lastname">Презиме <i class="fas fa-sort"></i></th>
                            <th data-sort="username">Корисничко име <i class="fas fa-sort"></i></th>
                            <th data-sort="email">Е-пошта <i class="fas fa-sort"></i></th>
                            <th data-sort="locked">Заклучен <i class="fas fa-sort"></i></th>
                            <th data-sort="emb">ЕМБ <i class="fas fa-sort"></i></th>
                            <th>Акции</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var user in Model.Users)
                        {
                            <tr data-user-id="@user.Id">
                                <td>@user.Id</td>
                                <td>@user.FirstName</td>
                                <td>@user.LastName</td>
                                <td>@user.Username</td>
                                <td class="editable-email">
                                    <span class="display-value">@user.Email</span>
                                    <input type="email" class="form-control edit-input d-none" value="@user.Email">
                                </td>
                                <td class="editable-locked">
                                    <span class="display-value">
                                        @if (user.Locked)
                                        {
                                            <i class="fas fa-check text-success"></i>
                                        }
                                        else
                                        {
                                            <i class="fas fa-times text-danger"></i>
                                        }
                                    </span>
                                    <select class="form-control edit-input d-none">
                                        <option value="false" selected="@(!user.Locked)">Не</option>
                                        <option value="true" selected="@(user.Locked)">Да</option>
                                    </select>
                                </td>
                                <td>@user.EMB</td>
                                <td>
                                    <button class="btn btn-sm btn-primary edit-btn" data-user-id="@user.Id">
                                        <i class="fas fa-edit"></i> Уреди
                                    </button>
                                    <button class="btn btn-sm btn-success save-btn d-none" data-user-id="@user.Id">
                                        <i class="fas fa-save"></i> Зачувај
                                    </button>
                                    <button class="btn btn-sm btn-secondary cancel-btn d-none" data-user-id="@user.Id">
                                        <i class="fas fa-times"></i> Откажи
                                    </button>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            let sortDirection = {};

            // Toggle switches functionality
            $('.form-check-input[data-setting]').on('change', function() {
                const settingName = $(this).data('setting');
                const isChecked = $(this).is(':checked');
                const switchElement = $(this);

                // Disable the switch during update
                switchElement.prop('disabled', true);

                $.ajax({
                    url: '?handler=ToggleSetting',
                    type: 'POST',
                    data: {
                        settingName: settingName,
                        value: isChecked,
                        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(response) {
                        if (response.success) {
                            showMessage(response.message, 'success');
                        } else {
                            // Revert the switch if update failed
                            switchElement.prop('checked', !isChecked);
                            showMessage(response.message, 'danger');
                        }
                    },
                    error: function() {
                        // Revert the switch if request failed
                        switchElement.prop('checked', !isChecked);
                        showMessage('Грешка при ажурирање на поставката', 'danger');
                    },
                    complete: function() {
                        // Re-enable the switch
                        switchElement.prop('disabled', false);
                    }
                });
            });

            // Email editing functionality
            let originalEmailValue = '';

            $('#editEmailBtn').on('click', function() {
                const emailInput = $('#emailZaObnovaNaPolisa');
                originalEmailValue = emailInput.val();

                emailInput.prop('readonly', false).focus();
                $(this).addClass('d-none');
                $('#saveEmailBtn, #cancelEmailBtn').removeClass('d-none');
            });

            $('#cancelEmailBtn').on('click', function() {
                const emailInput = $('#emailZaObnovaNaPolisa');
                emailInput.val(originalEmailValue).prop('readonly', true);

                $('#editEmailBtn').removeClass('d-none');
                $(this).addClass('d-none');
                $('#saveEmailBtn').addClass('d-none');
            });

            $('#saveEmailBtn').on('click', function() {
                const emailInput = $('#emailZaObnovaNaPolisa');
                const emailValue = emailInput.val();
                const saveBtn = $(this);

                // Show loading state
                saveBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Зачувувам...');

                $.ajax({
                    url: '?handler=UpdateEmail',
                    type: 'POST',
                    data: {
                        emailValue: emailValue,
                        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(response) {
                        if (response.success) {
                            emailInput.prop('readonly', true);
                            $('#editEmailBtn').removeClass('d-none');
                            $('#saveEmailBtn, #cancelEmailBtn').addClass('d-none');
                            showMessage(response.message, 'success');
                        } else {
                            showMessage(response.message, 'danger');
                        }
                    },
                    error: function() {
                        showMessage('Грешка при ажурирање на е-поштата', 'danger');
                    },
                    complete: function() {
                        // Reset button state
                        saveBtn.prop('disabled', false).html('<i class="fas fa-save"></i> Зачувај');
                    }
                });
            });

            // Search functionality
            $('#searchInput').on('keyup', function() {
                const searchTerm = $(this).val().toLowerCase();
                $('#usersTable tbody tr').each(function() {
                    const rowText = $(this).text().toLowerCase();
                    $(this).toggle(rowText.includes(searchTerm));
                });
            });

            // Sorting functionality
            $('th[data-sort]').on('click', function() {
                const column = $(this).data('sort');
                const columnIndex = $(this).index();
                const isAsc = sortDirection[column] !== 'asc';
                sortDirection[column] = isAsc ? 'asc' : 'desc';

                // Update sort icons
                $('th[data-sort] i').removeClass('fa-sort-up fa-sort-down').addClass('fa-sort');
                $(this).find('i').removeClass('fa-sort').addClass(isAsc ? 'fa-sort-up' : 'fa-sort-down');

                // Sort rows
                const rows = $('#usersTable tbody tr').get();
                rows.sort(function(a, b) {
                    const aVal = $(a).children().eq(columnIndex).text().trim();
                    const bVal = $(b).children().eq(columnIndex).text().trim();

                    // Handle numeric sorting for ID
                    if (column === 'id') {
                        return isAsc ? parseInt(aVal) - parseInt(bVal) : parseInt(bVal) - parseInt(aVal);
                    }

                    // Handle boolean sorting for locked
                    if (column === 'locked') {
                        const aLocked = $(a).find('.editable-locked .display-value i').hasClass('fa-check');
                        const bLocked = $(b).find('.editable-locked .display-value i').hasClass('fa-check');
                        return isAsc ? (aLocked - bLocked) : (bLocked - aLocked);
                    }

                    // String sorting
                    return isAsc ? aVal.localeCompare(bVal) : bVal.localeCompare(aVal);
                });

                $('#usersTable tbody').empty().append(rows);
            });

            // Edit functionality
            $('.edit-btn').on('click', function() {
                const userId = $(this).data('user-id');
                const row = $(`tr[data-user-id="${userId}"]`);

                // Show edit inputs and hide display values
                row.find('.display-value').addClass('d-none');
                row.find('.edit-input').removeClass('d-none');

                // Show save/cancel buttons, hide edit button
                $(this).addClass('d-none');
                row.find('.save-btn, .cancel-btn').removeClass('d-none');
            });

            // Cancel functionality
            $('.cancel-btn').on('click', function() {
                const userId = $(this).data('user-id');
                const row = $(`tr[data-user-id="${userId}"]`);

                // Hide edit inputs and show display values
                row.find('.edit-input').addClass('d-none');
                row.find('.display-value').removeClass('d-none');

                // Show edit button, hide save/cancel buttons
                row.find('.edit-btn').removeClass('d-none');
                $(this).addClass('d-none');
                row.find('.save-btn').addClass('d-none');

                // Reset input values to original
                const originalEmail = row.find('.display-value').first().text();
                row.find('.editable-email .edit-input').val(originalEmail);
            });

            // Save functionality
            $('.save-btn').on('click', function() {
                const userId = $(this).data('user-id');
                const row = $(`tr[data-user-id="${userId}"]`);

                const email = row.find('.editable-email .edit-input').val();
                const locked = row.find('.editable-locked .edit-input').val() === 'true';

                // Show loading state
                $(this).prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Зачувувам...');

                // Make AJAX call to update user
                $.ajax({
                    url: '?handler=UpdateUser',
                    type: 'POST',
                    data: {
                        id: userId,
                        email: email,
                        locked: locked,
                        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(response) {
                        if (response.success) {
                            // Update display values
                            row.find('.editable-email .display-value').text(email);

                            const lockedIcon = locked ?
                                '<i class="fas fa-check text-success"></i>' :
                                '<i class="fas fa-times text-danger"></i>';
                            row.find('.editable-locked .display-value').html(lockedIcon);

                            // Hide edit inputs and show display values
                            row.find('.edit-input').addClass('d-none');
                            row.find('.display-value').removeClass('d-none');

                            // Show edit button, hide save/cancel buttons
                            row.find('.edit-btn').removeClass('d-none');
                            row.find('.save-btn, .cancel-btn').addClass('d-none');

                            // Show success message
                            showMessage('Корисникот е успешно ажуриран', 'success');
                        } else {
                            showMessage(response.message, 'danger');
                        }
                    },
                    error: function() {
                        showMessage('Грешка при ажурирање на корисникот', 'danger');
                    },
                    complete: function() {
                        // Reset button state
                        row.find('.save-btn').prop('disabled', false).html('<i class="fas fa-save"></i> Зачувај');
                    }
                });
            });

            function showMessage(message, type) {
                const alertHtml = `<div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>`;

                $('.container-fluid').prepend(alertHtml);

                // Auto-hide after 5 seconds
                setTimeout(function() {
                    $('.alert').fadeOut();
                }, 5000);
            }
        });
    </script>
}