@page "{id:long}"
@model NextBroker.Pages.Polisi.ViewEditPolisaKlasa21Model
@{
    ViewData["Title"] = "Измени полиса Класа 21";
    var formClass = !Model.HasAdminAccess ? "form-disabled" : "";
}

<h1 style='text-align: center;'>@ViewData["Title"]</h1>

<div class="container-fluid mt-4 px-4">
    <!-- Success Message -->
    @if (TempData["SuccessMessage"] != null)
    {
        <div id="successMessage" class="alert alert-success alert-dismissible fade show" role="alert">
            <strong>Успешно!</strong> @TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    @if (!Model.HasAdminAccess)
    {
        <style>
            .form-disabled input:not([type="hidden"]),
            .form-disabled select,
            .form-disabled textarea {
                pointer-events: none;
                background-color: #e9ecef;
                opacity: 1;
            }
        </style>
    }

    <form method="post" class="@formClass">
        @Html.AntiForgeryToken()

        <div class="text-end mb-3">
            <button type="button" class="btn btn-outline-secondary btn-sm" id="collapseAllBtn">
                <i class="bi bi-chevron-up"></i> Затвори ги сите секции
            </button>
        </div>

        <!-- Add validation summary -->
        <div asp-validation-summary="All" class="text-danger"></div>

        <!-- Non-editable Information Card -->
        <div class="card mb-3">
            <div class="card-header" role="button" data-bs-toggle="collapse" data-bs-target="#systemInfo" aria-expanded="false" aria-controls="systemInfo" style="cursor: pointer;">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Системски информации</h5>
                    <i class="bi bi-chevron-down"></i>
                </div>
            </div>
            <div class="collapse show" id="systemInfo">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2 mb-3">
                            <label class="form-label">ID</label>
                            <input type="text" class="form-control" value="@Model.Input.Id" readonly />
                            <input type="hidden" asp-for="Input.Id" />
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">Датум на креирање</label>
                            <input type="text" class="form-control" value="@(Model.Input.DateCreated?.ToString("dd.MM.yyyy HH:mm:ss"))" readonly />
                        </div>
                        <div class="col-md-2 mb-3">
                            <label class="form-label">Креирано од</label>
                            <input type="text" class="form-control" value="@Model.Input.UsernameCreated" readonly />
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">Последна промена</label>
                            <input type="text" class="form-control" value="@(Model.Input.DateModified?.ToString("dd.MM.yyyy HH:mm:ss"))" readonly />
                        </div>
                        <div class="col-md-2 mb-3">
                            <label class="form-label">Променето од</label>
                            <input type="text" class="form-control" value="@Model.Input.UsernameModified" readonly />
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Basic Information Card -->
        <div class="card mb-4">
            <div class="card-header" role="button" data-bs-toggle="collapse" data-bs-target="#basicInfo" aria-expanded="false" aria-controls="basicInfo" style="cursor: pointer;">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Основни информации</h5>
                    <i class="bi bi-chevron-down"></i>
                </div>
            </div>
            <div class="collapse show" id="basicInfo">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Осигурител</label>
                            <select asp-for="Input.KlientiIdOsiguritel"
                                    asp-items="Model.Osiguriteli"
                                    class="form-select"
                                    disabled>
                                <option value="">-- Избери осигурител --</option>
                            </select>
                            <input type="hidden" asp-for="Input.KlientiIdOsiguritel" />
                            <span asp-validation-for="Input.KlientiIdOsiguritel" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Класа на осигурување</label>
                            <select asp-for="Input.KlasiOsiguruvanjeIdKlasa"
                                    asp-items="Model.KlasiOsiguruvanje"
                                    class="form-select"
                                    disabled>
                                <option value="">-- Избери класа --</option>
                            </select>
                            <input type="hidden" asp-for="Input.KlasiOsiguruvanjeIdKlasa" />
                            <span asp-validation-for="Input.KlasiOsiguruvanjeIdKlasa" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Продукт</label>
                            <select asp-for="Input.ProduktiIdProizvod"
                                    asp-items="Model.Produkti"
                                    class="form-select"
                                    disabled>
                                <option value="">-- Избери продукт --</option>
                            </select>
                            <input type="hidden" asp-for="Input.ProduktiIdProizvod" />
                            <span asp-validation-for="Input.ProduktiIdProizvod" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label asp-for="Input.BrojNaPolisa" class="form-label">Број на полиса</label>
                            <input asp-for="Input.BrojNaPolisa" class="form-control" readonly />
                            <span asp-validation-for="Input.BrojNaPolisa" class="text-danger"></span>
                        </div>
                        <div class="col-md-3 mb-3 d-none">
                            <label asp-for="Input.BrojNaPonuda" class="form-label">Број на понуда</label>
                            <input asp-for="Input.BrojNaPonuda" class="form-control" type="number" />
                            <span asp-validation-for="Input.BrojNaPonuda" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-10 mb-3">
                            <label class="form-label">Договорувач (пребарувај по МБ, ЕДБ, ЕМБГ, име или назив)</label>
                            <div class="input-group">
                                <input type="text" id="dogovoruvacMBSearch" class="form-control"
                                       autocomplete="off"
                                       placeholder="Пребарувај по МБ, ЕДБ, ЕМБГ, име/назив..."
                                       value="@ViewData["DogovoruvacNaziv"]"
                                       readonly />
                                <input type="hidden" asp-for="Input.KlientiIdDogovoruvac" id="KlientiIdDogovoruvac" />
                            </div>
                            <div id="dogovoruvacSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1"
                                 style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                            </div>
                            @if (!string.IsNullOrEmpty(Model.DogovoruvacEMNGMB))
                            {
                                <div class="mt-2">
                                    <small class="text-muted">ЕМБГ/МБ: <strong>@Model.DogovoruvacEMNGMB</strong></small>
                                </div>
                            }
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-10 mb-3">
                            <label class="form-label">Осигуреник (пребарувај по МБ, ЕДБ, ЕМБГ, име или назив)</label>
                            <div class="input-group">
                                <input type="text" id="osigurenikMBSearch" class="form-control"
                                       autocomplete="off"
                                       placeholder="Пребарувај по МБ, ЕДБ, ЕМБГ, име/назив..."
                                       value="@ViewData["OsigurenikNaziv"]"
                                       readonly />
                                <input type="hidden" asp-for="Input.KlientiIdOsigurenik" id="KlientiIdOsigurenik" />
                            </div>
                            <div id="osigurenikSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1"
                                 style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                            </div>
                            @if (!string.IsNullOrEmpty(Model.OsigurenikEMNGMB))
                            {
                                <div class="mt-2">
                                    <small class="text-muted">ЕМБГ/МБ: <strong>@Model.OsigurenikEMNGMB</strong></small>
                                </div>
                            }
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-10 mb-3">
                            <label class="form-label">Соработник/вработен (пребарувај по МБ, ЕДБ, ЕМБГ, име или назив)</label>
                            <div class="input-group">
                                <input type="text" id="sorabotnikMBSearch" class="form-control"
                                       autocomplete="off"
                                       placeholder="Пребарувај по МБ, ЕДБ, ЕМБГ, име/назив..."
                                       value="@ViewData["SorabotnikNaziv"]" />
                                <button type="button" class="btn btn-outline-danger"
                                        id="nullifySorabotnik"
                                        style="font-size: 0.75rem; padding: 0px 8px; height: 31px; line-height: 29px;">
                                    Анулирај
                                </button>
                                <input type="hidden" asp-for="Input.KlientiIdSorabotnik" id="KlientiIdSorabotnik" />
                            </div>
                            <div id="sorabotnikSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1"
                                 style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="form-check" style="display: none;">
                                <input class="form-check-input" type="checkbox" asp-for="Input.Kolektivna" id="kolektivna" disabled>
                                <input type="hidden" asp-for="Input.Kolektivna" />
                                <label class="form-check-label" for="kolektivna">
                                    Колективна
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="form-check" style="display: none;">
                                <input class="form-check-input" type="checkbox" asp-for="Input.********************************" id="kolektivnaNeodredenBr" disabled>
                                <input type="hidden" asp-for="Input.********************************" />
                                <label class="form-check-label" for="kolektivnaNeodredenBr">
                                    Колективна со неодреден број на осигуреници
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12 mb-3" style="display:none;">
                            <label asp-for="Input.NeodredenBrOsigureniciZabeleska" class="form-label">Забелешка за неодреден број на осигуреници</label>
                            <textarea asp-for="Input.NeodredenBrOsigureniciZabeleska" class="form-control" rows="3" readonly></textarea>
                            <span asp-validation-for="Input.NeodredenBrOsigureniciZabeleska" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.DatumVaziOd" class="form-label">Важи од</label>
                            <input asp-for="Input.DatumVaziOd" class="form-control datepicker" type="date" readonly />
                            <span asp-validation-for="Input.DatumVaziOd" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.DatumVaziDo" class="form-label">Важи до</label>
                            <input asp-for="Input.DatumVaziDo" class="form-control datepicker" type="date" readonly />
                            <span asp-validation-for="Input.DatumVaziDo" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.DatumNaIzdavanje" class="form-label">Датум на издавање</label>
                            <input asp-for="Input.DatumNaIzdavanje" class="form-control datepicker" type="date" readonly />
                            <span asp-validation-for="Input.DatumNaIzdavanje" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3 d-none">
                            <label asp-for="Input.VremetraenjeNaPolisa" class="form-label">Времетраење на полиса (месеци)</label>
                            <input asp-for="Input.VremetraenjeNaPolisa" class="form-control" type="number" min="0" />
                            <span asp-validation-for="Input.VremetraenjeNaPolisa" class="text-danger"></span>
                        </div>
                        <div class="col-md-6 mb-3 d-none">
                            <label asp-for="Input.PeriodNaUplata" class="form-label">Период на уплата (месеци)</label>
                            <input asp-for="Input.PeriodNaUplata" class="form-control" type="number" min="0" />
                            <span asp-validation-for="Input.PeriodNaUplata" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.SifrarnikValutiIdValuta" class="form-label">Валута</label>
                            <select asp-for="Input.SifrarnikValutiIdValuta"
                                    asp-items="Model.Valuti"
                                    class="form-select"
                                    disabled>
                                <option value="">-- Избери валута --</option>
                            </select>
                            <input type="hidden" asp-for="Input.SifrarnikValutiIdValuta" />
                            <span asp-validation-for="Input.SifrarnikValutiIdValuta" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" asp-for="Input.Faktoring" id="faktoring">
                                <label class="form-check-label" for="faktoring">
                                    Факторинг
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.SifrarnikValutiIdFranshizaValuta" class="form-label">Валута на франшиза</label>
                            <select asp-for="Input.SifrarnikValutiIdFranshizaValuta"
                                    asp-items="Model.Valuti"
                                    class="form-select">
                                <option value="">-- Избери валута --</option>
                            </select>
                            <span asp-validation-for="Input.SifrarnikValutiIdFranshizaValuta" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.ProcentFranshiza" class="form-label">Процент на франшиза (%)</label>
                            <input asp-for="Input.ProcentFranshiza" class="form-control" type="number" step="0.01" min="0" max="100" />
                            <span asp-validation-for="Input.ProcentFranshiza" class="text-danger"></span>
                        </div>

                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.FranshizaIznos" class="form-label">Износ на франшиза</label>
                            <input asp-for="Input.FranshizaIznos" class="form-control" type="number" step="0.0001" />
                            <span asp-validation-for="Input.FranshizaIznos" class="text-danger"></span>
                        </div>

                        <div class="col-md-4 mb-3 d-none">
                            <label asp-for="Input.ProcentFinansiski" class="form-label">Процент финансиски (%)</label>
                            <input asp-for="Input.ProcentFinansiski" class="form-control" type="number" step="0.01" min="0" max="100" />
                            <span asp-validation-for="Input.ProcentFinansiski" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.KoregiranaStapkaNaProvizija" class="form-label">Корегирана стапка на провизија (%)</label>
                            <input asp-for="Input.KoregiranaStapkaNaProvizija" class="form-control" type="number" step="0.0001" min="0" max="100" />
                            <span asp-validation-for="Input.KoregiranaStapkaNaProvizija" class="text-danger"></span>
                        </div>
                    </div>
                    <input type="hidden" asp-for="Input.SifrarnikBankiIdBanka" />
                    <input type="hidden" asp-for="Input.SifrarnikTipNaPlakanjeId" value="1" />
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label asp-for="Input.TipNaFaktura" class="form-label">Тип на фактура</label>
                            <select asp-for="Input.TipNaFaktura"
                                    asp-items="Model.TipoviNaFaktura"
                                    class="form-select"
                                    disabled>
                                <option value="">-- Избери тип на фактура --</option>
                            </select>
                            <input type="hidden" asp-for="Input.TipNaFaktura" />
                            <span asp-validation-for="Input.TipNaFaktura" class="text-danger"></span>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="Input.BrojNaFakturaVlezna" class="form-label">Број на влезна фактура</label>
                            <input asp-for="Input.BrojNaFakturaVlezna" class="form-control" readonly/>
                            <input type="hidden" asp-for="Input.BrojNaFakturaVlezna" />
                            <span asp-validation-for="Input.BrojNaFakturaVlezna" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="Input.DatumNaFakturaVlezna" class="form-label">Датум на влезна фактура</label>
                            <input asp-for="Input.DatumNaFakturaVlezna" class="form-control datepicker" type="date" readonly/>
                            <input type="hidden" asp-for="Input.DatumNaFakturaVlezna" />
                            <span asp-validation-for="Input.DatumNaFakturaVlezna" class="text-danger"></span>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="Input.RokNaPlakjanjeFakturaVlezna" class="form-label">Рок на плаќање на влезна фактура</label>
                            <input asp-for="Input.RokNaPlakjanjeFakturaVlezna" class="form-control datepicker" type="date" readonly/>
                            <input type="hidden" asp-for="Input.RokNaPlakjanjeFakturaVlezna" />
                            <span asp-validation-for="Input.RokNaPlakjanjeFakturaVlezna" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row" style="display: @(Model.Input.GeneriranaFakturaIzlezna ? "block" : "none")">
                        <div class="col-md-3 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" asp-for="Input.GeneriranaFakturaIzlezna" id="generiranaFakturaIzlezna" disabled>
                                <input type="hidden" asp-for="Input.GeneriranaFakturaIzlezna" />
                                <label class="form-check-label" for="generiranaFakturaIzlezna">
                                    Генерирана излезна фактура
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="row" style="display: @(Model.Input.GeneriranaFakturaIzlezna ? "block" : "none")">
                        <div class="col-md-6 mb-3">
                            <label asp-for="Input.BrojNaFakturaIzlezna" class="form-label">Број на излезна фактура</label>
                            <input asp-for="Input.BrojNaFakturaIzlezna" class="form-control" readonly />
                            <span asp-validation-for="Input.BrojNaFakturaIzlezna" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row" style="display: @(Model.Input.GeneriranaFakturaIzlezna ? "block" : "none")">
                        <div class="col-md-6 mb-3">
                            <label asp-for="Input.DatumNaIzleznaFaktura" class="form-label">Датум на излезна фактура</label>
                            <input asp-for="Input.DatumNaIzleznaFaktura" class="form-control datepicker" type="date" readonly />
                            <span asp-validation-for="Input.DatumNaIzleznaFaktura" class="text-danger"></span>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="Input.RokNaPlakjanjeFakturaIzlezna" class="form-label">Рок на плаќање на излезна фактура</label>
                            <input asp-for="Input.RokNaPlakjanjeFakturaIzlezna" class="form-control datepicker" type="date" readonly />
                            <span asp-validation-for="Input.RokNaPlakjanjeFakturaIzlezna" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" asp-for="Input.Storno" id="storno" disabled="@(!Model.HasStornoAccess)">
                                <label class="form-check-label" for="storno">
                                    Сторно
                                </label>
                                <input type="hidden" asp-for="Input.Storno" />
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label asp-for="Input.PricinaZaStorno" class="form-label">Причина за сторно</label>
                            <textarea asp-for="Input.PricinaZaStorno" class="form-control" rows="3"></textarea>
                            <span asp-validation-for="Input.PricinaZaStorno" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label asp-for="Input.Zabeleska" class="form-label">Забелешка</label>
                            <textarea asp-for="Input.Zabeleska" class="form-control" rows="3"></textarea>
                            <span asp-validation-for="Input.Zabeleska" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.SifrarnikNacinNaPlakjanjeId" class="form-label">Начин на плаќање</label>
                            <select asp-for="Input.SifrarnikNacinNaPlakjanjeId"
                                    asp-items="Model.NaciniNaPlakanje"
                                    class="form-select"
                                    disabled>
                                <option value="">-- Избери начин на плаќање --</option>
                            </select>
                            <input type="hidden" asp-for="Input.SifrarnikNacinNaPlakjanjeId" />
                            <span asp-validation-for="Input.SifrarnikNacinNaPlakjanjeId" class="text-danger"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Klasa 21 - Животно осигурување Card -->
        <div class="card mb-4">
            <div class="card-header" role="button" data-bs-toggle="collapse" data-bs-target="#klasa21Info" aria-expanded="false" aria-controls="klasa21Info" style="cursor: pointer;">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Класа 21 - Животно осигурување</h5>
                    <i class="bi bi-chevron-down"></i>
                </div>
            </div>
            <div class="collapse show" id="klasa21Info">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label class="form-label">Датум на креирање</label>
                            <input type="text" class="form-control" value="@(Model.Klasa21Input.DateCreated?.ToString("dd.MM.yyyy HH:mm:ss"))" readonly />
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">Креирано од</label>
                            <input type="text" class="form-control" value="@Model.Klasa21Input.UsernameCreated" readonly />
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">Последна промена</label>
                            <input type="text" class="form-control" value="@(Model.Klasa21Input.DateModified?.ToString("dd.MM.yyyy HH:mm:ss"))" readonly />
                        </div>
                        <div class="col-md-3 mb-3">
                            <label class="form-label">Променето од</label>
                            <input type="text" class="form-control" value="@Model.Klasa21Input.UsernameModified" readonly />
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label asp-for="Klasa21Input.ShifrarnikInvesticiskiFondIdInvesticiskiFond" class="form-label">Инвестициски фонд</label>
                            <select asp-for="Klasa21Input.ShifrarnikInvesticiskiFondIdInvesticiskiFond"
                                    asp-items="Model.InvesticiskiFondovi"
                                    class="form-select"
                                    disabled>
                                <option value="">-- Избери инвестициски фонд --</option>
                            </select>
                            <input type="hidden" asp-for="Klasa21Input.ShifrarnikInvesticiskiFondIdInvesticiskiFond" />
                            <span asp-validation-for="Klasa21Input.ShifrarnikInvesticiskiFondIdInvesticiskiFond" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Klasa21Input.NazivNaInvesticiskoDrushtvo" class="form-label">Назив на инвестициско друштво</label>
                            <select asp-for="Klasa21Input.NazivNaInvesticiskoDrushtvo"
                                    asp-items="Model.InvesticiskaDrushtva"
                                    class="form-select"
                                    disabled>
                                <option value="">-- Избери друштво --</option>
                            </select>
                            <input type="hidden" asp-for="Klasa21Input.NazivNaInvesticiskoDrushtvo" />
                            <span asp-validation-for="Klasa21Input.NazivNaInvesticiskoDrushtvo" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Klasa21Input.NazivNaInvesticiskiFond" class="form-label">Назив на инвестициски фонд</label>
                            <input asp-for="Klasa21Input.NazivNaInvesticiskiFond" class="form-control" readonly />
                            <span asp-validation-for="Klasa21Input.NazivNaInvesticiskiFond" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="Klasa21Input.IdentifikaciskiBrojaNaInvesticiskiFond" class="form-label">Идентификациски број на инвестициски фонд</label>
                            <input asp-for="Klasa21Input.IdentifikaciskiBrojaNaInvesticiskiFond" class="form-control" readonly />
                            <span asp-validation-for="Klasa21Input.IdentifikaciskiBrojaNaInvesticiskiFond" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label asp-for="Klasa21Input.ProcentOdPremijaPoIFGodishnoEdnokratno" class="form-label">Процент од премија по ИФ годишно/еднократно</label>
                            <input asp-for="Klasa21Input.ProcentOdPremijaPoIFGodishnoEdnokratno" class="form-control" type="number" step="0.0001" readonly />
                            <span asp-validation-for="Klasa21Input.ProcentOdPremijaPoIFGodishnoEdnokratno" class="text-danger"></span>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label asp-for="Klasa21Input.ProcentOdPremijaVoOsiguruvanjeGodishnoEdnokratno" class="form-label">Процент од премија во осигурување годишно/еднократно</label>
                            <input asp-for="Klasa21Input.ProcentOdPremijaVoOsiguruvanjeGodishnoEdnokratno" class="form-control" type="number" step="0.0001" readonly />
                            <span asp-validation-for="Klasa21Input.ProcentOdPremijaVoOsiguruvanjeGodishnoEdnokratno" class="text-danger"></span>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label asp-for="Klasa21Input.IznosOdPremijaPoIFGodishnoEdnokratno" class="form-label">Износ од премија по ИФ годишно/еднократно</label>
                            <input asp-for="Klasa21Input.IznosOdPremijaPoIFGodishnoEdnokratno" class="form-control" type="number" step="0.0001" readonly />
                            <span asp-validation-for="Klasa21Input.IznosOdPremijaPoIFGodishnoEdnokratno" class="text-danger"></span>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label asp-for="Klasa21Input.IznosOdPremijaVoOsiguruvanjeGodishnoEdnokratno" class="form-label">Износ од премија во осигурување годишно/еднократно</label>
                            <input asp-for="Klasa21Input.IznosOdPremijaVoOsiguruvanjeGodishnoEdnokratno" class="form-control" type="number" step="0.0001" readonly />
                            <span asp-validation-for="Klasa21Input.IznosOdPremijaVoOsiguruvanjeGodishnoEdnokratno" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label asp-for="Klasa21Input.VkupenIznosNaPremijaZaOsiguruvanje" class="form-label">Вкупен износ на премија за осигурување</label>
                            <input asp-for="Klasa21Input.VkupenIznosNaPremijaZaOsiguruvanje" class="form-control" type="number" step="0.0001" readonly />
                            <span asp-validation-for="Klasa21Input.VkupenIznosNaPremijaZaOsiguruvanje" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Klasa21Input.VkupenIznosNaPremijaZaIF" class="form-label">Вкупен износ на премија за ИФ</label>
                            <input asp-for="Klasa21Input.VkupenIznosNaPremijaZaIF" class="form-control" type="number" step="0.0001" readonly />
                            <span asp-validation-for="Klasa21Input.VkupenIznosNaPremijaZaIF" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Klasa21Input.VkupenIznosNaTroshociOdPremija" class="form-label">Вкупен износ на трошоци од премија</label>
                            <input asp-for="Klasa21Input.VkupenIznosNaTroshociOdPremija" class="form-control" type="number" step="0.0001" readonly />
                            <span asp-validation-for="Klasa21Input.VkupenIznosNaTroshociOdPremija" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label asp-for="Klasa21Input.OsigurenaSumaZaDozhivuvanje" class="form-label">Осигурена сума за доживување</label>
                            <input asp-for="Klasa21Input.OsigurenaSumaZaDozhivuvanje" class="form-control" readonly />
                            <span asp-validation-for="Klasa21Input.OsigurenaSumaZaDozhivuvanje" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Klasa21Input.OsigurenaSumaZaSmrtOdBolest" class="form-label">Осигурена сума за смрт од болест</label>
                            <input asp-for="Klasa21Input.OsigurenaSumaZaSmrtOdBolest" class="form-control" readonly />
                            <span asp-validation-for="Klasa21Input.OsigurenaSumaZaSmrtOdBolest" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Klasa21Input.OsigurenaSumaZaSmrtOdNezgoda" class="form-label">Осигурена сума за смрт од незгода</label>
                            <input asp-for="Klasa21Input.OsigurenaSumaZaSmrtOdNezgoda" class="form-control" readonly />
                            <span asp-validation-for="Klasa21Input.OsigurenaSumaZaSmrtOdNezgoda" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label asp-for="Klasa21Input.PrivremenaOsiguritelnaZastita" class="form-label">Привремена осигурителна заштита</label>
                            <input asp-for="Klasa21Input.PrivremenaOsiguritelnaZastita" class="form-control" readonly />
                            <span asp-validation-for="Klasa21Input.PrivremenaOsiguritelnaZastita" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Klasa21Input.PremijaGodishnaEdnokratna" class="form-label">Премија годишна/еднократна</label>
                            <input asp-for="Klasa21Input.PremijaGodishnaEdnokratna" class="form-control" readonly />
                            <span asp-validation-for="Klasa21Input.PremijaGodishnaEdnokratna" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Klasa21Input.BrojNaUdeliPoIF" class="form-label">Број на удели по ИФ</label>
                            <input asp-for="Klasa21Input.BrojNaUdeliPoIF" class="form-control" type="number" readonly />
                            <span asp-validation-for="Klasa21Input.BrojNaUdeliPoIF" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label asp-for="Klasa21Input.VkupnaPremijaGodishna" class="form-label">Вкупна премија годишна</label>
                            <input asp-for="Klasa21Input.VkupnaPremijaGodishna" class="form-control" type="number" step="0.0001" readonly />
                            <span asp-validation-for="Klasa21Input.VkupnaPremijaGodishna" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Klasa21Input.VkupnaPremijaEdnokratna" class="form-label">Вкупна премија еднократна</label>
                            <input asp-for="Klasa21Input.VkupnaPremijaEdnokratna" class="form-control" type="number" step="0.0001" readonly />
                            <span asp-validation-for="Klasa21Input.VkupnaPremijaEdnokratna" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Klasa21Input.DoplatociZaPodgodishnoPlakjanje" class="form-label">Доплатоци за подгодишно плаќање</label>
                            <input asp-for="Klasa21Input.DoplatociZaPodgodishnoPlakjanje" class="form-control" type="number" step="0.0001" readonly />
                            <span asp-validation-for="Klasa21Input.DoplatociZaPodgodishnoPlakjanje" class="text-danger"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Попусти -->
        <div class="card mb-4">
            <div class="card-header" role="button" data-bs-toggle="collapse" data-bs-target="#popustiInfo" aria-expanded="false" aria-controls="popustiInfo" style="cursor: pointer;">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Попусти</h5>
                    <i class="bi bi-chevron-down"></i>
                </div>
            </div>
            <div class="collapse show" id="popustiInfo">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label asp-for="Klasa21Input.ProcentNaPopustZaFakturaVoRok" class="form-label">Процент на попуст за фактура во рок</label>
                            <input asp-for="Klasa21Input.ProcentNaPopustZaFakturaVoRok" class="form-control" type="number" step="0.0001" readonly />
                            <span asp-validation-for="Klasa21Input.ProcentNaPopustZaFakturaVoRok" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Klasa21Input.IznosZaPlakjanjeVoRok" class="form-label">Износ за плаќanje во рок</label>
                            <input asp-for="Klasa21Input.IznosZaPlakjanjeVoRok" class="form-control" type="number" step="0.0001" readonly />
                            <span asp-validation-for="Klasa21Input.IznosZaPlakjanjeVoRok" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Klasa21Input.ProcentKomercijalenPopust" class="form-label">Процент комерцијален попуст</label>
                            <input asp-for="Klasa21Input.ProcentKomercijalenPopust" class="form-control" type="number" step="0.0001" readonly />
                            <span asp-validation-for="Klasa21Input.ProcentKomercijalenPopust" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label asp-for="Klasa21Input.ProcentFinansiski" class="form-label">Процент финансиски попуст</label>
                            <input asp-for="Klasa21Input.ProcentFinansiski" class="form-control" type="number" step="0.0001" readonly />
                            <span asp-validation-for="Klasa21Input.ProcentFinansiski" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Klasa21Input.PremijaZaNaplata" class="form-label">Премија за наплата</label>
                            <input asp-for="Klasa21Input.PremijaZaNaplata" class="form-control" type="number" step="0.0001" readonly />
                            <span asp-validation-for="Klasa21Input.PremijaZaNaplata" class="text-danger"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Supplementary Insurance Sections -->
        @if (Model.Klasa1Entries?.Any() == true)
        {
            <div class="card mb-4">
                <div class="card-header" role="button" data-bs-toggle="collapse" data-bs-target="#klasa1Info" aria-expanded="false" aria-controls="klasa1Info" style="cursor: pointer;">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Дополнително осигурување - Класа 1 (Незгоди)</h5>
                        <i class="bi bi-chevron-down"></i>
                    </div>
                </div>
                <div class="collapse" id="klasa1Info">
                    <div class="card-body">
                        @for (int i = 0; i < Model.Klasa1Entries.Count; i++)
                        {
                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <h6>Запис @(i + 1)</h6>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label class="form-label">Осигурена сума за смрт од незгода</label>
                                    <input asp-for="Klasa1Entries[i].OsigurenaSumaZaSmrtOdNezgoda" class="form-control" type="number" step="0.01" readonly />
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label class="form-label">Осигурена сума за 100% траен инвалидитет</label>
                                    <input asp-for="Klasa1Entries[i].OsigurenaSumaZa100ProcTraenInvaliditet" class="form-control" type="number" step="0.01" readonly />
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label class="form-label">Осигурена сума за траен инвалидитет</label>
                                    <input asp-for="Klasa1Entries[i].OsigurenaSumaZaTraenInvaliditet" class="form-control" type="number" step="0.01" readonly />
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label class="form-label">Дневен надомест</label>
                                    <input asp-for="Klasa1Entries[i].DnevenNadomest" class="form-control" type="number" step="0.01" readonly />
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label class="form-label">Премија незгода годишна</label>
                                    <input asp-for="Klasa1Entries[i].PremijaNezgodaGodishna" class="form-control" type="number" step="0.01" readonly />
                                </div>
                            </div>
                            @if (i < Model.Klasa1Entries.Count - 1)
                            {
                                <hr />
                            }
                        }
                    </div>
                </div>
            </div>
        }

        @if (Model.Klasa2Entries?.Any() == true)
        {
            <div class="card mb-4">
                <div class="card-header" role="button" data-bs-toggle="collapse" data-bs-target="#klasa2Info" aria-expanded="false" aria-controls="klasa2Info" style="cursor: pointer;">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Дополнително осигурување - Класа 2 (Здравствено)</h5>
                        <i class="bi bi-chevron-down"></i>
                    </div>
                </div>
                <div class="collapse" id="klasa2Info">
                    <div class="card-body">
                        @for (int i = 0; i < Model.Klasa2Entries.Count; i++)
                        {
                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <h6>Запис @(i + 1)</h6>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label class="form-label">Осигурена сума за тешко болни состојби</label>
                                    <input asp-for="Klasa2Entries[i].OsigurenaSumaZaTeskoBolniSostojbi" class="form-control" type="number" step="0.01" readonly />
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label class="form-label">Премија тешко болни состојби годишна</label>
                                    <input asp-for="Klasa2Entries[i].PremijaTeshkoBolniSostojbiGodishna" class="form-control" type="number" step="0.01" readonly />
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label class="form-label">Осигурена сума за операции</label>
                                    <input asp-for="Klasa2Entries[i].OsigurenaSumaZaOperacii" class="form-control" type="number" step="0.01" readonly />
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label class="form-label">Премија за операции годишна</label>
                                    <input asp-for="Klasa2Entries[i].PremijaZaOperaciiGodishna" class="form-control" type="number" step="0.01" readonly />
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label class="form-label">Осигурена сума за трајна неспособност</label>
                                    <input asp-for="Klasa2Entries[i].OsigurenaSumaZaTrajnaNesposobnost" class="form-control" type="number" step="0.01" readonly />
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label class="form-label">Премија за трајна неспособност годишна</label>
                                    <input asp-for="Klasa2Entries[i].PremijaZaTrajnaNesposobnostGodishna" class="form-control" type="number" step="0.01" readonly />
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label class="form-label">Осигурена сума за хируршки интервенции</label>
                                    <input asp-for="Klasa2Entries[i].OsigurenaSumaZaHirushkiIntervencii" class="form-control" type="number" step="0.01" readonly />
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label class="form-label">Премија за хируршки интервенции годишна</label>
                                    <input asp-for="Klasa2Entries[i].PremijaZaHirushkiIntervenciiGodishna" class="form-control" type="number" step="0.01" readonly />
                                </div>
                            </div>
                            @if (i < Model.Klasa2Entries.Count - 1)
                            {
                                <hr />
                            }
                        }
                    </div>
                </div>
            </div>
        }

        @if (Model.HasAdminAccess)
        {
            <div class="text-center mt-4">
                <button type="submit" class="btn btn-primary btn-lg">
                    <i class="bi bi-save"></i> Зачувај промени
                </button>
                <a href="@Url.Page("ListaPolisi")" class="btn btn-secondary btn-lg ms-2">
                    <i class="bi bi-arrow-left"></i> Назад
                </a>
            </div>
        }
        else
        {
            <div class="text-center mt-4">
                <a href="@Url.Page("ListaPolisi")" class="btn btn-secondary btn-lg">
                    <i class="bi bi-arrow-left"></i> Назад
                </a>
            </div>
        }
    </form>
</div>

<script>
    $(document).ready(function() {
        // Collapse all sections functionality
        $('#collapseAllBtn').click(function() {
            var isCollapsed = $('.collapse.show').length === 0;
            if (isCollapsed) {
                $('.collapse').addClass('show');
                $(this).html('<i class="bi bi-chevron-up"></i> Затвори ги сите секции');
            } else {
                $('.collapse').removeClass('show');
                $(this).html('<i class="bi bi-chevron-down"></i> Отвори ги сите секции');
            }
        });

        // Update chevron icons when sections are toggled
        $('.card-header[data-bs-toggle="collapse"]').click(function() {
            var target = $(this).attr('data-bs-target');
            var icon = $(this).find('i');

            setTimeout(function() {
                if ($(target).hasClass('show')) {
                    icon.removeClass('bi-chevron-down').addClass('bi-chevron-up');
                } else {
                    icon.removeClass('bi-chevron-up').addClass('bi-chevron-down');
                }
            }, 350);
        });

        // Client search functionality for Dogovoruvac
        let dogovoruvacTimeout;
        $('#dogovoruvacMBSearch').on('input', function() {
            clearTimeout(dogovoruvacTimeout);
            const searchTerm = $(this).val();

            if (searchTerm.length < 2) {
                $('#dogovoruvacSearchResults').hide();
                return;
            }

            dogovoruvacTimeout = setTimeout(function() {
                $.get('/Polisi/ViewEditPolisaKlasa21?handler=SearchKlienti', { mb: searchTerm })
                    .done(function(data) {
                        let html = '';
                        data.forEach(function(item) {
                            let displayText = item.naziv;
                            if (item.mb) displayText += ' (МБ: ' + item.mb + ')';
                            if (item.embg) displayText += ' (ЕМБГ: ' + item.embg + ')';
                            if (item.edb) displayText += ' (ЕДБ: ' + item.edb + ')';

                            html += '<div class="search-result-item p-2 border-bottom" style="cursor: pointer;" ' +
                                   'data-id="' + item.id + '" data-naziv="' + item.naziv + '">' +
                                   displayText + '</div>';
                        });
                        $('#dogovoruvacSearchResults').html(html).show();
                    });
            }, 300);
        });

        // Handle dogovoruvac selection
        $(document).on('click', '#dogovoruvacSearchResults .search-result-item', function() {
            const id = $(this).data('id');
            const naziv = $(this).data('naziv');

            $('#KlientiIdDogovoruvac').val(id);
            $('#dogovoruvacMBSearch').val(naziv);
            $('#dogovoruvacSearchResults').hide();
        });

        // Client search functionality for Osigurenik
        let osigurenikTimeout;
        $('#osigurenikMBSearch').on('input', function() {
            clearTimeout(osigurenikTimeout);
            const searchTerm = $(this).val();

            if (searchTerm.length < 2) {
                $('#osigurenikSearchResults').hide();
                return;
            }

            osigurenikTimeout = setTimeout(function() {
                $.get('/Polisi/ViewEditPolisaKlasa21?handler=SearchKlienti', { mb: searchTerm })
                    .done(function(data) {
                        let html = '';
                        data.forEach(function(item) {
                            let displayText = item.naziv;
                            if (item.mb) displayText += ' (МБ: ' + item.mb + ')';
                            if (item.embg) displayText += ' (ЕМБГ: ' + item.embg + ')';
                            if (item.edb) displayText += ' (ЕДБ: ' + item.edb + ')';

                            html += '<div class="search-result-item p-2 border-bottom" style="cursor: pointer;" ' +
                                   'data-id="' + item.id + '" data-naziv="' + item.naziv + '">' +
                                   displayText + '</div>';
                        });
                        $('#osigurenikSearchResults').html(html).show();
                    });
            }, 300);
        });

        // Handle osigurenik selection
        $(document).on('click', '#osigurenikSearchResults .search-result-item', function() {
            const id = $(this).data('id');
            const naziv = $(this).data('naziv');

            $('#KlientiIdOsigurenik').val(id);
            $('#osigurenikMBSearch').val(naziv);
            $('#osigurenikSearchResults').hide();
        });

        // Client search functionality for Sorabotnik
        let sorabotnikTimeout;
        $('#sorabotnikMBSearch').on('input', function() {
            clearTimeout(sorabotnikTimeout);
            const searchTerm = $(this).val();

            if (searchTerm.length < 2) {
                $('#sorabotnikSearchResults').hide();
                return;
            }

            sorabotnikTimeout = setTimeout(function() {
                $.get('/Polisi/ViewEditPolisaKlasa21?handler=SearchKlienti', { mb: searchTerm })
                    .done(function(data) {
                        let html = '';
                        data.forEach(function(item) {
                            let displayText = item.naziv;
                            if (item.mb) displayText += ' (МБ: ' + item.mb + ')';
                            if (item.embg) displayText += ' (ЕМБГ: ' + item.embg + ')';
                            if (item.edb) displayText += ' (ЕДБ: ' + item.edb + ')';

                            html += '<div class="search-result-item p-2 border-bottom" style="cursor: pointer;" ' +
                                   'data-id="' + item.id + '" data-naziv="' + item.naziv + '">' +
                                   displayText + '</div>';
                        });
                        $('#sorabotnikSearchResults').html(html).show();
                    });
            }, 300);
        });

        // Handle sorabotnik selection
        $(document).on('click', '#sorabotnikSearchResults .search-result-item', function() {
            const id = $(this).data('id');
            const naziv = $(this).data('naziv');

            $('#KlientiIdSorabotnik').val(id);
            $('#sorabotnikMBSearch').val(naziv);
            $('#sorabotnikSearchResults').hide();
        });

        // Nullify sorabotnik
        $('#nullifySorabotnik').click(function() {
            $('#KlientiIdSorabotnik').val('');
            $('#sorabotnikMBSearch').val('');
            $('#sorabotnikSearchResults').hide();
        });

        // Hide search results when clicking outside
        $(document).click(function(e) {
            if (!$(e.target).closest('#dogovoruvacMBSearch, #dogovoruvacSearchResults').length) {
                $('#dogovoruvacSearchResults').hide();
            }
            if (!$(e.target).closest('#osigurenikMBSearch, #osigurenikSearchResults').length) {
                $('#osigurenikSearchResults').hide();
            }
            if (!$(e.target).closest('#sorabotnikMBSearch, #sorabotnikSearchResults').length) {
                $('#sorabotnikSearchResults').hide();
            }
        });

        // Auto-hide success message after 5 seconds
        setTimeout(function() {
            $('#successMessage').fadeOut();
        }, 5000);
    });
</script>
