using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using Microsoft.Data.SqlClient;
using System.ComponentModel.DataAnnotations;
using System.Data;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace NextBroker.Pages.Pregledi
{
    public class IzvestajPolisiNivoNaVrabotenSorabotnikModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;

        public IzvestajPolisiNivoNaVrabotenSorabotnikModel(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        [BindProperty]
        public ReportInputModel Input { get; set; } = new();

        public List<PolisaReportResult> Results { get; set; } = new();
        public IEnumerable<SelectListItem> VraboteniSorabotnici { get; set; } = new List<SelectListItem>();
        public bool IsDataLoaded { get; set; }
        public string ErrorMessage { get; set; }
        public List<OsiguritelOption> OsiguriteliOptions { get; set; } = new();
        public List<KlasaOption> KlasiOsiguruvanjeOptions { get; set; } = new();
        public List<ProduktOption> ProduktiOptions { get; set; } = new();
        public List<EkspozituraOption> EkspozituriOptions { get; set; } = new();

        public class ReportInputModel
        {
            [Required(ErrorMessage = "Датум од е задолжително поле")]
            [Display(Name = "Датум на издавање од")]
            public DateTime? DatumNaIzdavanjeOd { get; set; }

            [Required(ErrorMessage = "Датум до е задолжително поле")]
            [Display(Name = "Датум на издавање до")]
            public DateTime? DatumNaIzdavanjeDo { get; set; }

            [Required(ErrorMessage = "Вработен/Соработник е задолжително поле")]
            [Display(Name = "Вработен/Соработник (корисничко име)")]
            public string VrabotenSorabotnik { get; set; }

            [Display(Name = "Осигурител")]
            public int? KlientiIdOsiguritel { get; set; }

            [Display(Name = "Класа осигурување")]
            public long? KlasiOsiguruvanjeIdKlasa { get; set; }

            [Display(Name = "Продукт")]
            public int? ProduktiIdProizvod { get; set; }

            [Display(Name = "Експозитура")]
            public string? EkspozituraIme { get; set; }

            [Display(Name = "Сторно")]
            public string? Storno { get; set; }
        }

        public class PolisaReportResult
        {
            public long Id { get; set; }
            public string BrojNaPolisa { get; set; }
            public string DatumVaziOd { get; set; }
            public string DatumVaziDo { get; set; }
            public string Osiguritel { get; set; }
            public string Klasa { get; set; }
            public string Produkt { get; set; }
            public string Dogovoruvac { get; set; }
            public string DogovoruvacAdresa { get; set; }
            public string DogovoruvacTelefon { get; set; }
            public string Osigurenik { get; set; }
            public string OsigurenikAdresa { get; set; }
            public string OsigurenikTelefon { get; set; }
            public string NacinNaPlakanje { get; set; }
            public decimal? PremijaPolisirana { get; set; }
            public decimal? PremijaNaplatenaOdDogovoruvac { get; set; }
            public decimal? DolgPremijaOdDogovoruvac { get; set; }
            public string DolgPoDenovi { get; set; }
            public string KlientFizickoPravnoLice { get; set; }
            public string Ekspozitura { get; set; }
            public string KreiranoOd { get; set; }
            public string TipNaSorabotnik { get; set; }
            public string ImePrezimeSorabotnik { get; set; }
            public string ImePrezimeNadreden { get; set; }
            public decimal? ProvizijaNaplateno { get; set; }
            public decimal? PrenesenaPremijaKonOsiguritel { get; set; }
            public string Storno { get; set; }
            public string Zabeleska { get; set; }
        }

        public class OsiguritelOption
        {
            public long Id { get; set; }
            public string Naziv { get; set; }
        }

        public class KlasaOption
        {
            public int Id { get; set; }
            public string KlasaIme { get; set; }
        }

        public class ProduktOption
        {
            public int Id { get; set; }
            public string Ime { get; set; }
        }

        public class EkspozituraOption
        {
            public int Id { get; set; }
            public string Ime { get; set; }
        }

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("IzvestajPolisiNivoNaVrabotenSorabotnik"))
            {
                return RedirectToAccessDenied();
            }

            // Set default dates (current month)
            var now = DateTime.Now;
            Input.DatumNaIzdavanjeOd = new DateTime(now.Year, now.Month, 1);
            Input.DatumNaIzdavanjeDo = new DateTime(now.Year, now.Month, DateTime.DaysInMonth(now.Year, now.Month));

            // Load dropdown options
            await LoadVraboteniSorabotnici();
            await LoadOsiguriteliOptions();
            await LoadKlasiOsiguruvanjeOptions();
            await LoadProduktiOptions();
            await LoadEkspozituriOptions();

            return Page();
        }

        public async Task<IActionResult> OnPost()
        {
            if (!await HasPageAccess("IzvestajPolisiNivoNaVrabotenSorabotnik"))
            {
                return RedirectToAccessDenied();
            }

            if (!ModelState.IsValid)
            {
                await LoadVraboteniSorabotnici();
                await LoadOsiguriteliOptions();
                await LoadKlasiOsiguruvanjeOptions();
                await LoadProduktiOptions();
                await LoadEkspozituriOptions();
                return Page();
            }

            try
            {
                await LoadReportData();
                IsDataLoaded = true;
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Грешка при вчитување на податоците: {ex.Message}";
            }

            await LoadVraboteniSorabotnici();
            await LoadOsiguriteliOptions();
            await LoadKlasiOsiguruvanjeOptions();
            await LoadProduktiOptions();
            await LoadEkspozituriOptions();
            return Page();
        }

        public async Task<IActionResult> OnPostExportExcel()
        {
            if (!await HasPageAccess("IzvestajPolisiNivoNaVrabotenSorabotnik"))
            {
                return RedirectToAccessDenied();
            }

            if (!ModelState.IsValid)
            {
                await LoadVraboteniSorabotnici();
                await LoadOsiguriteliOptions();
                await LoadKlasiOsiguruvanjeOptions();
                await LoadProduktiOptions();
                await LoadEkspozituriOptions();
                return Page();
            }

            try
            {
                await LoadReportData();
                return await GenerateExcelReport();
            }
            catch (Exception ex)
            {
                ErrorMessage = $"Грешка при генерирање на Excel извештај: {ex.Message}";
                await LoadVraboteniSorabotnici();
                await LoadOsiguriteliOptions();
                await LoadKlasiOsiguruvanjeOptions();
                await LoadProduktiOptions();
                await LoadEkspozituriOptions();
                return Page();
            }
        }

        private async Task LoadVraboteniSorabotnici()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Username
                    FROM users
                    WHERE emb IN (
                        SELECT embg
                        FROM Klienti
                        WHERE KlientVraboten = 1 OR KlientSorabotnik = 1
                    )
                    ORDER BY Username", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>
                    {
                        new SelectListItem("-- Изберете вработен/соработник --", "")
                    };

                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Username"].ToString(),
                            reader["Username"].ToString()
                        ));
                    }
                    VraboteniSorabotnici = items;
                }
            }
        }

        private async Task LoadOsiguriteliOptions()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                string query = "select Id, Naziv from Klienti where Osiguritel = 1 order by Naziv";

                using (SqlCommand cmd = new SqlCommand(query, connection))
                {
                    using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                    {
                        OsiguriteliOptions = new List<OsiguritelOption>();
                        while (await reader.ReadAsync())
                        {
                            OsiguriteliOptions.Add(new OsiguritelOption
                            {
                                Id = reader.GetInt64("Id"),
                                Naziv = reader.IsDBNull("Naziv") ? "" : reader.GetString("Naziv")
                            });
                        }
                    }
                }
            }
        }

        private async Task LoadKlasiOsiguruvanjeOptions()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                string query = "select Id, KlasaIme from KlasiOsiguruvanje order by KlasaIme";

                using (SqlCommand cmd = new SqlCommand(query, connection))
                {
                    using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                    {
                        KlasiOsiguruvanjeOptions = new List<KlasaOption>();
                        while (await reader.ReadAsync())
                        {
                            KlasiOsiguruvanjeOptions.Add(new KlasaOption
                            {
                                Id = reader.GetInt32("Id"),
                                KlasaIme = reader.IsDBNull("KlasaIme") ? "" : reader.GetString("KlasaIme")
                            });
                        }
                    }
                }
            }
        }

        private async Task LoadProduktiOptions()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                string query = "select Id, Ime from Produkti order by Ime";

                using (SqlCommand cmd = new SqlCommand(query, connection))
                {
                    using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                    {
                        ProduktiOptions = new List<ProduktOption>();
                        while (await reader.ReadAsync())
                        {
                            ProduktiOptions.Add(new ProduktOption
                            {
                                Id = reader.GetInt32("Id"),
                                Ime = reader.IsDBNull("Ime") ? "" : reader.GetString("Ime")
                            });
                        }
                    }
                }
            }
        }

        private async Task LoadEkspozituriOptions()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                string query = "select Id, Ime from Ekspozituri order by Ime";

                using (SqlCommand cmd = new SqlCommand(query, connection))
                {
                    using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                    {
                        EkspozituriOptions = new List<EkspozituraOption>();
                        while (await reader.ReadAsync())
                        {
                            EkspozituriOptions.Add(new EkspozituraOption
                            {
                                Id = reader.GetInt32("Id"),
                                Ime = reader.IsDBNull("Ime") ? "" : reader.GetString("Ime")
                            });
                        }
                    }
                }
            }
        }

        private async Task LoadReportData()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                string query = @"
                    select
                    p.Id,
                    p.BrojNaPolisa,
                    p.DatumVaziOd,
                    p.DatumVaziDo,
                    osig.Naziv as [Osiguritel],
                    kls.KlasaIme as [Klasa],
                    prod.Ime as [Produkt],
                    COALESCE(dogvrc.Ime, '') + ' ' +  COALESCE(dogvrc.Prezime, '') + COALESCE(dogvrc.Naziv, '') as [Dogovoruvac],
                    dbo.VratiAdresaDogovoruvacPoPolisaId(p.id) as [DogovoruvacAdresa],
                    dogvrc.Tel as [DogovoruvacTelefon],
                    COALESCE(osgrnk.Ime, '') + ' ' +  COALESCE(osgrnk.Prezime, '') + COALESCE(osgrnk.Naziv, '') as [Osigurenik],
                    dbo.VratiAdresaOsigurenikPoPolisaId(p.id) as [OsigurenikAdresa],
                    osgrnk.Tel as [OsigurenikTelefon],
                    snnp.NacinNaPlakanje,
                    dbo.VratiPolisaIznosZaPlakanjePoPolisaId(p.id) as [PremijaPolisirana],
                    dbo.VratiPolisaUplatenIznos(p.id) as [PremijaNaplatenaOdDogovoruvac],
                    dbo.VratiPolisaDospeanDolg(p.Id) as [DolgPremijaOdDogovoruvac],
                    dbo.VratiPolisaDolgPoDenoviVoRang(p.Id) as [DolgPoDenovi],

                    case
                     when dogvrc.KlientFizickoPravnoLice = 'F' then 'Физичко лице'
                     when dogvrc.KlientFizickoPravnoLice = 'P' then 'Правно лице'
                    end as [KlientFizickoPravnoLice],

                    dbo.VratiImeEkspozituraPoUsername(dbo.VratiPolisaUsernameIzrabotil(p.Id)) as [Ekspozitura],
                    dbo.VratiPolisaUsernameIzrabotil(p.Id) as [KreiranoOd],
                    dbo.VratiVrabotenSorabotnikIzrabotilPolisa(p.Id) as [TipNaSorabotnik],

                    case
                     when dbo.VratiVrabotenSorabotnikIzrabotilPolisa(p.Id) = 'Вработен' then null
                     when dbo.VratiVrabotenSorabotnikIzrabotilPolisa(p.Id) = 'Соработник' then (dbo.VratiImePrezimePoUsername(dbo.VratiPolisaUsernameIzrabotil(p.Id)))
                     end as [ImePrezimeSorabotnik],

                    dbo.VratiPolisaImePrezimeNadredenNaIzrabotuvac(p.Id) as [ImePrezimeNadreden],

                    case
                     when p.TipNaFaktura = 'Влезна фактура кон брокер' then dbo.VratiPolisaNaplatenaProvizija(p.Id)
                     when p.TipNaFaktura = 'Влезна фактура кон клиент' then dbo.VratiPolisaNaplatenaProvizijaVlezniFakturiKonKlient(p.Id)
                    end as [ProvizijaNaplateno],

                    dbo.VratiPolisaPrenesenaPremijaKonOsiguritel(p.Id) as [PrenesenaPremijaKonOsiguritel],

                    case
                     when p.Storno = 1 then 'Да'
                     else 'Не'
                    end as [Storno],

                    p.Zabeleska

                    from Polisi p
                    left join Klienti osig on p.KlientiIdOsiguritel = osig.id
                    left join KlasiOsiguruvanje kls on p.KlasiOsiguruvanjeIdKlasa = kls.id
                    left join Produkti prod on p.ProduktiIdProizvod = prod.id
                    left join Klienti dogvrc on p.KlientiIdDogovoruvac = dogvrc.id
                    left join Klienti osgrnk on p.KlientiIdOsigurenik = osgrnk.id
                    left join SifrarnikNacinNaPlakanje snnp on p.SifrarnikNacinNaPlakjanjeId = snnp.id

                    where p.DatumNaIzdavanje between @DatumNaIzdavanjeOd and @DatumNaIzdavanjeDo
                    and dbo.VratiPolisaUsernameIzrabotil(p.Id) = @VrabotenSorabotnik" +
                    (Input.KlientiIdOsiguritel.HasValue ? " and p.KlientiIdOsiguritel = @KlientiIdOsiguritel" : "") +
                    (Input.KlasiOsiguruvanjeIdKlasa.HasValue ? " and p.KlasiOsiguruvanjeIdKlasa = @KlasiOsiguruvanjeIdKlasa" : "") +
                    (Input.ProduktiIdProizvod.HasValue ? " and p.ProduktiIdProizvod = @ProduktiIdProizvod" : "") +
                    (!string.IsNullOrEmpty(Input.EkspozituraIme) ? " and dbo.VratiImeEkspozituraPoUsername(dbo.VratiPolisaUsernameIzrabotil(p.Id)) = @EkspozituraIme" : "") +
                    (Input.Storno == "Да" ? " and p.Storno = 1" : Input.Storno == "Не" ? " and p.Storno = 0" : "") + @"
                    order by p.DatumNaIzdavanje desc";

                using (SqlCommand cmd = new SqlCommand(query, connection))
                {
                    cmd.Parameters.AddWithValue("@DatumNaIzdavanjeOd", Input.DatumNaIzdavanjeOd.Value);
                    cmd.Parameters.AddWithValue("@DatumNaIzdavanjeDo", Input.DatumNaIzdavanjeDo.Value);
                    cmd.Parameters.AddWithValue("@VrabotenSorabotnik", Input.VrabotenSorabotnik);

                    if (Input.KlientiIdOsiguritel.HasValue)
                    {
                        cmd.Parameters.AddWithValue("@KlientiIdOsiguritel", Input.KlientiIdOsiguritel.Value);
                    }

                    if (Input.KlasiOsiguruvanjeIdKlasa.HasValue)
                    {
                        cmd.Parameters.AddWithValue("@KlasiOsiguruvanjeIdKlasa", Input.KlasiOsiguruvanjeIdKlasa.Value);
                    }

                    if (Input.ProduktiIdProizvod.HasValue)
                    {
                        cmd.Parameters.AddWithValue("@ProduktiIdProizvod", Input.ProduktiIdProizvod.Value);
                    }

                    if (!string.IsNullOrEmpty(Input.EkspozituraIme))
                    {
                        cmd.Parameters.AddWithValue("@EkspozituraIme", Input.EkspozituraIme);
                    }

                    using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                    {
                        Results = new List<PolisaReportResult>();
                        while (await reader.ReadAsync())
                        {
                            Results.Add(new PolisaReportResult
                            {
                                Id = reader.GetInt64("Id"),
                                BrojNaPolisa = reader.IsDBNull("BrojNaPolisa") ? "" : reader.GetString("BrojNaPolisa"),
                                DatumVaziOd = reader.IsDBNull("DatumVaziOd") ? "" : reader.GetDateTime("DatumVaziOd").ToString("dd.MM.yyyy"),
                                DatumVaziDo = reader.IsDBNull("DatumVaziDo") ? "" : reader.GetDateTime("DatumVaziDo").ToString("dd.MM.yyyy"),
                                Osiguritel = reader.IsDBNull("Osiguritel") ? "" : reader.GetString("Osiguritel"),
                                Klasa = reader.IsDBNull("Klasa") ? "" : reader.GetString("Klasa"),
                                Produkt = reader.IsDBNull("Produkt") ? "" : reader.GetString("Produkt"),
                                Dogovoruvac = reader.IsDBNull("Dogovoruvac") ? "" : reader.GetString("Dogovoruvac"),
                                DogovoruvacAdresa = reader.IsDBNull("DogovoruvacAdresa") ? "" : reader.GetString("DogovoruvacAdresa"),
                                DogovoruvacTelefon = reader.IsDBNull("DogovoruvacTelefon") ? "" : reader.GetString("DogovoruvacTelefon"),
                                Osigurenik = reader.IsDBNull("Osigurenik") ? "" : reader.GetString("Osigurenik"),
                                OsigurenikAdresa = reader.IsDBNull("OsigurenikAdresa") ? "" : reader.GetString("OsigurenikAdresa"),
                                OsigurenikTelefon = reader.IsDBNull("OsigurenikTelefon") ? "" : reader.GetString("OsigurenikTelefon"),
                                NacinNaPlakanje = reader.IsDBNull("NacinNaPlakanje") ? "" : reader.GetString("NacinNaPlakanje"),
                                PremijaPolisirana = reader.IsDBNull("PremijaPolisirana") ? null : reader.GetDecimal("PremijaPolisirana"),
                                PremijaNaplatenaOdDogovoruvac = reader.IsDBNull("PremijaNaplatenaOdDogovoruvac") ? null : reader.GetDecimal("PremijaNaplatenaOdDogovoruvac"),
                                DolgPremijaOdDogovoruvac = reader.IsDBNull("DolgPremijaOdDogovoruvac") ? null : reader.GetDecimal("DolgPremijaOdDogovoruvac"),
                                DolgPoDenovi = reader.IsDBNull("DolgPoDenovi") ? "" : reader.GetString("DolgPoDenovi"),
                                KlientFizickoPravnoLice = reader.IsDBNull("KlientFizickoPravnoLice") ? "" : reader.GetString("KlientFizickoPravnoLice"),
                                Ekspozitura = reader.IsDBNull("Ekspozitura") ? "" : reader.GetString("Ekspozitura"),
                                KreiranoOd = reader.IsDBNull("KreiranoOd") ? "" : reader.GetString("KreiranoOd"),
                                TipNaSorabotnik = reader.IsDBNull("TipNaSorabotnik") ? "" : reader.GetString("TipNaSorabotnik"),
                                ImePrezimeSorabotnik = reader.IsDBNull("ImePrezimeSorabotnik") ? "" : reader.GetString("ImePrezimeSorabotnik"),
                                ImePrezimeNadreden = reader.IsDBNull("ImePrezimeNadreden") ? "" : reader.GetString("ImePrezimeNadreden"),
                                ProvizijaNaplateno = reader.IsDBNull("ProvizijaNaplateno") ? null : reader.GetDecimal("ProvizijaNaplateno"),
                                PrenesenaPremijaKonOsiguritel = reader.IsDBNull("PrenesenaPremijaKonOsiguritel") ? null : reader.GetDecimal("PrenesenaPremijaKonOsiguritel"),
                                Storno = reader.IsDBNull("Storno") ? "" : reader.GetString("Storno"),
                                Zabeleska = reader.IsDBNull("Zabeleska") ? "" : reader.GetString("Zabeleska")
                            });
                        }
                    }
                }
            }
        }

        private async Task<IActionResult> GenerateExcelReport()
        {
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

            using (var package = new ExcelPackage())
            {
                var worksheet = package.Workbook.Worksheets.Add("Извештај полиси ниво на вработен соработник");

                // Set headers
                var headers = new string[]
                {
                    "ID", "Број на полиса", "Важи од", "Важи до", "Осигурител", "Класа", "Продукт",
                    "Договорувач", "Адреса договорувач", "Телефон договорувач", "Осигуреник",
                    "Адреса осигуреник", "Телефон осигуреник", "Начин на плаќање", "Премија полисирана",
                    "Премија наплатена од договорувач", "Долг премија од договорувач", "Долг по денови",
                    "Клиент физичко/правно лице", "Експозитура", "Креирано од", "Тип на соработник",
                    "Име презиме соработник", "Име презиме надреден", "Провизија наплатено",
                    "Пренесена премија кон осигурител", "Сторно", "Забелешка"
                };

                for (int i = 0; i < headers.Length; i++)
                {
                    worksheet.Cells[1, i + 1].Value = headers[i];
                    worksheet.Cells[1, i + 1].Style.Font.Bold = true;
                    worksheet.Cells[1, i + 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
                    worksheet.Cells[1, i + 1].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                }

                // Add data
                for (int row = 0; row < Results.Count; row++)
                {
                    var result = Results[row];
                    int excelRow = row + 2;

                    worksheet.Cells[excelRow, 1].Value = result.Id;
                    worksheet.Cells[excelRow, 2].Value = result.BrojNaPolisa;
                    worksheet.Cells[excelRow, 3].Value = result.DatumVaziOd;
                    worksheet.Cells[excelRow, 4].Value = result.DatumVaziDo;
                    worksheet.Cells[excelRow, 5].Value = result.Osiguritel;
                    worksheet.Cells[excelRow, 6].Value = result.Klasa;
                    worksheet.Cells[excelRow, 7].Value = result.Produkt;
                    worksheet.Cells[excelRow, 8].Value = result.Dogovoruvac;
                    worksheet.Cells[excelRow, 9].Value = result.DogovoruvacAdresa;
                    worksheet.Cells[excelRow, 10].Value = result.DogovoruvacTelefon;
                    worksheet.Cells[excelRow, 11].Value = result.Osigurenik;
                    worksheet.Cells[excelRow, 12].Value = result.OsigurenikAdresa;
                    worksheet.Cells[excelRow, 13].Value = result.OsigurenikTelefon;
                    worksheet.Cells[excelRow, 14].Value = result.NacinNaPlakanje;
                    worksheet.Cells[excelRow, 15].Value = result.PremijaPolisirana;
                    worksheet.Cells[excelRow, 16].Value = result.PremijaNaplatenaOdDogovoruvac;
                    worksheet.Cells[excelRow, 17].Value = result.DolgPremijaOdDogovoruvac;
                    worksheet.Cells[excelRow, 18].Value = result.DolgPoDenovi;
                    worksheet.Cells[excelRow, 19].Value = result.KlientFizickoPravnoLice;
                    worksheet.Cells[excelRow, 20].Value = result.Ekspozitura;
                    worksheet.Cells[excelRow, 21].Value = result.KreiranoOd;
                    worksheet.Cells[excelRow, 22].Value = result.TipNaSorabotnik;
                    worksheet.Cells[excelRow, 23].Value = result.ImePrezimeSorabotnik;
                    worksheet.Cells[excelRow, 24].Value = result.ImePrezimeNadreden;
                    worksheet.Cells[excelRow, 25].Value = result.ProvizijaNaplateno;
                    worksheet.Cells[excelRow, 26].Value = result.PrenesenaPremijaKonOsiguritel;
                    worksheet.Cells[excelRow, 27].Value = result.Storno;
                    worksheet.Cells[excelRow, 28].Value = result.Zabeleska;
                }

                // Auto-fit columns
                worksheet.Cells.AutoFitColumns();

                // Generate file
                var fileName = $"IzvestajPolisiNivoNaVrabotenSorabotnik_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
                var fileBytes = package.GetAsByteArray();

                return File(fileBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
        }
    }
}