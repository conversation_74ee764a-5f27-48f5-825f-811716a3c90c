using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System.ComponentModel.DataAnnotations;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using System.IO;
using Renci.SshNet;
using Renci.SshNet.Sftp;
using System.Text.Json;

namespace NextBroker.Pages.Finansii
{
    public class ListaFakturiKonOsiguritelProvizija : SecurePageModel
    {
        private readonly IConfiguration _configuration;

        public ListaFakturiKonOsiguritelProvizija(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        [BindProperty(SupportsGet = true)]
        public FilterModel Filter { get; set; } = new();
        public List<FakturaModel> Fakturi { get; set; } = new();
        public IEnumerable<SelectListItem> Osiguriteli { get; set; }

        public class FilterModel
        {
            [Display(Name = "Датум на фактура од")]
            public DateTime? DatumNaFakturaOd { get; set; }

            [Display(Name = "Датум на фактура до")]
            public DateTime? DatumNaFakturaDo { get; set; }

            [Display(Name = "Фактура до")]
            public string? FakturaDo { get; set; }
        }

        public class FakturaModel
        {
            public long Id { get; set; }
            public DateTime? Datecreated { get; set; }
            public string? UsernameCreated { get; set; }
            public string? BrojNaFaktura { get; set; }
            public string? FakturaDo { get; set; }
            public DateTime? DatumNaFaktura { get; set; }
            public DateTime? RokNaPlakanje { get; set; }
            public decimal? Iznos { get; set; }
            public DateTime? DatumOd { get; set; }
            public DateTime? DatumDo { get; set; }
            public string? FilePath { get; set; }
            public string? FileName { get; set; }

            // Keep original StavkaPremijaId for backward compatibility
            public long? StavkaPremijaId { get; set; }

            // New properties for concatenated data from multiple connected StavkaPremija records
            public string? StavkaPremijaIds { get; set; }  // Comma-separated IDs
            public string? BrojNaIzvod { get; set; }       // Comma-separated values
            public string? DatumNaIzvod { get; set; }      // Comma-separated values
            public string? Banka { get; set; }             // Comma-separated values
            public string? IznosStavka { get; set; }       // Comma-separated values

            public decimal? Dolg { get; set; }
            public string? Status { get; set; }
            public decimal? IznosOZ { get; set; }
            public string? BrojOZ { get; set; }
            public decimal? IznosOZPoPolisi { get; set; }
            public string? BroeviOZPoPolisi { get; set; }

        }

        public class IzvodPremijaModel
        {
            public long Id { get; set; }
            public string BrojNaIzvod { get; set; }
            public DateTime DatumNaIzvod { get; set; }
            public decimal Priliv { get; set; }
        }

        public class StavkaPremijaModel
        {
            public long Id { get; set; }
            public string CelNaDoznaka { get; set; }
            public decimal Iznos { get; set; }
        }

        public class PovrziFakturaModel
        {
            public long FakturaId { get; set; }
            public long StavkaPremijaId { get; set; }
        }

        private async Task LoadOsiguriteli()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Naziv 
                    FROM Klienti 
                    WHERE Osiguritel = 1 
                    ORDER BY Naziv", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>
                    {
                        new SelectListItem("Сите", "") // Add default "All" option
                    };
                    
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Naziv"].ToString(),
                            reader["Naziv"].ToString()
                        ));
                    }
                    Osiguriteli = items;
                }
            }
        }

        private async Task LoadFakturi()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                var query = @"
                    WITH StavkiAggregated AS (
                        -- Aggregate data from multiple connected StavkaPremija records
                        SELECT
                            ps.FakturiZaProvizijaKonOsiguritelId,
                            STRING_AGG(CAST(ps.StavkaPremijaId AS NVARCHAR(MAX)), ', ') as StavkaPremijaIds,
                            STRING_AGG(dbo.VratiBrojNaIzvodPoStavka(ps.StavkaPremijaId), ', ') as BrojNaIzvod,
                            STRING_AGG(FORMAT(dbo.VratiDatumNaIzvodPoStavka(ps.StavkaPremijaId), 'dd.MM.yyyy'), ', ') as DatumNaIzvod,
                            STRING_AGG(dbo.VratiImeNaBankaPoStavkaOdIzvod(ps.StavkaPremijaId), ', ') as Banka,
                            STRING_AGG(CAST(sp.Iznos AS NVARCHAR(50)), ', ') as IznosStavka,
                            SUM(ISNULL(sp.Iznos, 0)) as TotalIznosStavka
                        FROM FakturiZaProvizijaKonOsiguritelPovrzaniStavki ps
                        LEFT JOIN StavkaPremija sp ON ps.StavkaPremijaId = sp.Id
                        GROUP BY ps.FakturiZaProvizijaKonOsiguritelId
                    )
                    SELECT fzpk.Id, fzpk.Datecreated, fzpk.UsernameCreated, fzpk.BrojNaFaktura,
                           fzpk.FakturaDo, fzpk.DatumNaFaktura, fzpk.RokNaPlakanje,
                           fzpk.Iznos as [Iznos],
                           fzpk.DatumOd, fzpk.DatumDo, fzpk.FilePath, fzpk.FileName,
                           fzpk.StavkaPremijaId, -- Keep for backward compatibility
                           dbo.VraziOZIznosIzleznaFaktura(fzpk.BrojNaFaktura) as [Износ одобрување / задолжување],
                           dbo.VraziOZBrojOZDokumentFakturaIzlezna(fzpk.BrojNaFaktura) as [Број на документ ОЗ],
                           dbo.VraziOZVkupenIznosPoPolisiFakturaProvizija(fzpk.Id) as [Износ одобрување / задолжување по полиси],
                           dbo.VraziOZBroeviNaPolisiPoFakturaProvizija(fzpk.Id) as [ОЗ за провизија по полиси],
                           -- Use aggregated data from multiple StavkaPremija records
                           COALESCE(sa.StavkaPremijaIds, CAST(fzpk.StavkaPremijaId AS NVARCHAR(MAX))) as [StavkaPremijaIds],
                           COALESCE(sa.BrojNaIzvod, dbo.VratiBrojNaIzvodPoStavka(fzpk.StavkaPremijaId)) as [BrojNaIzvod],
                           COALESCE(sa.DatumNaIzvod, FORMAT(dbo.VratiDatumNaIzvodPoStavka(fzpk.StavkaPremijaId), 'dd.MM.yyyy')) as [DatumNaIzvod],
                           COALESCE(sa.Banka, dbo.VratiImeNaBankaPoStavkaOdIzvod(fzpk.StavkaPremijaId)) as [Banka],
                           COALESCE(sa.IznosStavka, FORMAT(sp_legacy.Iznos, 'N2')) as [IznosStavka],
                           (fzpk.Iznos + dbo.VraziOZIznosIzleznaFaktura(fzpk.BrojNaFaktura) + dbo.VraziOZVkupenIznosPoPolisiFakturaProvizija(fzpk.Id)) - COALESCE(sa.TotalIznosStavka, sp_legacy.Iznos, 0) as [Долг],
                           CASE
                           WHEN (fzpk.Iznos + dbo.VraziOZIznosIzleznaFaktura(fzpk.BrojNaFaktura) + dbo.VraziOZVkupenIznosPoPolisiFakturaProvizija(fzpk.Id)) - COALESCE(sa.TotalIznosStavka, sp_legacy.Iznos, 0) = 0 THEN 'Платена'
                           ELSE 'Фактура со долг'
                           END as [Status]
                    FROM FakturiZaProvizijaKonOsiguritel fzpk
                    LEFT JOIN StavkiAggregated sa ON fzpk.Id = sa.FakturiZaProvizijaKonOsiguritelId
                    LEFT JOIN StavkaPremija sp_legacy ON fzpk.StavkaPremijaId = sp_legacy.Id -- For backward compatibility
                    WHERE 1=1";

                if (Filter.DatumNaFakturaOd.HasValue)
                {
                    query += " AND fzpk.DatumNaFaktura >= @DatumOd";
                }
                if (Filter.DatumNaFakturaDo.HasValue)
                {
                    query += " AND fzpk.DatumNaFaktura <= @DatumDo";
                }
                if (!string.IsNullOrEmpty(Filter.FakturaDo))
                {
                    query += " AND fzpk.FakturaDo = @FakturaDo";
                }

                query += " ORDER BY fzpk.Id DESC";

                using (SqlCommand cmd = new SqlCommand(query, connection))
                {
                    if (Filter.DatumNaFakturaOd.HasValue)
                        cmd.Parameters.AddWithValue("@DatumOd", Filter.DatumNaFakturaOd.Value);
                    if (Filter.DatumNaFakturaDo.HasValue)
                        cmd.Parameters.AddWithValue("@DatumDo", Filter.DatumNaFakturaDo.Value);
                    if (!string.IsNullOrEmpty(Filter.FakturaDo))
                        cmd.Parameters.AddWithValue("@FakturaDo", Filter.FakturaDo);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var results = new List<FakturaModel>();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new FakturaModel
                        {
                            Id = reader.GetInt64(reader.GetOrdinal("Id")),
                            Datecreated = reader.IsDBNull(reader.GetOrdinal("Datecreated")) ? null : reader.GetDateTime(reader.GetOrdinal("Datecreated")),
                            UsernameCreated = reader.IsDBNull(reader.GetOrdinal("UsernameCreated")) ? null : reader.GetString(reader.GetOrdinal("UsernameCreated")),
                            BrojNaFaktura = reader.IsDBNull(reader.GetOrdinal("BrojNaFaktura")) ? null : reader.GetString(reader.GetOrdinal("BrojNaFaktura")),
                            FakturaDo = reader.IsDBNull(reader.GetOrdinal("FakturaDo")) ? null : reader.GetString(reader.GetOrdinal("FakturaDo")),
                            DatumNaFaktura = reader.IsDBNull(reader.GetOrdinal("DatumNaFaktura")) ? null : reader.GetDateTime(reader.GetOrdinal("DatumNaFaktura")),
                            RokNaPlakanje = reader.IsDBNull(reader.GetOrdinal("RokNaPlakanje")) ? null : reader.GetDateTime(reader.GetOrdinal("RokNaPlakanje")),
                            IznosOZ = reader.IsDBNull(reader.GetOrdinal("Износ одобрување / задолжување")) ? null : reader.GetDecimal(reader.GetOrdinal("Износ одобрување / задолжување")),
                            BrojOZ = reader.IsDBNull(reader.GetOrdinal("Број на документ ОЗ")) ? null : reader.GetString(reader.GetOrdinal("Број на документ ОЗ")),
                            IznosOZPoPolisi = reader.IsDBNull(reader.GetOrdinal("Износ одобрување / задолжување по полиси")) ? null : reader.GetDecimal(reader.GetOrdinal("Износ одобрување / задолжување по полиси")),
                            BroeviOZPoPolisi = reader.IsDBNull(reader.GetOrdinal("ОЗ за провизија по полиси")) ? null : reader.GetString(reader.GetOrdinal("ОЗ за провизија по полиси")),
                            Iznos = reader.IsDBNull(reader.GetOrdinal("Iznos")) ? null : reader.GetDecimal(reader.GetOrdinal("Iznos")),
                            DatumOd = reader.IsDBNull(reader.GetOrdinal("DatumOd")) ? null : reader.GetDateTime(reader.GetOrdinal("DatumOd")),
                            DatumDo = reader.IsDBNull(reader.GetOrdinal("DatumDo")) ? null : reader.GetDateTime(reader.GetOrdinal("DatumDo")),
                            FilePath = reader.IsDBNull(reader.GetOrdinal("FilePath")) ? null : reader.GetString(reader.GetOrdinal("FilePath")),
                            FileName = reader.IsDBNull(reader.GetOrdinal("FileName")) ? null : reader.GetString(reader.GetOrdinal("FileName")),

                            // Keep backward compatibility
                            StavkaPremijaId = reader.IsDBNull(reader.GetOrdinal("StavkaPremijaId")) ? null : reader.GetInt64(reader.GetOrdinal("StavkaPremijaId")),

                            // New aggregated fields (these are now strings with comma-separated values)
                            StavkaPremijaIds = reader.IsDBNull(reader.GetOrdinal("StavkaPremijaIds")) ? null : reader.GetString(reader.GetOrdinal("StavkaPremijaIds")),
                            BrojNaIzvod = reader.IsDBNull(reader.GetOrdinal("BrojNaIzvod")) ? null : reader.GetString(reader.GetOrdinal("BrojNaIzvod")),
                            DatumNaIzvod = reader.IsDBNull(reader.GetOrdinal("DatumNaIzvod")) ? null : reader.GetString(reader.GetOrdinal("DatumNaIzvod")),
                            Banka = reader.IsDBNull(reader.GetOrdinal("Banka")) ? null : reader.GetString(reader.GetOrdinal("Banka")),
                            IznosStavka = reader.IsDBNull(reader.GetOrdinal("IznosStavka")) ? null : reader.GetString(reader.GetOrdinal("IznosStavka")),

                            Dolg = reader.IsDBNull(reader.GetOrdinal("Долг")) ? null : reader.GetDecimal(reader.GetOrdinal("Долг")),
                            Status = reader.IsDBNull(reader.GetOrdinal("Status")) ? null : reader.GetString(reader.GetOrdinal("Status"))
                        });
                    }
                    Fakturi = results;
                }
            }
        }

        public async Task<IActionResult> OnPostExportExcelAsync()
        {
            if (!await HasPageAccess("ListaFakturiKonOsiguritelProvizija"))
            {
                return RedirectToAccessDenied();
            }

            await LoadOsiguriteli();
            await LoadFakturi();
            
            // Set the license context
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
            
            using (var package = new ExcelPackage())
            {
                var worksheet = package.Workbook.Worksheets.Add("Фактури кон осигурител");
                
                // Add headers
                worksheet.Cells[1, 1].Value = "ID";
                worksheet.Cells[1, 2].Value = "Датум на креирање";
                worksheet.Cells[1, 3].Value = "Креирано од";
                worksheet.Cells[1, 4].Value = "Број на фактура";
                worksheet.Cells[1, 5].Value = "Фактура до";
                worksheet.Cells[1, 6].Value = "Датум на фактура";
                worksheet.Cells[1, 7].Value = "Рок на плаќање";
                worksheet.Cells[1, 8].Value = "Износ";
                worksheet.Cells[1, 9].Value = "Износ одобрување / задолжување по фактура";
                worksheet.Cells[1, 10].Value = "Број на документ ОЗ по фактура";
                worksheet.Cells[1, 11].Value = "Износ одобрување / задолжување по полиси";
                worksheet.Cells[1, 12].Value = "ОЗ за провизија по полиси";
                worksheet.Cells[1, 13].Value = "Датум од";
                worksheet.Cells[1, 14].Value = "Датум до";
                worksheet.Cells[1, 15].Value = "Ставки Премија (ID, Број на извод, Датум на извод, Банка, Износ)";
                worksheet.Cells[1, 16].Value = "Долг";
                worksheet.Cells[1, 17].Value = "Статус";

                // Style headers
                using (var range = worksheet.Cells[1, 1, 1, 17])
                {
                    range.Style.Font.Bold = true;
                    range.Style.Fill.PatternType = ExcelFillStyle.Solid;
                    range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                    range.Style.Border.BorderAround(ExcelBorderStyle.Thin);
                }
                
                // Add data
                int row = 2;
                foreach (var faktura in Fakturi)
                {
                    worksheet.Cells[row, 1].Value = faktura.Id;
                    worksheet.Cells[row, 2].Value = faktura.Datecreated;
                    worksheet.Cells[row, 2].Style.Numberformat.Format = "dd.MM.yyyy HH:mm";
                    worksheet.Cells[row, 3].Value = faktura.UsernameCreated;
                    worksheet.Cells[row, 4].Value = faktura.BrojNaFaktura;
                    worksheet.Cells[row, 5].Value = faktura.FakturaDo;
                    worksheet.Cells[row, 6].Value = faktura.DatumNaFaktura;
                    worksheet.Cells[row, 6].Style.Numberformat.Format = "dd.MM.yyyy";
                    worksheet.Cells[row, 7].Value = faktura.RokNaPlakanje;
                    worksheet.Cells[row, 7].Style.Numberformat.Format = "dd.MM.yyyy";
                    worksheet.Cells[row, 8].Value = faktura.Iznos;
                    worksheet.Cells[row, 8].Style.Numberformat.Format = "#,##0.00";
                    worksheet.Cells[row, 9].Value = faktura.IznosOZ;
                    worksheet.Cells[row, 9].Style.Numberformat.Format = "#,##0.00";
                    worksheet.Cells[row, 10].Value = faktura.BrojOZ;
                    worksheet.Cells[row, 11].Value = faktura.IznosOZPoPolisi;
                    worksheet.Cells[row, 11].Style.Numberformat.Format = "#,##0.00";
                    worksheet.Cells[row, 12].Value = faktura.BroeviOZPoPolisi;
                    worksheet.Cells[row, 13].Value = faktura.DatumOd;
                    worksheet.Cells[row, 13].Style.Numberformat.Format = "dd.MM.yyyy";
                    worksheet.Cells[row, 14].Value = faktura.DatumDo;
                    worksheet.Cells[row, 14].Style.Numberformat.Format = "dd.MM.yyyy";

                    // Format StavkaPremija data in one column, same as frontend
                    var stavkaIds = !string.IsNullOrEmpty(faktura.StavkaPremijaIds) ? faktura.StavkaPremijaIds : faktura.StavkaPremijaId?.ToString();
                    var brojIzvod = faktura.BrojNaIzvod;
                    var datumIzvod = faktura.DatumNaIzvod;
                    var banka = faktura.Banka;
                    var iznosStavka = faktura.IznosStavka;

                    string formattedStavki = "";
                    if (!string.IsNullOrEmpty(stavkaIds))
                    {
                        var ids = stavkaIds.Split(',').Select(x => x.Trim()).ToArray();
                        var brojovi = !string.IsNullOrEmpty(brojIzvod) ? brojIzvod.Split(',').Select(x => x.Trim()).ToArray() : new string[0];
                        var datumi = !string.IsNullOrEmpty(datumIzvod) ? datumIzvod.Split(',').Select(x => x.Trim()).ToArray() : new string[0];
                        var banki = !string.IsNullOrEmpty(banka) ? banka.Split(',').Select(x => x.Trim()).ToArray() : new string[0];
                        var iznosi = !string.IsNullOrEmpty(iznosStavka) ? iznosStavka.Split(',').Select(x => x.Trim()).ToArray() : new string[0];

                        var stavkiList = new List<string>();
                        for (int i = 0; i < ids.Length; i++)
                        {
                            var stavkaInfo = $"ID: {ids[i]}";
                            if (i < brojovi.Length && !string.IsNullOrEmpty(brojovi[i]))
                                stavkaInfo += $", Извод: {brojovi[i]}";
                            if (i < datumi.Length && !string.IsNullOrEmpty(datumi[i]))
                                stavkaInfo += $", Датум: {datumi[i]}";
                            if (i < banki.Length && !string.IsNullOrEmpty(banki[i]))
                                stavkaInfo += $", Банка: {banki[i]}";
                            if (i < iznosi.Length && !string.IsNullOrEmpty(iznosi[i]))
                            {
                                if (decimal.TryParse(iznosi[i], out decimal amount))
                                    stavkaInfo += $", Износ: {amount:N2}";
                                else
                                    stavkaInfo += $", Износ: {iznosi[i]}";
                            }
                            stavkiList.Add(stavkaInfo);
                        }
                        formattedStavki = string.Join("\n", stavkiList);
                    }
                    else
                    {
                        formattedStavki = "Нема поврзани ставки";
                    }

                    worksheet.Cells[row, 15].Value = formattedStavki;
                    worksheet.Cells[row, 16].Value = faktura.Dolg;
                    worksheet.Cells[row, 16].Style.Numberformat.Format = "#,##0.00";
                    worksheet.Cells[row, 17].Value = faktura.Status;
                    row++;
                }
                
                // AutoFit columns
                worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();
                
                // Create file name with date
                string dateStr = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                string fileName = $"FakturiKonOsiguritel_{dateStr}.xlsx";
                
                // Generate the Excel file as byte array (like in ASOMesecenIzvestaj)
                var content = package.GetAsByteArray();
                
                return File(
                    content, 
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", 
                    fileName);
            }
        }

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("ListaFakturiKonOsiguritelProvizija"))
            {
                return RedirectToAccessDenied();
            }

            await LoadOsiguriteli();
            await LoadFakturi();
            return Page();
        }

        public async Task<IActionResult> OnGetSearch()
        {
            if (!await HasPageAccess("ListaFakturiKonOsiguritelProvizija"))
            {
                return RedirectToAccessDenied();
            }

            await LoadOsiguriteli();
            await LoadFakturi();
            return Page();
        }

        public async Task<IActionResult> OnGetDownloadFileAsync(long id)
        {
            if (!await HasPageAccess("ListaFakturiKonOsiguritelProvizija"))
            {
                return RedirectToAccessDenied();
            }

            // Find the invoice in the database
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            string filePath = null;
            string fileName = null;

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                string query = "SELECT FilePath, FileName FROM FakturiZaProvizijaKonOsiguritel WHERE Id = @Id";
                
                using (SqlCommand cmd = new SqlCommand(query, connection))
                {
                    cmd.Parameters.AddWithValue("@Id", id);
                    
                    using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                            filePath = reader.IsDBNull(reader.GetOrdinal("FilePath")) ? null : reader.GetString(reader.GetOrdinal("FilePath"));
                            fileName = reader.IsDBNull(reader.GetOrdinal("FileName")) ? null : reader.GetString(reader.GetOrdinal("FileName"));
                        }
                    }
                }
            }

            if (string.IsNullOrEmpty(filePath) || string.IsNullOrEmpty(fileName))
            {
                return NotFound("Фајлот не е пронајден.");
            }

            try
            {
                // Get SFTP configuration from appsettings.json
                var host = _configuration["SftpConfig:Host"];
                var portStr = _configuration["SftpConfig:Port"];
                var username = _configuration["SftpConfig:Username"];
                var password = _configuration["SftpConfig:Password"];

                if (!int.TryParse(portStr, out int port))
                {
                    port = 22; // Default SFTP port
                }

                byte[] fileData;
                
                // Connect to SFTP server and download the file
                using (var client = new SftpClient(host, port, username, password))
                {
                    client.Connect();
                    
                    using (var memoryStream = new MemoryStream())
                    {
                        client.DownloadFile(filePath, memoryStream);
                        fileData = memoryStream.ToArray();
                    }
                    
                    client.Disconnect();
                }
                
                // Return the file for download
                return File(fileData, "application/octet-stream", fileName);
            }
            catch (Exception ex)
            {
                // Log the error
                Console.WriteLine($"Error downloading file: {ex.Message}");
                return StatusCode(500, "Грешка при превземање на фајлот.");
            }
        }

        public async Task<IActionResult> OnGetSearchIzvodPremijaAsync(string searchTerm)
        {
            if (!await HasPageAccess("ListaFakturiKonOsiguritelProvizija"))
            {
                return new JsonResult(new List<IzvodPremijaModel>());
            }

            var results = new List<IzvodPremijaModel>();
            string connectionString = _configuration.GetConnectionString("DefaultConnection");

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                var query = @"
                    SELECT TOP 50 Id, BrojNaIzvod, DatumNaIzvod, Priliv 
                    FROM IzvodPremija
                    WHERE (BrojNaIzvod LIKE @SearchTerm OR CAST(Id AS NVARCHAR) LIKE @SearchTerm)
                    ORDER BY DatumNaIzvod DESC";

                using (SqlCommand cmd = new SqlCommand(query, connection))
                {
                    cmd.Parameters.AddWithValue("@SearchTerm", "%" + searchTerm + "%");

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new IzvodPremijaModel
                        {
                            Id = reader.GetInt64(reader.GetOrdinal("Id")),
                            BrojNaIzvod = reader.IsDBNull(reader.GetOrdinal("BrojNaIzvod")) ? "" : reader.GetString(reader.GetOrdinal("BrojNaIzvod")),
                            DatumNaIzvod = reader.GetDateTime(reader.GetOrdinal("DatumNaIzvod")),
                            Priliv = reader.GetDecimal(reader.GetOrdinal("Priliv"))
                        });
                    }
                }
            }

            return new JsonResult(results);
        }

        public async Task<IActionResult> OnGetSearchStavkaPremijaAsync(long izvodPremijaId, long? currentFakturaId = null)
        {
            if (!await HasPageAccess("ListaFakturiKonOsiguritelProvizija"))
            {
                return new JsonResult(new List<StavkaPremijaModel>());
            }

            var results = new List<StavkaPremijaModel>();
            string connectionString = _configuration.GetConnectionString("DefaultConnection");

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                // Modified query to exclude only StavkaPremija records that are already connected to the current invoice
                // This allows the same StavkaPremija to be connected to multiple different invoices
                var query = @"
                    SELECT sp.Id, sp.CelNaDoznaka, sp.Iznos
                    FROM StavkaPremija sp
                    WHERE sp.Odliv = 0
                    AND sp.IzvodPremijaId = @IzvodPremijaId
                    AND sp.Id NOT IN (
                        SELECT ps.StavkaPremijaId
                        FROM FakturiZaProvizijaKonOsiguritelPovrzaniStavki ps
                        WHERE ps.FakturiZaProvizijaKonOsiguritelId = @CurrentFakturaId
                        AND ps.StavkaPremijaId IS NOT NULL
                    ) AND (sp.Storno is null or sp.Storno = 0)";

                using (SqlCommand cmd = new SqlCommand(query, connection))
                {
                    cmd.Parameters.AddWithValue("@IzvodPremijaId", izvodPremijaId);
                    cmd.Parameters.AddWithValue("@CurrentFakturaId", currentFakturaId ?? 0);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new StavkaPremijaModel
                        {
                            Id = reader.GetInt64(reader.GetOrdinal("Id")),
                            CelNaDoznaka = reader.IsDBNull(reader.GetOrdinal("CelNaDoznaka")) ? "" : reader.GetString(reader.GetOrdinal("CelNaDoznaka")),
                            Iznos = reader.GetDecimal(reader.GetOrdinal("Iznos"))
                        });
                    }
                }
            }

            return new JsonResult(results);
        }

        [IgnoreAntiforgeryToken]
        public async Task<IActionResult> OnPostPovrziFakturaAsync([FromBody] PovrziFakturaModel model)
        {
            if (!await HasPageAccess("ListaFakturiKonOsiguritelProvizija"))
            {
                return new JsonResult(new { success = false, message = "Немате пристап до оваа функционалност" });
            }

            string connectionString = _configuration.GetConnectionString("DefaultConnection");

            try
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    // Start a transaction to ensure both operations succeed or fail together
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // Check if this connection already exists to prevent duplicates
                            var checkQuery = @"
                                SELECT COUNT(*)
                                FROM FakturiZaProvizijaKonOsiguritelPovrzaniStavki
                                WHERE FakturiZaProvizijaKonOsiguritelId = @FakturaId
                                AND StavkaPremijaId = @StavkaPremijaId";

                            using (SqlCommand checkCmd = new SqlCommand(checkQuery, connection, transaction))
                            {
                                checkCmd.Parameters.AddWithValue("@FakturaId", model.FakturaId);
                                checkCmd.Parameters.AddWithValue("@StavkaPremijaId", model.StavkaPremijaId);

                                int existingCount = (int)await checkCmd.ExecuteScalarAsync();
                                if (existingCount > 0)
                                {
                                    transaction.Rollback();
                                    return new JsonResult(new { success = false, message = "Оваа ставка е веќе поврзана со фактурата" });
                                }
                            }

                            // Insert into the junction table instead of updating the main table
                            var insertQuery = @"
                                INSERT INTO FakturiZaProvizijaKonOsiguritelPovrzaniStavki
                                (FakturiZaProvizijaKonOsiguritelId, StavkaPremijaId, DateCreated)
                                VALUES (@FakturaId, @StavkaPremijaId, GETDATE())";

                            using (SqlCommand insertCmd = new SqlCommand(insertQuery, connection, transaction))
                            {
                                insertCmd.Parameters.AddWithValue("@FakturaId", model.FakturaId);
                                insertCmd.Parameters.AddWithValue("@StavkaPremijaId", model.StavkaPremijaId);

                                int rowsAffected = await insertCmd.ExecuteNonQueryAsync();

                                if (rowsAffected > 0)
                                {
                                    // Note: We no longer automatically set Neraspredelena = 0
                                    // because we want to allow the same StavkaPremija to be connected to multiple invoices
                                    // The Neraspredelena flag should be managed separately based on business logic

                                    // Commit the transaction
                                    transaction.Commit();
                                    return new JsonResult(new { success = true });
                                }
                                else
                                {
                                    transaction.Rollback();
                                    return new JsonResult(new { success = false, message = "Грешка при внесување на поврзувањето" });
                                }
                            }
                        }
                        catch (Exception)
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = "Грешка при поврзување: " + ex.Message });
            }
        }
    }
}
