@page
@model NextBroker.Pages.AdministrationPages.ListaPolisiPrivilegiiModel
@{
    ViewData["Title"] = "Листа на привилегии за полиси";
}

<div class="container-fluid mt-4">
    @Html.AntiForgeryToken()
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>@ViewData["Title"]</h1>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addRuleModal">
            <i class="fas fa-plus"></i> Додај правило
        </button>
    </div>

    @if (!string.IsNullOrEmpty(TempData["SuccessMessage"]?.ToString()))
    {
        <div class="alert alert-success" id="successMessage">
            @TempData["SuccessMessage"]
        </div>
    }

    @if (!string.IsNullOrEmpty(TempData["ErrorMessage"]?.ToString()))
    {
        <div class="alert alert-danger" id="errorMessage">
            @TempData["ErrorMessage"]
        </div>
    }

    <div class="card">
        <div class="card-body">
            <div class="mb-3">
                <input type="text" id="searchBox" class="form-control" placeholder="Пребарувај..." />
            </div>
            <div class="table-responsive">
                <table id="privilegiiTable" class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Креирано на</th>
                            <th>Креирано од</th>
                            <th>Правило за ID</th>
                            <th>Правило за</th>
                            <th>Гледа полиси од ID</th>
                            <th>Гледа полиси од</th>
                            <th>Акции</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var privilegij in Model.Privilegii)
                        {
                            <tr>
                                <td>@privilegij.Id</td>
                                <td>@(privilegij.DateCreated?.ToString("dd.MM.yyyy HH:mm"))</td>
                                <td>@privilegij.UsernameCreated</td>
                                <td>@privilegij.PraviloZa</td>
                                <td>@privilegij.PraviloZaIme</td>
                                <td>@privilegij.GledaPolisiOd</td>
                                <td>@privilegij.GledaPolisiOdIme</td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-danger" onclick="deleteRule(@privilegij.Id)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Add Rule Modal -->
<div class="modal fade" id="addRuleModal" tabindex="-1" aria-labelledby="addRuleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addRuleModalLabel">Додај правило за привилегии</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addRuleForm">
                    <div class="mb-3">
                        <label for="praviloZa" class="form-label">Правило за (корисник кој ќе има привилегија):</label>
                        <select class="form-select" id="praviloZa" name="praviloZa" required>
                            <option value="">Избери корисник...</option>
                            @foreach (var klient in Model.Klienti)
                            {
                                <option value="@klient.Id">@klient.DisplayName</option>
                            }
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="gledaPolisiOd" class="form-label">Гледа полиси од (може да избере повеќе):</label>
                        <div class="border rounded p-3" style="max-height: 300px; overflow-y: auto;">
                            @foreach (var klient in Model.Klienti)
                            {
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="gledaPolisiOd" value="@klient.Id" id="<EMAIL>">
                                    <label class="form-check-label" for="<EMAIL>">
                                        @klient.DisplayName
                                    </label>
                                </div>
                            }
                        </div>
                        <div class="form-text">Изберете еден или повеќе корисници чии полиси ќе може да ги гледа избраниот корисник.</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Откажи</button>
                <button type="button" class="btn btn-primary" onclick="createRules()">Креирај правила</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" />
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.bootstrap5.min.css" />

    <script type="text/javascript" src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.bootstrap5.min.js"></script>
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.html5.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.print.min.js"></script>

    <script>
        $(document).ready(function() {
            // Initialize DataTable
            var table = $('#privilegiiTable').DataTable({
                dom: 'Bfrtip',
                buttons: [
                    {
                        extend: 'excel',
                        text: '<i class="fas fa-file-excel"></i> Excel',
                        className: 'btn btn-success',
                        exportOptions: {
                            columns: ':visible'
                        }
                    },
                    {
                        extend: 'pdf',
                        text: '<i class="fas fa-file-pdf"></i> PDF',
                        className: 'btn btn-danger',
                        exportOptions: {
                            columns: ':visible'
                        }
                    },
                    {
                        extend: 'print',
                        text: '<i class="fas fa-print"></i> Print',
                        className: 'btn btn-info',
                        exportOptions: {
                            columns: ':visible'
                        }
                    }
                ],
                pageLength: 25,
                order: [[0, 'desc']],
                language: {
                    url: '/lib/datatables/macedonian.json'
                }
            });

            // Handle search box
            $('#searchBox').on('keyup', function() {
                table.search(this.value).draw();
            });

            // Handle success/error message fade out
            setTimeout(function() {
                $("#successMessage, #errorMessage").fadeOut('slow');
            }, 5000);
        });

        function createRules() {
            const praviloZa = document.getElementById('praviloZa').value;
            const gledaPolisiOdCheckboxes = document.querySelectorAll('input[name="gledaPolisiOd"]:checked');

            if (!praviloZa) {
                alert('Мора да изберете корисник за кој се применува правилото.');
                return;
            }

            if (gledaPolisiOdCheckboxes.length === 0) {
                alert('Мора да изберете најмалку еден корисник чии полиси ќе може да ги гледа.');
                return;
            }

            const gledaPolisiOd = Array.from(gledaPolisiOdCheckboxes).map(cb => cb.value);

            // Show loading state
            const createButton = document.querySelector('#addRuleModal .btn-primary');
            const originalText = createButton.innerHTML;
            createButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Креирам...';
            createButton.disabled = true;

            // Prepare form data
            const formData = new FormData();
            formData.append('praviloZa', praviloZa);
            gledaPolisiOd.forEach(id => formData.append('gledaPolisiOd', id));
            formData.append('__RequestVerificationToken', $('input[name="__RequestVerificationToken"]').val());

            fetch('?handler=Create', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message and reload page
                    alert(data.message);
                    location.reload();
                } else {
                    alert('Грешка: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Настана грешка при креирање на правилата.');
            })
            .finally(() => {
                // Restore button state
                createButton.innerHTML = originalText;
                createButton.disabled = false;
            });
        }

        function deleteRule(id) {
            if (!confirm('Дали сте сигурни дека сакате да го избришете ова правило?')) {
                return;
            }

            const formData = new FormData();
            formData.append('id', id);
            formData.append('__RequestVerificationToken', $('input[name="__RequestVerificationToken"]').val());

            fetch('?handler=Delete', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    location.reload();
                } else {
                    alert('Грешка: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Настана грешка при бришење на правилото.');
            });
        }
    </script>
}