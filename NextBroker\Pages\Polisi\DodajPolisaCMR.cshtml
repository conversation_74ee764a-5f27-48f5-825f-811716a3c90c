@page
@model NextBroker.Pages.Polisi.DodajPolisaCMRModel
@{
    ViewData["Title"] = "Додај полиса ЦМР";
}

<h1 style='text-align: center;'>@ViewData["Title"]</h1>

<div class="container-fluid mt-4 px-4">
    <!-- Success Message -->
    <div id="successMessage" class="alert alert-success alert-dismissible fade" role="alert" style="display: none;">
        <strong>Успешно!</strong> Полисата е успешно зачувана.
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>

    <form method="post" id="polisaForm" class="dodaj-polisa-form">
        @Html.AntiForgeryToken()
        
        <!-- Add validation summary -->
        <div asp-validation-summary="All" class="text-danger"></div>

        <!-- Basic Information Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Основни информации</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Осигурител</label>
                        <select asp-for="Input.KlientiIdOsiguritel" 
                                asp-items="Model.Osiguriteli" 
                                class="form-select">
                            <option value="">-- Избери осигурител --</option>
                        </select>
                        <span asp-validation-for="Input.KlientiIdOsiguritel" class="text-danger"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Класа на осигурување</label>
                        <select asp-for="Input.KlasiOsiguruvanjeIdKlasa" 
                                asp-items="Model.KlasiOsiguruvanje" 
                                class="form-select">
                            <option value="">-- Избери класа --</option>
                        </select>
                        <span asp-validation-for="Input.KlasiOsiguruvanjeIdKlasa" class="text-danger"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Продукт</label>
                        <select asp-for="Input.ProduktiIdProizvod" 
                                asp-items="Model.Produkti" 
                                class="form-select">
                            <option value="">-- Избери продукт --</option>
                        </select>
                        <span asp-validation-for="Input.ProduktiIdProizvod" class="text-danger"></span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.BrojNaPolisa" class="form-label">Број на полиса</label>
                        <input asp-for="Input.BrojNaPolisa" class="form-control" type="text" required />
                        <span asp-validation-for="Input.BrojNaPolisa" class="text-danger"></span>
                    </div>
                    <!-- Commented out Broj na Ponuda
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.BrojNaPonuda" class="form-label">Број на понуда</label>
                        <input asp-for="Input.BrojNaPonuda" class="form-control" type="number" />
                        <span asp-validation-for="Input.BrojNaPonuda" class="text-danger"></span>
                    </div>
                    -->
                </div>
                <div class="row">
                    <div class="col-md-10 mb-3">
                        <label class="form-label">Договорувач (пребарувај по МБ, ЕДБ, ЕМБГ, име или назив)</label>
                        <div class="input-group">
                            <button type="button" class="btn btn-outline-secondary btn-sm clear-field" 
                                    data-target="dogovoruvac" 
                                    style="width: 30px; height: 30px; padding: 0; margin-right: 5px;">
                                <i class="fas fa-eraser" style="font-size: 0.7rem;"></i>
                            </button>
                            <input type="text" id="dogovoruvacMBSearch" class="form-control" 
                                   autocomplete="off"
                                   placeholder="Пребарувај по МБ, ЕДБ, ЕМБГ, име/назив..." />
                            <input type="hidden" asp-for="Input.KlientiIdDogovoruvac" id="KlientiIdDogovoruvac" />
                        </div>
                        <div id="dogovoruvacSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1" 
                             style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-10 mb-3">
                        <label class="form-label">Осигуреник (пребарувај по МБ, ЕДБ, ЕМБГ, име или назив) <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <button type="button" class="btn btn-outline-secondary btn-sm clear-field" 
                                    data-target="osigurenik" 
                                    style="width: 30px; height: 30px; padding: 0; margin-right: 5px;">
                                <i class="fas fa-eraser" style="font-size: 0.7rem;"></i>
                            </button>
                            <input type="text" id="osigurenikMBSearch" class="form-control" 
                                   autocomplete="off"
                                   placeholder="Пребарувај по МБ, ЕДБ, ЕМБГ, име/назив..."
                                   required />
                            <input type="hidden" asp-for="Input.KlientiIdOsigurenik" id="KlientiIdOsigurenik" required />
                        </div>
                        <div id="osigurenikSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1" 
                             style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                        </div>
                        <span asp-validation-for="Input.KlientiIdOsigurenik" class="text-danger"></span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-10 mb-3">
                        <label class="form-label">Соработник/вработен (пребарувај по МБ, ЕДБ, ЕМБГ, име или назив)</label>
                        <div class="input-group">
                            <button type="button" class="btn btn-outline-secondary btn-sm clear-field" 
                                    data-target="sorabotnik" 
                                    style="width: 30px; height: 30px; padding: 0; margin-right: 5px;">
                                <i class="fas fa-eraser" style="font-size: 0.7rem;"></i>
                            </button>
                            <input type="text" id="sorabotnikMBSearch" class="form-control" 
                                   autocomplete="off"
                                   placeholder="Пребарувај по МБ, ЕДБ, ЕМБГ, име/назив..." />
                            <input type="hidden" asp-for="Input.KlientiIdSorabotnik" id="KlientiIdSorabotnik" />
                        </div>
                        <div id="sorabotnikSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1" 
                             style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <!-- Commented out Kolektivna checkboxes
                        <div class="form-check mb-2">
                            <input type="checkbox" class="form-check-input" asp-for="Input.Kolektivna" id="kolektivna">
                            <label class="form-check-label" asp-for="Input.Kolektivna">Колективна</label>
                        </div>
                        <div class="form-check mb-3">
                            <input type="checkbox" class="form-check-input" asp-for="Input.KolektivnaNeodredenBrOsigurenici" 
                                   id="kolektivnaNeodredena">
                            <label class="form-check-label" asp-for="Input.KolektivnaNeodredenBrOsigurenici">
                                Колективна со неодреден број на осигуреници
                            </label>
                        </div>
                        -->
                        <!-- Keep existing checkboxes -->
                        <div class="form-check mb-3">
                            <input type="checkbox" class="form-check-input" asp-for="Input.Faktoring" id="faktoring">
                            <label class="form-check-label" asp-for="Input.Faktoring">Факторинг</label>
                        </div>
                        <div class="form-check mb-3" style="display: none;">
                            <input type="checkbox" class="form-check-input" asp-for="Input.GeneriranaFakturaIzlezna" id="generiranaFakturaIzlezna">
                            <label class="form-check-label" asp-for="Input.GeneriranaFakturaIzlezna">Генерирана излезна фактура</label>
                        </div>
                        <!-- Commented out Сторно field
                        <div class="form-check mb-3">
                            <input type="checkbox" class="form-check-input" asp-for="Input.Storno" id="storno">
                            <label class="form-check-label" asp-for="Input.Storno">Сторно</label>
                        </div>
                        <div id="pricinaZaStornoContainer" class="mb-3" style="display: none;">
                            <label asp-for="Input.SifrarnikPricinaZaStornoId" class="form-label">Причина за сторно</label>
                            <select asp-for="Input.SifrarnikPricinaZaStornoId" 
                                    asp-items="Model.PriciniZaStorno" 
                                    class="form-select">
                                <option value="">-- Избери причина за сторно --</option>
                            </select>
                            <span asp-validation-for="Input.SifrarnikPricinaZaStornoId" class="text-danger"></span>
                        </div>
                        -->
                        <!-- Add hidden fields to ensure values are still submitted -->
                        <div style="display: none;">
                            <input asp-for="Input.Storno" type="hidden" value="false" />
                            <input asp-for="Input.SifrarnikPricinaZaStornoId" type="hidden" />
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.DatumVaziOd" class="form-label"></label>
                        <input asp-for="Input.DatumVaziOd" class="form-control datepicker" type="date" />
                        <span asp-validation-for="Input.DatumVaziOd" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.DatumVaziDo" class="form-label"></label>
                        <input asp-for="Input.DatumVaziDo" class="form-control datepicker" type="date" />
                        <span asp-validation-for="Input.DatumVaziDo" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.DatumNaIzdavanje" class="form-label"></label>
                        <input asp-for="Input.DatumNaIzdavanje" class="form-control datepicker" type="date" />
                        <span asp-validation-for="Input.DatumNaIzdavanje" class="text-danger"></span>
                    </div>
                </div>
                <!-- Commented out Vremetraenje and Period fields
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.VremetraenjeNaPolisa" class="form-label"></label>
                        <input asp-for="Input.VremetraenjeNaPolisa" class="form-control" type="number" min="0" />
                        <span asp-validation-for="Input.VremetraenjeNaPolisa" class="text-danger"></span>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.PeriodNaUplata" class="form-label"></label>
                        <input asp-for="Input.PeriodNaUplata" class="form-control" type="number" min="0" />
                        <span asp-validation-for="Input.PeriodNaUplata" class="text-danger"></span>
                    </div>
                </div>
                -->
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.SifrarnikValutiIdValuta" class="form-label">Валута</label>
                        <select asp-for="Input.SifrarnikValutiIdValuta" 
                                asp-items="Model.Valuti" 
                                class="form-select">
                            <option value="">-- Избери валута --</option>
                        </select>
                        <span asp-validation-for="Input.SifrarnikValutiIdValuta" class="text-danger"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label asp-for="Input.SifrarnikNacinNaPlakjanjeId" class="form-label">Начин на плаќање</label>
                        <select asp-for="Input.SifrarnikNacinNaPlakjanjeId" 
                                asp-items="Model.NaciniNaPlakanje" 
                                class="form-select">
                            <option value="">-- Избери начин на плаќање --</option>
                        </select>
                        <span asp-validation-for="Input.SifrarnikNacinNaPlakjanjeId" class="text-danger"></span>
                    </div>
                </div>
               
                 
                    <!-- Commented out Тип на плаќање field
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.SifrarnikTipNaPlakanjeId" class="form-label">Тип на плаќање</label>
                        <select asp-for="Input.SifrarnikTipNaPlakanjeId" 
                                asp-items="Model.TipoviNaPlakanje" 
                                class="form-select">
                            <option value="">-- Избери тип на плаќање --</option>
                        </select>
                        <span asp-validation-for="Input.SifrarnikTipNaPlakanjeId" class="text-danger"></span>
                    </div>
                    -->
                    <!-- Commented out Банка field
                    <div class="col-md-4 mb-3">
                        <label asp-for="Input.SifrarnikBankiIdBanka" class="form-label">Банка</label>
                        <select asp-for="Input.SifrarnikBankiIdBanka" 
                                asp-items="Model.Banki" 
                                class="form-select">
                            <option value="">-- Избери банка --</option>
                        </select>
                        <span asp-validation-for="Input.SifrarnikBankiIdBanka" class="text-danger"></span>
                    </div>
                    -->
                    <!-- Add hidden fields to ensure values are still submitted -->
                    <div style="display: none;">
                        <input asp-for="Input.SifrarnikTipNaPlakanjeId" type="hidden" />
                        <input asp-for="Input.SifrarnikBankiIdBanka" type="hidden" />
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label asp-for="Input.TipNaFaktura" class="form-label">Тип на фактура</label>
                        <select asp-for="Input.TipNaFaktura" 
                                asp-items="Model.TipoviNaFaktura" 
                                class="form-select"
                                required>
                            <option value="">-- Избери тип на фактура --</option>
                        </select>
                        <span asp-validation-for="Input.TipNaFaktura" class="text-danger"></span>
                    </div>
                    <div class="col-md-4 mb-3" style="display: none;">
                        <label asp-for="Input.BrojNaFakturaVlezna" class="form-label"></label>
                        <input asp-for="Input.BrojNaFakturaVlezna" class="form-control" type="text" />
                        <span asp-validation-for="Input.BrojNaFakturaVlezna" class="text-danger"></span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 mb-3" style="display: none;">
                        <label asp-for="Input.BrojNaFakturaIzlezna" class="form-label"></label>
                        <input asp-for="Input.BrojNaFakturaIzlezna" class="form-control" type="text" />
                        <span asp-validation-for="Input.BrojNaFakturaIzlezna" class="text-danger"></span>
                    </div>
                    <div class="col-md-4 mb-3" style="display: none;">
                        <label asp-for="Input.DatumNaFakturaVlezna" class="form-label"></label>
                        <input asp-for="Input.DatumNaFakturaVlezna" class="form-control datepicker" type="date" />
                        <span asp-validation-for="Input.DatumNaFakturaVlezna" class="text-danger"></span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 mb-3" style="display: none;">
                        <label asp-for="Input.DatumNaIzleznaFaktura" class="form-label"></label>
                        <input asp-for="Input.DatumNaIzleznaFaktura" class="form-control datepicker" type="date" />
                        <span asp-validation-for="Input.DatumNaIzleznaFaktura" class="text-danger"></span>
                    </div>
                    <div class="col-md-4 mb-3" style="display: none;">
                        <label asp-for="Input.RokNaPlakjanjeFakturaIzlezna" class="form-label"></label>
                        <input asp-for="Input.RokNaPlakjanjeFakturaIzlezna" class="form-control datepicker" type="date" />
                        <span asp-validation-for="Input.RokNaPlakjanjeFakturaIzlezna" class="text-danger"></span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 mb-3" style="display: none;">
                        <label asp-for="Input.RokNaPlakjanjeFakturaVlezna" class="form-label"></label>
                        <input asp-for="Input.RokNaPlakjanjeFakturaVlezna" class="form-control datepicker" type="date" />
                        <span asp-validation-for="Input.RokNaPlakjanjeFakturaVlezna" class="text-danger"></span>
                    </div>
                    <div style="display: none;">
                        <!--
                        <input asp-for="Input.ProcentNaPopustZaFakturaVoRok" type="hidden" />
                        <input asp-for="Input.IznosZaPlakjanjeVoRok" type="hidden" />
                        <input asp-for="Input.ProcentKomercijalenPopust" type="hidden" />
                        <input asp-for="Input.ProcentFinansiski" type="hidden" />                        
                        <input asp-for="Input.PremijaZaNaplata" type="hidden" />
-->
                        <input asp-for="Input.Uplateno" type="hidden" />
                        <input asp-for="Input.DolznaPremija" type="hidden" />
                    </div>
                </div>
                <div class="row">
                <!--
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.ProcentKomercijalenPopust" class="form-label"></label>
                        <div class="input-group">
                            <input asp-for="Input.ProcentKomercijalenPopust" class="form-control decimal-input" type="number" step="0.0001" min="0" max="100" />
                            <span class="input-group-text">%</span>
                        </div>
                        <span asp-validation-for="Input.ProcentKomercijalenPopust" class="text-danger"></span>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label asp-for="Input.ProcentFinansiski" class="form-label"></label>
                        <div class="input-group">
                            <input asp-for="Input.ProcentFinansiski" class="form-control decimal-input" type="number" step="0.0001" min="0" max="100" />
                            <span class="input-group-text">%</span>
                        </div>
                        <span asp-validation-for="Input.ProcentFinansiski" class="text-danger"></span>
                    </div>
                </div>
                <div class="row">
                -->
<!--
                    <div class="col-md-3 mb-3">
                      <label asp-for="Input.KoregiranaStapkaNaProvizija" class="form-label"></label> 
                        <div class="input-group">
                            <input asp-for="Input.KoregiranaStapkaNaProvizija" class="form-control decimal-input" type="number" step="0.0001" min="0" max="100" />
                            <span class="input-group-text">%</span>
                        </div>
                        <span asp-validation-for="Input.KoregiranaStapkaNaProvizija" class="text-danger"></span>
                    </div>

                    <div class="col-md-4 mb-3">
                        <label asp-for="Input.PremijaZaNaplata" class="form-label"></label>
                        <input asp-for="Input.PremijaZaNaplata" class="form-control decimal-input" type="number" step="0.0001" />
                        <span asp-validation-for="Input.PremijaZaNaplata" class="text-danger"></span>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.Uplateno" class="form-label"></label>
                        <input asp-for="Input.Uplateno" class="form-control decimal-input" type="number" step="0.0001" />
                        <span asp-validation-for="Input.Uplateno" class="text-danger"></span>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label asp-for="Input.DolznaPremija" class="form-label"></label>
                        <input asp-for="Input.DolznaPremija" class="form-control decimal-input" type="number" step="0.0001" />
                        <span asp-validation-for="Input.DolznaPremija" class="text-danger"></span>
                    </div>
                </div>
-->
            </div>
        </div>
        <!-- Financial Information -->
             <div class="row">
                    <div class="col-md-3 mb-3">
                        <label asp-for="Input.SifrarnikValutiIdFranshizaValuta" class="form-label">Валута за франшиза</label>
                        <select asp-for="Input.SifrarnikValutiIdFranshizaValuta" 
                                asp-items="Model.Valuti" 
                                class="form-select">
                            <option value="">-- Изберете валута --</option>
                        </select>
                        <span asp-validation-for="Input.SifrarnikValutiIdFranshizaValuta" class="text-danger"></span>
                    </div>

        <div class="row">
            <div class="col-md-3 mb-3 ms-4">
                <label asp-for="Input.ProcentFranshiza" class="form-label"></label>
                <div class="input-group">
                    <input asp-for="Input.ProcentFranshiza" 
                           class="form-control decimal-input" 
                           type="number" 
                           step="0.01" 
                           min="0" 
                           max="100"
                           onchange="console.log('ProcentFranshiza changed:', this.value)" />
                    <span class="input-group-text">%</span>
                </div>
                <span asp-validation-for="Input.ProcentFranshiza" class="text-danger"></span>
            </div>

            <div class="col-md-3 mb-3">
                <label asp-for="Input.FranshizaIznos" class="form-label"></label>
                <div class="input-group">
                    <input asp-for="Input.FranshizaIznos" 
                           class="form-control decimal-input" 
                           type="number" 
                           step="0.0001" />
                </div>
                <span asp-validation-for="Input.FranshizaIznos" class="text-danger"></span>
            </div>
             <!--
            <div class="col-md-3 mb-3">
                <label asp-for="Input.ProcentNaPopustZaFakturaVoRok" class="form-label"></label>
                <div class="input-group">
                    <input asp-for="Input.ProcentNaPopustZaFakturaVoRok" class="form-control decimal-input" type="number" step="0.0001" min="0" max="100" />
                    <span class="input-group-text">%</span>
                </div>
                <span asp-validation-for="Input.ProcentNaPopustZaFakturaVoRok" class="text-danger"></span>
            </div>
            
            <div class="col-md-3 mb-3">
                <label asp-for="Input.IznosZaPlakjanjeVoRok" class="form-label"></label>
                <input asp-for="Input.IznosZaPlakjanjeVoRok" class="form-control decimal-input" type="number" step="0.0001" />
                <span asp-validation-for="Input.IznosZaPlakjanjeVoRok" class="text-danger"></span>
            </div>
            -->
        </div>
        <div class="row">
         <!--
            <div class="col-md-3 mb-3">
                <label asp-for="Input.ProcentKomercijalenPopust" class="form-label"></label>
                <div class="input-group">
                    <input asp-for="Input.ProcentKomercijalenPopust" class="form-control decimal-input" type="number" step="0.0001" min="0" max="100" />
                    <span class="input-group-text">%</span>
                </div>
                <span asp-validation-for="Input.ProcentKomercijalenPopust" class="text-danger"></span>
            </div>
            <div class="col-md-3 mb-3">
                <label asp-for="Input.ProcentFinansiski" class="form-label"></label>
                <div class="input-group">
                    <input asp-for="Input.ProcentFinansiski" class="form-control decimal-input" type="number" step="0.0001" min="0" max="100" />
                    <span class="input-group-text">%</span>
                </div>
                <span asp-validation-for="Input.ProcentFinansiski" class="text-danger"></span>
            </div>
            -->
            <div class="col-md-3 mb-3 ms-4">
                <label asp-for="Input.KoregiranaStapkaNaProvizija" class="form-label"></label>
                <div class="input-group">
                    <input asp-for="Input.KoregiranaStapkaNaProvizija" 
                           class="form-control decimal-input" 
                           type="number" 
                           step="0.0001" 
                           min="0" 
                           max="100"
                           onchange="console.log('KoregiranaStapkaNaProvizija changed:', this.value)" />
                    <span class="input-group-text">%</span>
                </div>
                <span asp-validation-for="Input.KoregiranaStapkaNaProvizija" class="text-danger"></span>
            </div>


        </div>
         <!--
        <div class="row">
            <div class="col-md-3 mb-3">
                <label asp-for="Input.PremijaZaNaplata" class="form-label"></label>
                <input asp-for="Input.PremijaZaNaplata" class="form-control decimal-input" type="number" step="0.0001" />
                <span asp-validation-for="Input.PremijaZaNaplata" class="text-danger"></span>
            </div>
            <div class="col-md-3 mb-3">
                <label asp-for="Input.Uplateno" class="form-label"></label>
                <input asp-for="Input.Uplateno" class="form-control decimal-input" type="number" step="0.0001" />
                <span asp-validation-for="Input.Uplateno" class="text-danger"></span>
            </div>
            <div class="col-md-3 mb-3">
                <label asp-for="Input.DolznaPremija" class="form-label"></label>
                <input asp-for="Input.DolznaPremija" class="form-control decimal-input" type="number" step="0.0001" />
                <span asp-validation-for="Input.DolznaPremija" class="text-danger"></span>
            </div>
        </div>
        -->
        <!-- Auto Odgovornost -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center" role="button" data-bs-toggle="collapse" data-bs-target="#cmrCollapse" aria-expanded="false" aria-controls="cmrCollapse">
                <h5 class="mb-0">ЦМР</h5>
                <i class="fas fa-chevron-down"></i>
            </div>
            <div id="cmrCollapse" class="collapse">
                <div class="card-body">
                    <div class="row">

                        <div class="col-md-3 mb-3">
                            <label asp-for="Input.TeritorijalnoPokritie" class="form-label"></label>
                            <input asp-for="Input.TeritorijalnoPokritie" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.TeritorijalnoPokritie" class="text-danger"></span>
                        </div>

                        <div class="col-md-3 mb-3">
                            <label asp-for="Input.PredmetNaOsiguruvanje" class="form-label"></label>
                            <input asp-for="Input.PredmetNaOsiguruvanje" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.PredmetNaOsiguruvanje" class="text-danger"></span>
                        </div>


                        <div class="col-md-3 mb-3">
                            <label asp-for="Input.TipNaSoobrakjaj" class="form-label"></label>
                            <input asp-for="Input.TipNaSoobrakjaj" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.TipNaSoobrakjaj" class="text-danger"></span>
                        </div>

                        <div class="col-md-3 mb-3">
                            <label asp-for="Input.OsigurenaVrednost" class="form-label"></label>
                            <input asp-for="Input.OsigurenaVrednost" class="form-control decimal-input" type="number" step="0.0001" />
                            <span asp-validation-for="Input.OsigurenaVrednost" class="text-danger"></span>
                        </div>

                        <div class="col-md-3 mb-3">
                            <label asp-for="Input.Premija" class="form-label"></label>
                            <input asp-for="Input.Premija" class="form-control decimal-input" type="number" step="0.0001" />
                            <span asp-validation-for="Input.Premija" class="text-danger"></span>
                        </div>

                        <div class="col-md-3 mb-3">
                            <label asp-for="Input.Opis" class="form-label"></label>
                            <input asp-for="Input.Opis" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.Opis" class="text-danger"></span>
                        </div>

                    
                    </div>
                    <div class="row">

 <!--
                        <div class="col-md-3 mb-3">
                            <label asp-for="Input.OsnovnaPremija" class="form-label"></label>
                            <input asp-for="Input.OsnovnaPremija" class="form-control decimal-input" type="number" step="0.0001" onchange="calculateVkupnaPremija()" />
                            <span asp-validation-for="Input.OsnovnaPremija" class="text-danger"></span>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label asp-for="Input.DopolnitelnaPremija" class="form-label"></label>
                            <input asp-for="Input.DopolnitelnaPremija" class="form-control decimal-input" type="number" step="0.0001" onchange="calculateVkupnaPremija()" />
                            <span asp-validation-for="Input.DopolnitelnaPremija" class="text-danger"></span>
                        </div>

-->



                        <div class="col-md-3 mb-3">
                            <label asp-for="Input.VkupnaPremija" class="form-label"></label>
                            <input asp-for="Input.VkupnaPremija" class="form-control decimal-input" type="number" step="0.0001" />
                            <span asp-validation-for="Input.VkupnaPremija" class="text-danger"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Popusti Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Попусти</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label asp-for="Input.ProcentNaPopustZaFakturaVoRok" class="form-label"></label>
                        <input asp-for="Input.ProcentNaPopustZaFakturaVoRok" class="form-control" type="number" step="0.01" />
                        <span asp-validation-for="Input.ProcentNaPopustZaFakturaVoRok" class="text-danger"></span>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label asp-for="Input.IznosZaPlakjanjeVoRok" class="form-label"></label>
                        <input asp-for="Input.IznosZaPlakjanjeVoRok" class="form-control" type="number" step="0.0001" />
                        <span asp-validation-for="Input.IznosZaPlakjanjeVoRok" class="text-danger"></span>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label asp-for="Input.ProcentKomercijalenPopust" class="form-label"></label>
                        <input asp-for="Input.ProcentKomercijalenPopust" class="form-control" type="number" step="0.01" />
                        <span asp-validation-for="Input.ProcentKomercijalenPopust" class="text-danger"></span>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label asp-for="Input.ProcentFinansiski" class="form-label"></label>
                        <input asp-for="Input.ProcentFinansiski" class="form-control" type="number" step="0.01" />
                        <span asp-validation-for="Input.ProcentFinansiski" class="text-danger"></span>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label asp-for="Input.PremijaZaNaplata" class="form-label"></label>
                        <input asp-for="Input.PremijaZaNaplata" class="form-control" type="number" step="0.0001" />
                        <span asp-validation-for="Input.PremijaZaNaplata" class="text-danger"></span>
                    </div>
                </div>
            </div>
        </div>
        <!-- Soobrakjajna -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center" role="button" data-bs-toggle="collapse" data-bs-target="#soobrakjajnaCollapse" aria-expanded="false" aria-controls="soobrakjajnaCollapse">
                <h5 class="mb-0">Сообраќајна</h5>
                <i class="fas fa-chevron-down"></i>
            </div>
            <div id="soobrakjajnaCollapse" class="collapse">
                <div class="card-body">

<h5 class="mb-3">Група 1 - Основни податоци</h5>

                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label asp-for="Input.SifrarnikTipNaVoziloId" class="form-label">Тип на возило</label>
                            <select asp-for="Input.SifrarnikTipNaVoziloId" 
                                    asp-items="Model.TipoviNaVozilo" 
                                    class="form-select">
                                <option value="">-- Избери тип на возило --</option>
                            </select>
                            <span asp-validation-for="Input.SifrarnikTipNaVoziloId" class="text-danger"></span>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label asp-for="Input.RegisterskaOznaka" class="form-label">Регистарска ознака</label>
                            <input asp-for="Input.RegisterskaOznaka" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.RegisterskaOznaka" class="text-danger"></span>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label asp-for="Input.Marka" class="form-label">Марка</label>
                            <div class="mb-2">
                                <div class="form-check form-check-inline">
                                    <input type="radio" class="form-check-input" name="brandInputType" id="brandDropdown" value="dropdown" checked />
                                    <label class="form-check-label" for="brandDropdown">Избери од листа</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input type="radio" class="form-check-input" name="brandInputType" id="brandCustom" value="custom" />
                                    <label class="form-check-label" for="brandCustom">Внеси сопствена</label>
                                </div>
                            </div>
                            <select id="carBrandDropdown" asp-items="Model.CarBrands" class="form-select mb-2" onchange="handleBrandSelection()">
                                <option value="">-- Избери марка --</option>
                            </select>
                            <input asp-for="Input.Marka" id="carBrandTextInput" class="form-control" maxlength="255" style="display: none;" />
                            <input asp-for="Input.SelectedCarBrandId" type="hidden" />
                            <input asp-for="Input.UseCustomCarBrand" type="hidden" />
                            <span asp-validation-for="Input.Marka" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.KomercijalnaOznaka" class="form-label">Комерцијална ознака</label>
                            <div class="mb-2">
                                <div class="form-check form-check-inline">
                                    <input type="radio" class="form-check-input" name="modelInputType" id="modelDropdown" value="dropdown" checked />
                                    <label class="form-check-label" for="modelDropdown">Избери од листа</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input type="radio" class="form-check-input" name="modelInputType" id="modelCustom" value="custom" />
                                    <label class="form-check-label" for="modelCustom">Внеси сопствена</label>
                                </div>
                            </div>
                            <select id="carModelDropdown" asp-items="Model.CarModels" class="form-select mb-2" onchange="handleModelSelection()">
                                <option value="">-- Прво избери марка --</option>
                            </select>
                            <input asp-for="Input.KomercijalnaOznaka" id="carModelTextInput" class="form-control" maxlength="255" style="display: none;" />
                            <input asp-for="Input.UseCustomCarModel" type="hidden" />
                            <span asp-validation-for="Input.KomercijalnaOznaka" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.Shasija" class="form-label">Број на шасија</label>
                            <input asp-for="Input.Shasija" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.Shasija" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label asp-for="Input.GodinaNaProizvodstvo" class="form-label">Година на производство</label>
                            <input asp-for="Input.GodinaNaProizvodstvo" class="form-control" type="number" min="1900" max="2100" />
                            <span asp-validation-for="Input.GodinaNaProizvodstvo" class="text-danger"></span>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label asp-for="Input.ZafatninaNaMotorotcm3" class="form-label">Зафатнина на моторот (cm³)</label>
                            <input asp-for="Input.ZafatninaNaMotorotcm3" class="form-control" type="number" min="0" />
                            <span asp-validation-for="Input.ZafatninaNaMotorotcm3" class="text-danger"></span>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label asp-for="Input.SilinaNaMotorotKW" class="form-label">Силина на моторот (kW)</label>
                            <input asp-for="Input.SilinaNaMotorotKW" class="form-control" type="number" min="0" />
                            <span asp-validation-for="Input.SilinaNaMotorotKW" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.BrojNaSedista" class="form-label">Број на седишта</label>
                            <input asp-for="Input.BrojNaSedista" class="form-control" type="number" min="0" />
                            <span asp-validation-for="Input.BrojNaSedista" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.NosivostKG" class="form-label">Носивост (kg)</label>
                            <input asp-for="Input.NosivostKG" class="form-control" type="number" min="0" />
                            <span asp-validation-for="Input.NosivostKG" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label asp-for="Input.BojaNaVoziloto" class="form-label">Боја на возилото</label>
                            <input asp-for="Input.BojaNaVoziloto" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.BojaNaVoziloto" class="text-danger"></span>
                        </div>
                    </div>

<h5 class="mb-3">Група 2 - Дополнителни податоци</h5>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.DatumNaRegistracija" class="form-label">Датум на регистрација</label>
                            <input asp-for="Input.DatumNaRegistracija" class="form-control datepicker" type="date" />
                            <span asp-validation-for="Input.DatumNaRegistracija" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.DatumNaPrvataRegistracija" class="form-label">Датум на првата регистрација</label>
                            <input asp-for="Input.DatumNaPrvataRegistracija" class="form-control datepicker" type="date" />
                            <span asp-validation-for="Input.DatumNaPrvataRegistracija" class="text-danger"></span>
                        </div>
                    </div>
                    <!-- Податоци за корисник -->
                    <div class="row mt-4">
                        <h6 class="mb-3">Податоци за корисник</h6>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.BrojNaVpisot" class="form-label">Број на впис</label>
                            <input asp-for="Input.BrojNaVpisot" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.BrojNaVpisot" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.PrezimeNazivNaKorisnikot" class="form-label">Презиме/Назив на корисникот</label>
                            <input asp-for="Input.PrezimeNazivNaKorisnikot" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.PrezimeNazivNaKorisnikot" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.Ime" class="form-label">Име</label>
                            <input asp-for="Input.Ime" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.Ime" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label asp-for="Input.AdresaNaPostojanoZivealiste" class="form-label">Адреса на постојано живеалиште</label>
                            <input asp-for="Input.AdresaNaPostojanoZivealiste" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.AdresaNaPostojanoZivealiste" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.EMBNaKorisnikot" class="form-label">ЕМБ на корисникот</label>
                            <input asp-for="Input.EMBNaKorisnikot" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.EMBNaKorisnikot" class="text-danger"></span>
                        </div>
                    </div>
                    <!-- Податоци за регистрација -->
                    <div class="row mt-4">
                        <h6 class="mb-3">Податоци за регистрација</h6>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.DatumNaPrvaRegistracijaVoRSM" class="form-label">Датум на прва регистрација во РСМ</label>
                            <input asp-for="Input.DatumNaPrvaRegistracijaVoRSM" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.DatumNaPrvaRegistracijaVoRSM" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.DozvolataJaIzdal" class="form-label">Дозволата ја издал</label>
                            <input asp-for="Input.DozvolataJaIzdal" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.DozvolataJaIzdal" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.OznakaNaOdobrenie" class="form-label">Ознака на одобрение</label>
                            <input asp-for="Input.OznakaNaOdobrenie" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.OznakaNaOdobrenie" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label asp-for="Input.BrojNAEUPotvrdaZaSoobraznost" class="form-label">Број на ЕУ потврда за сообразност</label>
                            <input asp-for="Input.BrojNAEUPotvrdaZaSoobraznost" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.BrojNAEUPotvrdaZaSoobraznost" class="text-danger"></span>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="Input.PrezimeNazivNaSopstvenikot" class="form-label">Презиме/Назив на сопственикот</label>
                            <input asp-for="Input.PrezimeNazivNaSopstvenikot" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.PrezimeNazivNaSopstvenikot" class="text-danger"></span>
                        </div>
                    </div>
                    <!-- Податоци за сопственик -->
                    <div class="row mt-4">
                        <h6 class="mb-3">Податоци за сопственик</h6>
                        <div class="col-md-6 mb-3">
                            <label asp-for="Input.ImeSopstvenik" class="form-label">Име</label>
                            <input asp-for="Input.ImeSopstvenik" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.ImeSopstvenik" class="text-danger"></span>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="Input.EMBNaFizickoLiceEMBNaPravnoLice" class="form-label">ЕМБ на физичко/правно лице</label>
                            <input asp-for="Input.EMBNaFizickoLiceEMBNaPravnoLice" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.EMBNaFizickoLiceEMBNaPravnoLice" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label asp-for="Input.AdresaNaPostojanoZivealisteSediste" class="form-label">Адреса на постојано живеалиште/седиште</label>
                            <input asp-for="Input.AdresaNaPostojanoZivealisteSediste" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.AdresaNaPostojanoZivealisteSediste" class="text-danger"></span>
                        </div>
                    </div>
                    <!-- Технички податоци за возилото -->
                    <div class="row mt-4">
                        <h6 class="mb-3">Технички податоци за возилото</h6>
                        <div class="col-md-6 mb-3">
                            <label asp-for="Input.KategorijaIVidNaVoziloto" class="form-label">Категорија и вид на возилото</label>
                            <input asp-for="Input.KategorijaIVidNaVoziloto" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.KategorijaIVidNaVoziloto" class="text-danger"></span>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="Input.OblikINamenaNaKaroserijata" class="form-label">Облик и намена на каросеријата</label>
                            <input asp-for="Input.OblikINamenaNaKaroserijata" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.OblikINamenaNaKaroserijata" class="text-danger"></span>
                        </div>
                    </div>
                    <!-- Податоци за моторот -->
                    <div class="row mt-4">
                        <h6 class="mb-3">Податоци за моторот</h6>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.TipNaMotorot" class="form-label">Тип на моторот</label>
                            <input asp-for="Input.TipNaMotorot" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.TipNaMotorot" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.VidNaGorivo" class="form-label">Вид на гориво</label>
                            <input asp-for="Input.VidNaGorivo" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.VidNaGorivo" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.BrojNaVrtezi" class="form-label">Број на вртежи</label>
                            <input asp-for="Input.BrojNaVrtezi" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.BrojNaVrtezi" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label asp-for="Input.IdentifikacionenBrojNaMotorot" class="form-label">Идентификационен број на моторот</label>
                            <input asp-for="Input.IdentifikacionenBrojNaMotorot" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.IdentifikacionenBrojNaMotorot" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.MaksimalnaBrzinaKM" class="form-label">Максимална брзина (km/h)</label>
                            <input asp-for="Input.MaksimalnaBrzinaKM" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.MaksimalnaBrzinaKM" class="text-danger"></span>
                        </div>
                    </div>
                    <!-- Податоци за маса -->
                    <div class="row mt-4">
                        <h6 class="mb-3">Податоци за маса</h6>
                        <div class="col-md-6 mb-3">
                            <label asp-for="Input.OdnosSilinaMasa" class="form-label">Однос сила/маса</label>
                            <input asp-for="Input.OdnosSilinaMasa" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.OdnosSilinaMasa" class="text-danger"></span>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label asp-for="Input.MasaNaVoziloto" class="form-label">Маса на возилото</label>
                            <input asp-for="Input.MasaNaVoziloto" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.MasaNaVoziloto" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.NajgolemaKonstruktivnaVkupnaMasaNaVozilotoKG" class="form-label">Најголема конструктивна маса (kg)</label>
                            <input asp-for="Input.NajgolemaKonstruktivnaVkupnaMasaNaVozilotoKG" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.NajgolemaKonstruktivnaVkupnaMasaNaVozilotoKG" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.NajgolemaLegalnaVkupnaMasaNaVozilotoPriRegistracijaKG" class="form-label">Најголема легална маса (kg)</label>
                            <input asp-for="Input.NajgolemaLegalnaVkupnaMasaNaVozilotoPriRegistracijaKG" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.NajgolemaLegalnaVkupnaMasaNaVozilotoPriRegistracijaKG" class="text-danger"></span>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label asp-for="Input.NajgolemaLegalnaVkupnaMasaNaGrupaVozilaPriRegistracijaKG" class="form-label">Најголема легална маса на група (kg)</label>
                            <input asp-for="Input.NajgolemaLegalnaVkupnaMasaNaGrupaVozilaPriRegistracijaKG" class="form-control" maxlength="255" />
                            <span asp-validation-for="Input.NajgolemaLegalnaVkupnaMasaNaGrupaVozilaPriRegistracijaKG" class="text-danger"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Забелешка field at the bottom -->
        <div class="row mt-4">
            <div class="col-12">
                <label asp-for="Input.Zabeleska" class="form-label">Забелешка</label>
                <textarea asp-for="Input.Zabeleska" 
                          class="form-control" 
                          rows="3"
                          placeholder="Внесете забелешка..."></textarea>
                <span asp-validation-for="Input.Zabeleska" class="text-danger"></span>
            </div>
        </div>
        <!-- Save Button -->
        <div class="row mt-4">
            <div class="col-12 text-center">
                <button type="submit" class="btn btn-primary btn-lg">
                    <i class="fas fa-save me-2"></i>Зачувај
                </button>
            </div>
        </div>

        <!-- Bottom Success Message -->
        <div id="bottomSuccessMessage" class="alert alert-success alert-dismissible fade mt-3" role="alert" style="display: none;">
            <strong>Успешно!</strong> Полисата е успешно зачувана.
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    </form>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script>
        // Define the function globally
        function openAddClientWindow(sourceField) {
            const width = 800;
            const height = 600;
            const left = (window.screen.width - width) / 2;
            const top = (window.screen.height - height) / 2;
            
            const popup = window.open(`/Klienti/DodajKlient?fromPolisa=true&source=${sourceField}`, 'DodajKlient', 
                `width=${width},height=${height},left=${left},top=${top},resizable=yes,scrollbars=yes`);
            
            // Listen for messages from the popup
            window.addEventListener('message', function(event) {
                if (event.data.type === 'clientAdded') {
                    // Clear the search field based on the source
                    if (event.data.source === 'dogovoruvac') {
                        $('#dogovoruvacMBSearch').val('');
                        $('#KlientiIdDogovoruvac').val('');
                        $('#dogovoruvacSearchResults').hide();
                    } else if (event.data.source === 'osigurenik') {
                        $('#osigurenikMBSearch').val('');
                        $('#KlientiIdOsigurenik').val('');
                        $('#osigurenikSearchResults').hide();
                    } else if (event.data.source === 'sorabotnik') {
                        $('#sorabotnikMBSearch').val('');
                        $('#KlientiIdSorabotnik').val('');
                        $('#sorabotnikSearchResults').hide();
                    }
                }
            });
        }

        // Car brand and model functionality
        function handleBrandSelection() {
            const brandDropdown = document.getElementById('carBrandDropdown');
            const selectedBrandId = brandDropdown.value;
            const brandTextInput = document.getElementById('carBrandTextInput');
            const modelDropdown = document.getElementById('carModelDropdown');
            
            if (selectedBrandId) {
                // Set the brand text input value to the selected brand name
                const selectedBrandText = brandDropdown.options[brandDropdown.selectedIndex].text;
                if (selectedBrandText !== "-- Избери марка --") {
                    brandTextInput.value = selectedBrandText;
                }
                
                // Load car models for the selected brand
                fetch(`?handler=LoadCarModels&brandId=${selectedBrandId}`)
                    .then(response => response.json())
                    .then(data => {
                        modelDropdown.innerHTML = '<option value="">-- Избери модел --</option>';
                        data.forEach(model => {
                            const option = document.createElement('option');
                            option.value = model.value;
                            option.textContent = model.text;
                            modelDropdown.appendChild(option);
                        });
                    })
                    .catch(error => {
                        console.error('Error loading car models:', error);
                        modelDropdown.innerHTML = '<option value="">-- Грешка при вчитување --</option>';
                    });
            } else {
                modelDropdown.innerHTML = '<option value="">-- Прво избери марка --</option>';
                brandTextInput.value = '';
            }
        }

        function handleModelSelection() {
            const modelDropdown = document.getElementById('carModelDropdown');
            const modelTextInput = document.getElementById('carModelTextInput');
            const selectedModelText = modelDropdown.options[modelDropdown.selectedIndex].text;
            
            if (selectedModelText && selectedModelText !== "-- Избери модел --" && selectedModelText !== "-- Прво избери марка --") {
                modelTextInput.value = selectedModelText;
            }
        }
    </script>

    <style>
        #dogovoruvacSearchResults .list-group-item.text-center {
            padding: 1rem;
        }
        #dogovoruvacSearchResults .list-group-item.text-center p {
            color: #6c757d;
            margin-bottom: 0.75rem;
        }
        #dogovoruvacSearchResults .btn-primary {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
        }
    </style>

    
    <script>
        $(document).ready(function() {
            // Cyrillic to Latin conversion for RegisterskaOznaka field
            function convertCyrillicToLatin(text) {
                const cyrillicToLatinMap = {
                    'А': 'A', 'Б': 'B', 'В': 'V', 'Г': 'G', 'Д': 'D', 'Ѓ': 'GJ', 'Е': 'E',
                    'Ж': 'ZH', 'З': 'Z', 'Ѕ': 'DZ', 'И': 'I', 'Ј': 'J', 'К': 'K', 'Л': 'L',
                    'Љ': 'LJ', 'М': 'M', 'Н': 'N', 'Њ': 'NJ', 'О': 'O', 'П': 'P', 'Р': 'R',
                    'С': 'S', 'Т': 'T', 'Ќ': 'KJ', 'У': 'U', 'Ф': 'F', 'Х': 'H', 'Ц': 'C',
                    'Ч': 'CH', 'Џ': 'DZH', 'Ш': 'SH',
                    'а': 'a', 'б': 'b', 'в': 'v', 'г': 'g', 'д': 'd', 'ѓ': 'gj', 'е': 'e',
                    'ж': 'zh', 'з': 'z', 'ѕ': 'dz', 'и': 'i', 'ј': 'j', 'к': 'k', 'л': 'l',
                    'љ': 'lj', 'м': 'm', 'н': 'n', 'њ': 'nj', 'о': 'o', 'п': 'p', 'р': 'r',
                    'с': 's', 'т': 't', 'ќ': 'kj', 'у': 'u', 'ф': 'f', 'х': 'h', 'ц': 'c',
                    'ч': 'ch', 'џ': 'dzh', 'ш': 'sh'
                };
                
                return text.split('').map(char => cyrillicToLatinMap[char] || char).join('');
            }
            
            // Add input event listener to RegisterskaOznaka field
            $('#Input_RegisterskaOznaka').on('input', function() {
                const cyrillicText = $(this).val();
                const latinText = convertCyrillicToLatin(cyrillicText);
                
                // Only update if conversion actually changed something
                if (cyrillicText !== latinText) {
                    $(this).val(latinText);
                }
            });
            
            // Add click handlers for clear buttons
            $('.clear-field').on('click', function() {
                const target = $(this).data('target');
                if (target === 'dogovoruvac') {
                    $('#dogovoruvacMBSearch').val('');
                    $('#KlientiIdDogovoruvac').val('');
                    $('#dogovoruvacSearchResults').hide();
                } else if (target === 'osigurenik') {
                    $('#osigurenikMBSearch').val('');
                    $('#KlientiIdOsigurenik').val('');
                    $('#osigurenikSearchResults').hide();
                } else if (target === 'sorabotnik') {
                    $('#sorabotnikMBSearch').val('');
                    $('#KlientiIdSorabotnik').val('');
                    $('#sorabotnikSearchResults').hide();
                }
            });

            // Function to create search functionality
            function createSearchFunctionality(searchInputId, resultsContainerId, hiddenInputId, searchHandler = 'SearchKlienti') {
                let searchTimeout;
                const antiForgeryToken = $('input[name="__RequestVerificationToken"]').val();

                $(`#${searchInputId}`).on('input', function() {
                    clearTimeout(searchTimeout);
                    const searchTerm = $(this).val();
                    const resultsDiv = $(`#${resultsContainerId}`);

                    if (searchTerm.length < 1) {
                        resultsDiv.hide();
                        return;
                    }

                    searchTimeout = setTimeout(function() {
                        $.ajax({
                            url: `?handler=${searchHandler}`,
                            type: 'GET',
                            data: { mb: searchTerm },
                            headers: {
                                "RequestVerificationToken": antiForgeryToken
                            },
                            success: function(data) {
                                if (data && data.length > 0) {
                                    let html = '<div class="list-group">';
                                    data.forEach(item => {
                                        let displayText = '';
                                        if (item.tip === 'P') {
                                            displayText = `${item.naziv}`;
                                            let identifiers = [];
                                            if (item.mb) identifiers.push(`МБ: ${item.mb}`);
                                            if (item.edb) identifiers.push(`ЕДБ: ${item.edb}`);
                                            if (identifiers.length > 0) {
                                                displayText += ` (${identifiers.join(', ')})`;
                                            }
                                        } else {
                                            displayText = `${item.ime} ${item.prezime}`;
                                            let identifiers = [];
                                            if (item.embg) identifiers.push(`ЕМБГ: ${item.embg}`);
                                            if (item.mb) identifiers.push(`МБ: ${item.mb}`);
                                            if (identifiers.length > 0) {
                                                displayText += ` (${identifiers.join(', ')})`;
                                            }
                                        }
                                        html += `<a href="#" class="list-group-item list-group-item-action" data-id="${item.id}">
                                                  ${displayText}
                                               </a>`;
                                    });
                                    html += '</div>';
                                    resultsDiv.html(html).show();
                                } else {
                                    resultsDiv.html(`
                                        <div class="list-group">
                                            <div class="list-group-item text-center">
                                                <p class="mb-2">Нема пронајдени резултати</p>
                                                <button type="button" class="btn btn-primary" onclick="openAddClientWindow('${
                                                    searchInputId === 'dogovoruvacMBSearch' ? 'dogovoruvac' : 
                                                    searchInputId === 'osigurenikMBSearch' ? 'osigurenik' : 'sorabotnik'
                                                }')">
                                                    <i class="fas fa-plus"></i> Додај клиент
                                                </button>
                                            </div>
                                        </div>
                                    `).show();
                                }
                            },
                            error: function() {
                                resultsDiv.html(`
                                    <div class="list-group">
                                        <div class="list-group-item text-center">
                                            <p class="text-danger mb-2">Грешка при пребарување</p>
                                            <button type="button" class="btn btn-primary" onclick="openAddClientWindow('${
                                                searchInputId === 'dogovoruvacMBSearch' ? 'dogovoruvac' : 
                                                searchInputId === 'osigurenikMBSearch' ? 'osigurenik' : 'sorabotnik'
                                            }')">
                                                <i class="fas fa-plus"></i> Додај клиент
                                            </button>
                                        </div>
                                    </div>
                                `).show();
                            }
                        });
                    }, 300);
                });

                // Handle selection
                $(document).on('click', `#${resultsContainerId} .list-group-item`, function(e) {
                    e.preventDefault();
                    const id = $(this).data('id');
                    const displayText = $(this).text();
                    $(`#${searchInputId}`).val(displayText.trim());
                    $(`#${hiddenInputId}`).val(id);
                    $(`#${resultsContainerId}`).hide();
                });

                // Hide results when clicking outside
                $(document).on('click', function(e) {
                    if (!$(e.target).closest(`#${searchInputId}, #${resultsContainerId}`).length) {
                        $(`#${resultsContainerId}`).hide();
                    }
                });

                    // Add VkupnaPremija calculation function
        function calculateVkupnaPremija() {
            const osnovnaPremija = parseFloat($('#Input_OsnovnaPremija').val()) || 0;
            const dopolnitelnaPremija = parseFloat($('#Input_DopolnitelnaPremija').val()) || 0;
            const vkupnaPremija = osnovnaPremija + dopolnitelnaPremija;
            $('#Input_VkupnaPremija').val(vkupnaPremija.toFixed(4));
        }
            }

            // Initialize search for all fields
            createSearchFunctionality('dogovoruvacMBSearch', 'dogovoruvacSearchResults', 'KlientiIdDogovoruvac');
            createSearchFunctionality('osigurenikMBSearch', 'osigurenikSearchResults', 'KlientiIdOsigurenik');
            createSearchFunctionality('sorabotnikMBSearch', 'sorabotnikSearchResults', 'KlientiIdSorabotnik', 'SearchSorabotnici');

            // Car brand and model radio button event handlers
            $('input[name="brandInputType"]').on('change', function() {
                const brandDropdown = $('#carBrandDropdown');
                const brandTextInput = $('#carBrandTextInput');
                const useCustomBrandInput = $('#Input_UseCustomCarBrand');
                
                if ($(this).val() === 'dropdown') {
                    brandDropdown.show();
                    brandTextInput.hide();
                    useCustomBrandInput.val('false');
                } else {
                    brandDropdown.hide();
                    brandTextInput.show();
                    useCustomBrandInput.val('true');
                    // Clear dropdown selection when switching to custom
                    brandDropdown.val('');
                }
            });

            $('input[name="modelInputType"]').on('change', function() {
                const modelDropdown = $('#carModelDropdown');
                const modelTextInput = $('#carModelTextInput');
                const useCustomModelInput = $('#Input_UseCustomCarModel');
                
                if ($(this).val() === 'dropdown') {
                    modelDropdown.show();
                    modelTextInput.hide();
                    useCustomModelInput.val('false');
                } else {
                    modelDropdown.hide();
                    modelTextInput.show();
                    useCustomModelInput.val('true');
                    // Clear dropdown selection when switching to custom
                    modelDropdown.val('');
                }
            });

            // Function to calculate VkupnaPremija
            function calculateVkupnaPremija() {
                const osnovnaPremija = parseFloat($('#Input_OsnovnaPremija').val()) || 0;
                const dopolnitelnaPremija = parseFloat($('#Input_DopolnitelnaPremija').val()) || 0;
                const vkupnaPremija = osnovnaPremija + dopolnitelnaPremija;
                $('#Input_VkupnaPremija').val(vkupnaPremija.toFixed(4));
            }

            // Add event handlers for premium calculation
            $('#Input_OsnovnaPremija, #Input_DopolnitelnaPremija').on('input', calculateVkupnaPremija);
            
            // Calculate initial VkupnaPremija
            calculateVkupnaPremija();

            // Handle decimal input formatting
            $('.decimal-input').on('input', function() {
                let value = $(this).val();
                if (value !== '') {
                    // Ensure we don't exceed 4 decimal places
                    let parts = value.split('.');
                    if (parts.length > 1 && parts[1].length > 4) {
                        $(this).val(Number(value).toFixed(4));
                    }
                }
            });

            // Show/hide zabeleska field based on checkbox
            $('#kolektivnaNeodredena').change(function() {
                $('#neodredenBrZabeleskaContainer').toggle(this.checked);
            });

            /* Commented out because the Storno checkbox has been hidden
            // Show/hide pricina za storno field based on checkbox
            $('#storno').change(function() {
                $('#pricinaZaStornoContainer').toggle(this.checked);
            });
            */

            // Handle form submission
            $('#polisaForm').on('submit', function(e) {
                e.preventDefault();
                
                // Get reference to the submit button
                const submitButton = $(this).find('button[type="submit"]');
                var originalText = submitButton.html();
                submitButton.prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Зачувување...');
                
                $.ajax({
                    url: window.location.pathname,
                    type: 'POST',
                    data: $(this).serialize(),
                    success: function(response) {
                        if (response.success) {
                            // Immediate redirect
                            window.location.replace(response.redirectUrl);
                        } else {
                            submitButton.prop('disabled', false).html(originalText);
                            alert(response.errorMessage);
                        }
                    },
                    error: function() {
                        submitButton.prop('disabled', false).html(originalText);
                        alert("Настана грешка при зачувување на полисата.");
                    }
                });
            });

            // Handle collapse icon rotation
            $('.collapse').on('show.bs.collapse hide.bs.collapse', function() {
                $(this)
                    .parent()
                    .find('.card-header')
                    .find('.fas')
                    .toggleClass('rotated');
            });
            // Add policy number check
            $('#Input_BrojNaPolisa').on('change', function() {
                const brojNaPolisa = $(this).val();
                const errorDiv = $('#brojNaPolisa-error');
                if (!errorDiv.length) {
                    $('<div id="brojNaPolisa-error" class="text-danger" style="margin-bottom: 0.5rem;"></div>').insertAfter($(this));
                }
                
                if (brojNaPolisa) {
                    $.get('?handler=CheckBrojNaPolisa', { brojNaPolisa: brojNaPolisa })
                        .done(function(data) {
                            if (data.exists) {
                                $('#brojNaPolisa-error').text('Веќе постои полиса со овој број која не е сторнирана!');
                                $('#Input_BrojNaPolisa').addClass('is-invalid');
                                // Add a custom validation attribute to prevent form submission
                                $('#Input_BrojNaPolisa').attr('data-val-duplicate', 'true');
                            } else {
                                $('#brojNaPolisa-error').text('');
                                $('#Input_BrojNaPolisa').removeClass('is-invalid');
                                $('#Input_BrojNaPolisa').removeAttr('data-val-duplicate');
                            }
                        });
                } else {
                    $('#brojNaPolisa-error').text('');
                    $('#Input_BrojNaPolisa').removeClass('is-invalid');
                    $('#Input_BrojNaPolisa').removeAttr('data-val-duplicate');
                }
            });

            // Add form submission check
            $('form').on('submit', function(e) {
                if ($('#Input_BrojNaPolisa').attr('data-val-duplicate') === 'true') {
                    e.preventDefault();
                    return false;
                }
            });

            // Handle date validation and auto-filling
            $('#Input_DatumVaziOd, #Input_DatumVaziDo').on('change', function() {
                const vaziOd = $('#Input_DatumVaziOd').val();
                const vaziDo = $('#Input_DatumVaziDo').val();
                
                if (vaziDo && vaziOd) {
                    const dateVaziOd = new Date(vaziOd);
                    const dateVaziDo = new Date(vaziDo);
                    
                    if (dateVaziDo < dateVaziOd) {
                        // If Важи до is before Важи од, set Важи од to match Важи до
                        $('#Input_DatumVaziOd').val(vaziDo);
                        // Clear any validation errors
                        $('#vaziOd-error').remove();
                        $('#Input_DatumVaziOd').removeClass('input-validation-error');
                        $('span[data-valmsg-for="Input.DatumVaziOd"]').text('');
                        $('span[data-valmsg-for="Input.DatumVaziDo"]').text('');
                    }
                }
            });

            // Add custom date validation
            $.validator.addMethod('dateGreaterThan', function(value, element, params) {
                if (!value || !$(params.other).val()) return true;
                
                const startDate = new Date($(params.other).val());
                const endDate = new Date(value);
                
                if (endDate < startDate) {
                    // If end date is less than start date, update start date to match end date
                    $(params.other).val(value);
                    return true;
                }
                return true;
            });

            $.validator.unobtrusive.adapters.add('dateGreaterThan', ['other'], function(options) {
                options.rules['dateGreaterThan'] = {
                    other: '#' + options.params.other
                };
                options.messages['dateGreaterThan'] = options.message;
            });

            // Add new calculation functions
            function calculateVkupnaPremija() {
                const vrednost = parseFloat($('#Input_Vrednost').val()) || 0;
                const sumaNaosiguruvanje = parseFloat($('#Input_SumaNaosiguruvanje').val()) || 0;
                const osnovnaPremija = parseFloat($('#Input_OsnovnaPremija').val()) || 0;
                const dopolnitelnaPremija = parseFloat($('#Input_DopolnitelnaPremija').val()) || 0;
                
                const vkupnaPremija =  osnovnaPremija + dopolnitelnaPremija;
                $('#Input_VkupnaPremija').val(vkupnaPremija.toFixed(4));
            }

            function calculatePopusti() {
                const vkupnaPremija = parseFloat($('#Input_VkupnaPremija').val()) || 0;
                const procentNaPopustZaFakturaVoRok = parseFloat($('#Input_ProcentNaPopustZaFakturaVoRok').val()) || 0;
                const procentKomercijalenPopust = parseFloat($('#Input_ProcentKomercijalenPopust').val()) || 0;
                const procentFinansiski = parseFloat($('#Input_ProcentFinansiski').val()) || 0;

                // Calculate popust za faktura vo rok
                const iznosZaPlakjanjeVoRok = vkupnaPremija * (1 - (procentNaPopustZaFakturaVoRok / 100));
                $('#Input_IznosZaPlakjanjeVoRok').val(iznosZaPlakjanjeVoRok.toFixed(4));

                // Calculate premija za naplata
                const premijaZaNaplata = vkupnaPremija * (1 - (procentKomercijalenPopust / 100)) * (1 - (procentFinansiski / 100));
                $('#Input_PremijaZaNaplata').val(premijaZaNaplata.toFixed(4));
            }

            // Add event listeners for calculations
            $('#Input_Vrednost, #Input_SumaNaosiguruvanje, #Input_OsnovnaPremija, #Input_DopolnitelnaPremija').on('input', function() {
                calculateVkupnaPremija();
                calculatePopusti();
            });

            $('#Input_ProcentNaPopustZaFakturaVoRok, #Input_ProcentKomercijalenPopust, #Input_ProcentFinansiski').on('input', function() {
                calculatePopusti();
            });

            // Initialize calculations on page load
            $(document).ready(function() {
                calculateVkupnaPremija();
                calculatePopusti();
            });

            // Add date validation for DatumVaziOd and DatumVaziDo
            function validateDates() {
                const vaziOd = $('#Input_DatumVaziOd').val();
                const vaziDo = $('#Input_DatumVaziDo').val();
                
                if (vaziOd && vaziDo) {
                    const dateVaziOd = new Date(vaziOd);
                    const dateVaziDo = new Date(vaziDo);
                    
                    if (dateVaziDo < dateVaziOd) {
                        if (!$('#vaziDoError').length) {
                            $('#Input_DatumVaziDo').after('<span id="vaziDoError" class="text-danger">Датумот "Важи до" не може да биде пред "Важи од"</span>');
                        }
                        $('#Input_DatumVaziDo').val(vaziOd);
                        return false;
                    } else {
                        $('#vaziDoError').remove();
                        return true;
                    }
                }
                return true;
            }

            // Function to calculate months between two dates
            function calculateMonthsBetween(startDate, endDate) {
                const start = new Date(startDate);
                const end = new Date(endDate);
                return (end.getFullYear() - start.getFullYear()) * 12 + (end.getMonth() - start.getMonth()) + 1;
            }

            // Function to filter payment options based on policy duration
            function filterPaymentOptions() {
                const vaziOd = $('#Input_DatumVaziOd').val();
                const vaziDo = $('#Input_DatumVaziDo').val();
                
                if (vaziOd && vaziDo) {
                    const months = calculateMonthsBetween(vaziOd, vaziDo);
                    const paymentSelect = $('#Input_SifrarnikNacinNaPlakjanjeId');
                    
                    // Store original options if not already stored
                    if (!paymentSelect.data('original-options')) {
                        paymentSelect.data('original-options', paymentSelect.find('option').clone());
                    }
                    
                    // Reset options
                    paymentSelect.empty().append('<option value="">-- Избери начин на плаќање --</option>');
                    
                    // Get original options and filter them
                    const originalOptions = paymentSelect.data('original-options');
                    originalOptions.each(function() {
                        const $option = $(this);
                        if ($option.val() === '') return true; // Skip the placeholder option
                        
                        const optionText = $option.text().trim();
                        const rateMatch = optionText.match(/(\d+)\s+рати/);
                        
                        // Always include "Еднократно" option
                        if (optionText === 'Еднократно') {
                            paymentSelect.append($option.clone());
                            return true;
                        }
                        
                        // For rate options, check against policy duration
                        if (rateMatch) {
                            const rates = parseInt(rateMatch[1]);
                            if (rates <= months) {
                                paymentSelect.append($option.clone());
                            }
                        }
                    });
                }
            }

            // Add event listeners for date fields
            $('#Input_DatumVaziOd, #Input_DatumVaziDo').on('change', function() {
                validateDates();
                filterPaymentOptions();  // Call filterPaymentOptions after validateDates
            });

            // Add event handler for Premija to update VkupnaPremija
            $('#Input_Premija').on('input', function() {
                const premija = parseFloat($(this).val()) || 0;
                $('#Input_VkupnaPremija').val(premija.toFixed(4));
                calculatePopusti(); // Trigger popusti calculation when VkupnaPremija changes
            });

            // Function to calculate popusti
            function calculatePopusti() {
                const vkupnaPremija = parseFloat($('#Input_VkupnaPremija').val()) || 0;
                const procentNaPopustZaFakturaVoRok = parseFloat($('#Input_ProcentNaPopustZaFakturaVoRok').val()) || 0;
                const procentKomercijalenPopust = parseFloat($('#Input_ProcentKomercijalenPopust').val()) || 0;
                const procentFinansiski = parseFloat($('#Input_ProcentFinansiski').val()) || 0;

                // Calculate popust za faktura vo rok
                const iznosZaPlakjanjeVoRok = vkupnaPremija * (1 - (procentNaPopustZaFakturaVoRok / 100));
                $('#Input_IznosZaPlakjanjeVoRok').val(iznosZaPlakjanjeVoRok.toFixed(4));

                // Calculate premija za naplata
                const premijaZaNaplata = vkupnaPremija * (1 - (procentKomercijalenPopust / 100)) * (1 - (procentFinansiski / 100));
                $('#Input_PremijaZaNaplata').val(premijaZaNaplata.toFixed(4));

                // If any of the fields are empty, set them to 0
                if (!$('#Input_ProcentNaPopustZaFakturaVoRok').val()) {
                    $('#Input_ProcentNaPopustZaFakturaVoRok').val('0');
                }
                if (!$('#Input_ProcentKomercijalenPopust').val()) {
                    $('#Input_ProcentKomercijalenPopust').val('0');
                }
                if (!$('#Input_ProcentFinansiski').val()) {
                    $('#Input_ProcentFinansiski').val('0');
                }
            }

            // Add event listeners for popusti calculations
            $('#Input_VkupnaPremija').on('input', calculatePopusti);
            $('#Input_ProcentNaPopustZaFakturaVoRok, #Input_ProcentKomercijalenPopust, #Input_ProcentFinansiski').on('input', calculatePopusti);

            // Initialize calculations on page load
            $(document).ready(function() {
                calculatePopusti();
            });
        });
    </script>
}

@section Styles {
    <style>
        .card-header[data-bs-toggle="collapse"] {
            cursor: pointer;
        }
        
        .card-header[data-bs-toggle="collapse"]:hover {
            background-color: rgba(0,0,0,.03);
        }
        
        .fas {
            transition: transform 0.3s ease-in-out;
        }
        
        /* Rotate when section is expanded */
        .rotated {
            transform: rotate(180deg);
        }

        #dogovoruvacSearchResults .list-group-item.text-center {
            padding: 1rem;
        }
        #dogovoruvacSearchResults .list-group-item.text-center p {
            color: #6c757d;
            margin-bottom: 0.75rem;
        }
        #dogovoruvacSearchResults .btn-primary {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
        }
        
    </style>
}