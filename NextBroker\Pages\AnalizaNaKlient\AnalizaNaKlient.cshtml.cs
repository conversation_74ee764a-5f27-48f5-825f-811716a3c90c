using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System.ComponentModel.DataAnnotations;
using Microsoft.Data.SqlClient;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Text.Json;
using iTextSharp.text;
using iTextSharp.text.pdf;
using System.IO;
using System.Collections.Generic;
using System.Linq;
using MailKit.Net.Smtp;
using MimeKit;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Hosting;
using MailKit.Security;
using System.IO;

namespace NextBroker.Pages.AnalizaNaKlient
{
    public class AnalizaNaKlientModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<AnalizaNaKlientModel> _logger;
        private readonly IWebHostEnvironment _webHostEnvironment;

        [BindProperty]
        public AnalizaInputModel Input { get; set; } = new();

        public List<SelectListItem> Osiguriteli { get; set; } = new List<SelectListItem>();
        public List<SelectListItem> Rizici { get; set; } = new List<SelectListItem>();
        public List<SelectListItem> Klienti { get; set; } = new List<SelectListItem>();
        public List<SelectListItem> Klasi { get; set; } = new List<SelectListItem>();
        public List<SelectListItem> Produkti { get; set; } = new List<SelectListItem>();

        public AnalizaNaKlientModel(IConfiguration configuration, ILogger<AnalizaNaKlientModel> logger, IWebHostEnvironment webHostEnvironment)
            : base(configuration)
        {
            _configuration = configuration;
            _logger = logger;
            _webHostEnvironment = webHostEnvironment;
        }

        private async Task SendTestEmailAsync(string recipientEmail)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("Starting test email sending...");
                Console.WriteLine("Starting test email sending...");
                
                var emailSettings = _configuration.GetSection("EmailSettings");
                var smtpServer = "smtp.gmail.com";
                var smtpPort = 587;
                var email = "<EMAIL>";
                var appCode = "gniz ixey qsqe aydz";

                System.Diagnostics.Debug.WriteLine($"SMTP Settings - Server: {smtpServer}, Port: {smtpPort}, Email: {email}");
                Console.WriteLine($"SMTP Settings - Server: {smtpServer}, Port: {smtpPort}, Email: {email}");
                Console.WriteLine($"Recipient Email: {recipientEmail}");

                var message = new MimeMessage();
                message.From.Add(new MailboxAddress("NextBroker", email));
                message.To.Add(new MailboxAddress("Client", recipientEmail));
                message.Subject = "Споредба на осигурители";
                message.Date = DateTimeOffset.Now;

                // Create the email body
                var body = new TextPart("plain")
                {
                    Text = "Почитувани,\n\nВо прилог Ви ја испраќаме споредбата на осигурителите и информациите согласно член 50 од Закон за супервизија во осигурување.\n\nСо почит,\nNextBroker"
                };

                // Create a multipart message to hold both the body and attachments
                var multipart = new Multipart("mixed");
                multipart.Add(body);

                // Generate PDF using the same logic as the GeneratePdf button
                if (Input.Osiguriteli != null && Input.Osiguriteli.Any() && Input.Kombinacii != null && Input.Kombinacii.Any())
                {
                    Console.WriteLine("Starting PDF generation...");
                    var pdfRequest = new PdfRequestModel
                    {
                        InsurerIds = Input.Osiguriteli.Select(o => o.Id ?? 0).ToList(),
                        RiskIds = Input.Kombinacii.Select(k => k.RizikIdRizici).ToList(),
                        AdditionalData = new AdditionalDataModel
                        {
                            Email = recipientEmail,
                            Ime = Input.Ime,
                            Prezime = Input.Prezime,
                            EMBG = Input.EMBG,
                            Adresa = Input.Adresa,
                            Naziv = Input.Naziv,
                            PravnoEkonomskiOdnosi = Input.PravnoEkonomskiOdnosiOsiguritel,
                            PrivizijaZaPredlogPonuda = Input.PrivizijaZaPredlogPonuda,
                            ProvizijaZaSiteProdukti = Input.ProvizijaZaSiteProduktiIOsiguriteli,
                            PremijaIznos = Input.PremijaIznos,
                            ObrazlozenieZaPredlog = Input.ObrazlozenieZaPredlog,
                            RiziciOsiguritel = Input.RiziciOsiguritel ?? ""  // Ensure it's not null
                        }
                    };

                    Console.WriteLine($"PDF Request - InsurerIds: {string.Join(",", pdfRequest.InsurerIds)}, RiskIds: {string.Join(",", pdfRequest.RiskIds)}");

                    // Get the comparison data
                    var comparisonData = await GetComparisonDataAsync(pdfRequest.InsurerIds, pdfRequest.RiskIds);
                    Console.WriteLine($"Retrieved comparison data - Insurers: {comparisonData.Insurers.Count}, Risks: {comparisonData.Risks.Count}");

                    // Register custom encoding and font
                    var fontPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "fonts", "arial.ttf");
                    if (!System.IO.File.Exists(fontPath))
                    {
                        throw new Exception("Font file not found");
                    }

                    // Register the font with iTextSharp
                    var baseFont = BaseFont.CreateFont(fontPath, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
                    var titleFont = new Font(baseFont, 18, Font.BOLD);
                    var headerFont = new Font(baseFont, 11, Font.BOLD);
                    var cellFont = new Font(baseFont, 10, Font.NORMAL);
                    var contentFont = new Font(baseFont, 11, Font.NORMAL);
                    var sectionFont = new Font(baseFont, 12, Font.BOLD);

                    // Create PDF document
                    using (var memoryStream = new MemoryStream())
                    {
                        var doc = new Document(PageSize.A4.Rotate(), 50, 50, 25, 25);
                        var writer = PdfWriter.GetInstance(doc, memoryStream);

                        doc.Open();

                        // Add title
                        var title = new Paragraph("Споредба на осигурители", titleFont);
                        title.Alignment = Element.ALIGN_CENTER;
                        title.SpacingAfter = 30f;
                        doc.Add(title);

                        // Create table for comparison
                        var table = new PdfPTable(comparisonData.Insurers.Count + 1);
                        table.WidthPercentage = 100;
                        table.SpacingBefore = 10f;
                        table.SpacingAfter = 30f;

                        // Set relative column widths
                        float[] columnWidths = new float[comparisonData.Insurers.Count + 1];
                        columnWidths[0] = 3f; // Risk column wider
                        for (int i = 1; i < columnWidths.Length; i++)
                        {
                            columnWidths[i] = 2f; // Insurer columns
                        }
                        table.SetWidths(columnWidths);

                        // Define colors
                        var headerBgColor = new BaseColor(248, 249, 250); // #f8f9fa
                        var borderColor = new BaseColor(222, 226, 230); // #dee2e6
                        var successColor = new BaseColor(40, 167, 69); // Bootstrap success color
                        var dangerColor = new BaseColor(220, 53, 69); // Bootstrap danger color

                        // Add headers
                        var riskHeader = new PdfPCell(new Phrase("Ризик", headerFont));
                        riskHeader.BackgroundColor = headerBgColor;
                        riskHeader.HorizontalAlignment = Element.ALIGN_LEFT;
                        riskHeader.VerticalAlignment = Element.ALIGN_MIDDLE;
                        riskHeader.Padding = 10f;
                        riskHeader.BorderColor = borderColor;
                        table.AddCell(riskHeader);

                        foreach (var insurer in comparisonData.Insurers)
                        {
                            var insurerHeader = new PdfPCell(new Phrase(insurer, headerFont));
                            insurerHeader.BackgroundColor = headerBgColor;
                            insurerHeader.HorizontalAlignment = Element.ALIGN_CENTER;
                            insurerHeader.VerticalAlignment = Element.ALIGN_MIDDLE;
                            insurerHeader.Padding = 10f;
                            insurerHeader.BorderColor = borderColor;
                            table.AddCell(insurerHeader);
                        }

                        // Add data rows
                        foreach (var risk in comparisonData.Risks)
                        {
                            // Risk name cell
                            var riskCell = new PdfPCell(new Phrase(risk, cellFont));
                            riskCell.HorizontalAlignment = Element.ALIGN_LEFT;
                            riskCell.VerticalAlignment = Element.ALIGN_MIDDLE;
                            riskCell.PaddingLeft = 15f;
                            riskCell.PaddingTop = 10f;
                            riskCell.PaddingBottom = 10f;
                            riskCell.BorderColor = borderColor;
                            table.AddCell(riskCell);

                            // Coverage cells for each insurer
                            foreach (var insurer in comparisonData.Insurers)
                            {
                                var coverage = comparisonData.GetCoverage(risk, insurer);
                                var textColor = coverage.Pokrieno ? successColor : dangerColor;

                                var phrase = new Phrase();
                                
                                // Add symbol using ZapfDingbats
                                var zapfFont = new Font(Font.FontFamily.ZAPFDINGBATS, 12, Font.NORMAL, textColor);
                                phrase.Add(new Chunk(coverage.Pokrieno ? "4" : "8", zapfFont)); // 4 is checkmark, 8 is X in ZapfDingbats
                                
                                // Add space and text
                                phrase.Add(new Chunk(" ", new Font(baseFont, 6, Font.NORMAL))); // small space
                                phrase.Add(new Chunk(coverage.Pokrieno ? "Да" : "Не", new Font(baseFont, 10, Font.NORMAL, textColor)));

                                if (!string.IsNullOrEmpty(coverage.Beleshka))
                                {
                                    phrase.Add(new Chunk("\n(" + coverage.Beleshka + ")", new Font(baseFont, 9, Font.NORMAL, BaseColor.GRAY)));
                                }

                                var coverageCell = new PdfPCell(phrase);
                                coverageCell.HorizontalAlignment = Element.ALIGN_CENTER;
                                coverageCell.VerticalAlignment = Element.ALIGN_MIDDLE;
                                coverageCell.PaddingTop = 10f;
                                coverageCell.PaddingBottom = 10f;
                                coverageCell.BorderColor = borderColor;
                                table.AddCell(coverageCell);
                            }
                        }

                        doc.Add(table);

                        // Add a line break after the table
                        doc.Add(new Paragraph("\n"));

                        // Add additional information section title
                        var additionalInfoTitle = new Paragraph("Дополнителни информации", titleFont);
                        additionalInfoTitle.Alignment = Element.ALIGN_LEFT;
                        additionalInfoTitle.SpacingBefore = 20f;
                        additionalInfoTitle.SpacingAfter = 15f;
                        doc.Add(additionalInfoTitle);

                        // Add insurer-specific information
                        foreach (var insurer in pdfRequest.AdditionalData.Insurers)
                        {
                            var insurerTitle = new Paragraph(new Phrase(new Chunk(insurer.Name ?? "-", sectionFont)));
                            insurerTitle.SpacingBefore = 15f;
                            insurerTitle.SpacingAfter = 10f;
                            doc.Add(insurerTitle);

                            // Create a table for insurer-specific information
                            var insurerTable = new PdfPTable(2);
                            insurerTable.WidthPercentage = 100;
                            insurerTable.SetWidths(new float[] { 1.5f, 3f });

                            void AddInsurerInfoRow(string label, string content)
                            {
                                // Label cell with explicit font encoding
                                var labelChunk = new Chunk(label, sectionFont);
                                var labelCell = new PdfPCell(new Phrase(labelChunk));
                                labelCell.BorderColor = borderColor;
                                labelCell.BackgroundColor = headerBgColor;
                                labelCell.Padding = 8f;
                                labelCell.MinimumHeight = 30f;
                                insurerTable.AddCell(labelCell);

                                // Content cell with explicit font encoding
                                var contentChunk = new Chunk(content ?? "-", contentFont);
                                var contentCell = new PdfPCell(new Phrase(contentChunk));
                                contentCell.BorderColor = borderColor;
                                contentCell.Padding = 8f;
                                contentCell.MinimumHeight = 30f;
                                insurerTable.AddCell(contentCell);
                            }

                            // Add insurer-specific fields with explicit content
                            AddInsurerInfoRow("Правно-економски односи со осигурител:", 
                                string.IsNullOrEmpty(insurer.PravnoEkonomskiOdnosi) ? "-" : insurer.PravnoEkonomskiOdnosi);
                            AddInsurerInfoRow("Провизија за предлог понуда:", 
                                string.IsNullOrEmpty(insurer.PrivizijaZaPredlogPonuda) ? "-" : insurer.PrivizijaZaPredlogPonuda);
                            AddInsurerInfoRow("Премија износ:", 
                                insurer.PremijaIznos.HasValue ? 
                                insurer.PremijaIznos.Value.ToString("N2") + " ден." : 
                                "-");
                            // Removed RiziciOsiguritel field from per-insurer section

                            doc.Add(insurerTable);
                            doc.Add(new Paragraph("\n"));
                        }

                        // Add a line break before final information
                        doc.Add(new Paragraph("\n"));

                        // Add final information (ObrazlozenieZaPredlog)
                        var finalInfoTable = new PdfPTable(2);
                        finalInfoTable.WidthPercentage = 100;
                        finalInfoTable.SetWidths(new float[] { 1.5f, 3f });

                        void AddFinalInfoRow(string label, string content)
                        {
                            // Label cell
                            var labelCell = new PdfPCell(new Phrase(label, sectionFont));
                            labelCell.BorderColor = borderColor;
                            labelCell.BackgroundColor = headerBgColor;
                            labelCell.Padding = 8f;
                            labelCell.MinimumHeight = 30f;
                            finalInfoTable.AddCell(labelCell);

                            // Content cell
                            var contentCell = new PdfPCell(new Phrase(content ?? "-", contentFont));
                            contentCell.BorderColor = borderColor;
                            contentCell.Padding = 8f;
                            contentCell.MinimumHeight = 30f;
                            finalInfoTable.AddCell(contentCell);
                        }

                        // Add Ризици за предмет на осигурување as a global field
                        Console.WriteLine($"Debug - Before adding RiziciOsiguritel to PDF:");
                        Console.WriteLine($"Debug - request.AdditionalData.RiziciOsiguritel value: '{pdfRequest.AdditionalData.RiziciOsiguritel}'");
                        Console.WriteLine($"Debug - request.AdditionalData.RiziciOsiguritel is null: {pdfRequest.AdditionalData.RiziciOsiguritel == null}");
                        Console.WriteLine($"Debug - request.AdditionalData.RiziciOsiguritel is empty: {string.IsNullOrEmpty(pdfRequest.AdditionalData.RiziciOsiguritel)}");
                        Console.WriteLine($"Debug - request.AdditionalData.RiziciOsiguritel length: {pdfRequest.AdditionalData.RiziciOsiguritel?.Length ?? 0}");

                        var riziciValue = !string.IsNullOrEmpty(pdfRequest.AdditionalData.RiziciOsiguritel) ? 
                            pdfRequest.AdditionalData.RiziciOsiguritel : "-";
                        Console.WriteLine($"Debug - Final riziciValue to be added to PDF: '{riziciValue}'");

                        AddFinalInfoRow("Ризици за предмет на осигурување:", riziciValue);

                        // Add ProvizijaZaSiteProdukti field
                        AddFinalInfoRow("Провизија за сите производи и осигурители:", 
                            "https://inco.com.mk/ProvizijaOsiguriteli");

                        // Add a line break
                        doc.Add(new Paragraph("\n"));

                        // Add ObrazlozenieZaPredlog
                        AddFinalInfoRow("Образложение за предлог:", 
                            pdfRequest.AdditionalData.ObrazlozenieZaPredlog);

                        doc.Add(finalInfoTable);

                        // Add a line break before declaration
                        doc.Add(new Paragraph("\n\n"));

                        // Always start Izjava on a new page
                        doc.NewPage();

                        // Add declaration title
                        var declarationTitle = new Paragraph("Изјава", sectionFont);
                        declarationTitle.SpacingAfter = 15f;
                        doc.Add(declarationTitle);

                        // Create declaration text based on whether it's a person or company
                        string declarationText;
                        if (!string.IsNullOrEmpty(pdfRequest.AdditionalData.Naziv))
                        {
                            // Company declaration
                            declarationText = $"Јас __________________(Име, презиме), ЕМБГ____________________, адреса " +
                                $"во својство на застапник на {pdfRequest.AdditionalData.Naziv} (Назив на правно лице), " +
                                "изјавувам дека ОБД ИНКО АД Скопје, со дозвола за вршење осигурително брокерски работи број УП 14-1-16, " +
                                "издадена на 14.03.2025 година го имам овластено да посредува во мое име и моја сметка за склучување на полиси " +
                                "за осигурување од сите класи, ги примив и разбрав информациите од член 50 согласно Закон за супервизија во осигурување, " +
                                "информиран/а сум за провизијата која ја прима Осигурително Брокерското Друштво од Осигурителите, " +
                                "ја примив и разбрав анализата за осигурително покрите и сум согласен со предложената понуда од ОБД ИНКО АД Скопјe, " +
                                "се согласувам за склучување на договор за осигурување.";
                        }
                        else if (!string.IsNullOrEmpty(pdfRequest.AdditionalData.Ime) || !string.IsNullOrEmpty(pdfRequest.AdditionalData.Prezime))
                        {
                            // Personal declaration
                            var nameField = !string.IsNullOrEmpty(pdfRequest.AdditionalData.Ime) && !string.IsNullOrEmpty(pdfRequest.AdditionalData.Prezime) ?
                                $"{pdfRequest.AdditionalData.Ime} {pdfRequest.AdditionalData.Prezime}" :
                                "__________________";
                            
                            declarationText = $"Јас {nameField} (Име, презиме), " +
                                $"ЕМБГ{(string.IsNullOrEmpty(pdfRequest.AdditionalData.EMBG) ? "__________________" : pdfRequest.AdditionalData.EMBG)}, " +
                                $"адреса {(string.IsNullOrEmpty(pdfRequest.AdditionalData.Adresa) ? "_____________________" : pdfRequest.AdditionalData.Adresa)} " +
                                "изјавувам дека ОБД ИНКО АД Скопје, со дозвола за вршење осигурително брокерски работи број УП 14-1-16, " +
                                "издадена на 14.03.2025 година, го имам овластено да посредува во мое име и моја сметка за склучување на полиси " +
                                "за осигурување од сите класи, ги примив и разбрав информациите од член 50 согласно Закон за супервизија во осигурување, " +
                                "информиран/а сум за провизијата која ја прима Осигурително Брокерското Друштво од Осигурителите, " +
                                "ја примив и разбрав анализата за осигурително покрите и сум согласен со предложената понуда од ОБД ИНКО АД Скопјe, " +
                                "се согласувам за склучување на договор за осигурување.";
                        }
                        else
                        {
                            declarationText = "";
                        }

                        if (!string.IsNullOrEmpty(declarationText))
                        {
                            // Add declaration paragraph with proper formatting
                            var declaration = new Paragraph(declarationText, contentFont);
                            declaration.Alignment = Element.ALIGN_JUSTIFIED;
                            declaration.SetLeading(0f, 1.5f); // Increase line spacing
                            declaration.SpacingAfter = 30f;
                            doc.Add(declaration);

                            // Add date line
                            var dateLine = new Paragraph("\n\nДата: _______________", contentFont);
                            dateLine.Alignment = Element.ALIGN_LEFT;
                            doc.Add(dateLine);

                            // Add signature line
                            var signatureLine = new Paragraph("Потпис: _______________", contentFont);
                            signatureLine.Alignment = Element.ALIGN_RIGHT;
                            signatureLine.SpacingBefore = 0f;
                            doc.Add(signatureLine);
                        }

                        doc.Close();

                        // Add the generated PDF as an attachment
                        var pdfBytes = memoryStream.ToArray();
                        Console.WriteLine($"Generated PDF size: {pdfBytes.Length} bytes");
                        
                        var pdfAttachment = new MimePart("application", "pdf")
                        {
                            Content = new MimeContent(new MemoryStream(pdfBytes)),
                            ContentDisposition = new ContentDisposition(ContentDisposition.Attachment),
                            ContentTransferEncoding = ContentEncoding.Base64,
                            FileName = "Споредба_на_осигурители.pdf"
                        };
                        multipart.Add(pdfAttachment);
                        Console.WriteLine("PDF attachment added to email");
                    }
                }
                else
                {
                    Console.WriteLine("No insurers or combinations selected for PDF generation");
                }

                // Add the document from the documents folder
                var documentsPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "documents", "Informacii solasno clen 50ZSO .docx");
                if (System.IO.File.Exists(documentsPath))
                {
                    var documentBytes = await System.IO.File.ReadAllBytesAsync(documentsPath);
                    var documentAttachment = new MimePart("application", "vnd.openxmlformats-officedocument.wordprocessingml.document")
                    {
                        Content = new MimeContent(new MemoryStream(documentBytes)),
                        ContentDisposition = new ContentDisposition(ContentDisposition.Attachment),
                        ContentTransferEncoding = ContentEncoding.Base64,
                        FileName = "Информации согласно член 50 ЗСО.docx"
                    };
                    multipart.Add(documentAttachment);
                    Console.WriteLine("Document attachment added to email");
                }

                message.Body = multipart;

                System.Diagnostics.Debug.WriteLine("Connecting to SMTP server...");
                Console.WriteLine("Connecting to SMTP server...");

                using (var client = new SmtpClient())
                {
                    try
                    {
                        // Accept all SSL certificates
                        client.ServerCertificateValidationCallback = (s, c, h, e) => true;
                        
                        // Set timeout
                        client.Timeout = 30000; // 30 seconds
                        
                        System.Diagnostics.Debug.WriteLine("Attempting to connect...");
                        Console.WriteLine("Attempting to connect...");
                        
                        // Connect with explicit SSL
                        await client.ConnectAsync(smtpServer, smtpPort, MailKit.Security.SecureSocketOptions.StartTls);
                        
                        System.Diagnostics.Debug.WriteLine("Connected successfully");
                        Console.WriteLine("Connected successfully");
                        
                        System.Diagnostics.Debug.WriteLine("Attempting to authenticate...");
                        Console.WriteLine("Attempting to authenticate...");
                        
                        // Authenticate with the credentials
                        await client.AuthenticateAsync(email, appCode);
                        
                        System.Diagnostics.Debug.WriteLine("Authenticated successfully");
                        Console.WriteLine("Authenticated successfully");
                        
                        System.Diagnostics.Debug.WriteLine("Sending email...");
                        Console.WriteLine("Sending email...");
                        
                        // Send the message
                        await client.SendAsync(message);
                        
                        System.Diagnostics.Debug.WriteLine("Email sent successfully");
                        Console.WriteLine("Email sent successfully");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error during SMTP operations: {ex.Message}");
                        Console.WriteLine($"Error during SMTP operations: {ex.Message}");
                        System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                        Console.WriteLine($"Stack trace: {ex.StackTrace}");
                        
                        if (ex is MailKit.Security.AuthenticationException)
                        {
                            System.Diagnostics.Debug.WriteLine("Authentication failed. Please check your credentials.");
                            Console.WriteLine("Authentication failed. Please check your credentials.");
                        }
                        else if (ex is MailKit.Net.Smtp.SmtpCommandException smtpEx)
                        {
                            System.Diagnostics.Debug.WriteLine($"SMTP Error: {smtpEx.Message}");
                            Console.WriteLine($"SMTP Error: {smtpEx.Message}");
                            System.Diagnostics.Debug.WriteLine($"Status Code: {smtpEx.StatusCode}");
                            Console.WriteLine($"Status Code: {smtpEx.StatusCode}");
                        }
                        
                        throw;
                    }
                    finally
                    {
                        try
                        {
                            System.Diagnostics.Debug.WriteLine("Disconnecting...");
                            Console.WriteLine("Disconnecting...");
                            await client.DisconnectAsync(true);
                            System.Diagnostics.Debug.WriteLine("Disconnected successfully");
                            Console.WriteLine("Disconnected successfully");
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"Error during disconnect: {ex.Message}");
                            Console.WriteLine($"Error during disconnect: {ex.Message}");
                        }
                    }
                }
                
                System.Diagnostics.Debug.WriteLine("Test email process completed successfully");
                Console.WriteLine("Test email process completed successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in test email: {ex.Message}");
                Console.WriteLine($"Error in test email: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                if (ex.InnerException != null)
                {
                    System.Diagnostics.Debug.WriteLine($"Inner exception: {ex.InnerException.Message}");
                    Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }
                throw;
            }
        }

        public async Task<IActionResult> OnPostTestEmailAsync()
        {
            if (!await HasPageAccess("AnalizaNaKlient"))
            {
                return RedirectToAccessDenied();
            }

            try
            {
                if (string.IsNullOrEmpty(Input?.Email))
                {
                    return new JsonResult(new { success = false, message = "Email address is required" });
                }

                Console.WriteLine($"Debug - Input.RiziciOsiguritel value: '{Input.RiziciOsiguritel}'");

                // Get insurer names from database
                var insurerNames = new Dictionary<long, string>();
                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();
                    var insurerIds = Input.Osiguriteli?.Where(o => o.Id.HasValue).Select(o => o.Id.Value).ToList() ?? new List<long>();
                    if (insurerIds.Any())
                    {
                        var query = "SELECT Id, Naziv FROM Klienti WHERE Id IN (" + string.Join(",", insurerIds) + ")";
                        using (var command = new SqlCommand(query, connection))
                        {
                            using (var reader = await command.ExecuteReaderAsync())
                            {
                                while (await reader.ReadAsync())
                                {
                                    insurerNames[reader.GetInt64(0)] = reader.GetString(1);
                                }
                            }
                        }
                    }
                }

                // Create PDF request model
                var pdfRequest = new PdfRequestModel
                {
                    InsurerIds = Input.Osiguriteli?.Where(o => o.Id.HasValue).Select(o => o.Id.Value).ToList() ?? new List<long>(),
                    RiskIds = Input.Kombinacii?.Select(k => k.RizikIdRizici).ToList() ?? new List<long>(),
                    AdditionalData = new AdditionalDataModel
                    {
                        Ime = Input.Ime,
                        Prezime = Input.Prezime,
                        EMBG = Input.EMBG,
                        Adresa = Input.Adresa,
                        Naziv = Input.Naziv,
                        Email = Input.Email,
                        ObrazlozenieZaPredlog = Input.ObrazlozenieZaPredlog,
                        RiziciOsiguritel = Input.RiziciOsiguritel ?? "",  // Ensure it's not null
                        Insurers = Input.Osiguriteli?.Where(o => o.Id.HasValue).Select(o => new InsurerData
                        {
                            Name = insurerNames.ContainsKey(o.Id.Value) ? insurerNames[o.Id.Value] : "",
                            PravnoEkonomskiOdnosi = o.PravnoEkonomskiOdnosiOsiguritel,
                            PrivizijaZaPredlogPonuda = o.PrivizijaZaPredlogPonuda,
                            ProvizijaZaSiteProdukti = o.ProvizijaZaSiteProduktiIOsiguriteli,
                            PremijaIznos = o.PremijaIznos,
                            RiziciOsiguritel = null  // Set to null since we're using the global field
                        }).ToList() ?? new List<InsurerData>()
                    }
                };

                Console.WriteLine($"Debug - pdfRequest.AdditionalData.RiziciOsiguritel value: '{pdfRequest.AdditionalData.RiziciOsiguritel}'");

                // Generate PDF and send email directly
                var pdfResult = await GeneratePdfInternalAsync(pdfRequest, sendEmail: true);
                if (pdfResult is JsonResult jsonResult)
                {
                    var result = jsonResult.Value as dynamic;
                    if (result?.success == true)
                    {
                        return new JsonResult(new { success = true, message = "Test email sent successfully" });
                    }
                    else
                    {
                        return new JsonResult(new { success = false, message = result?.message ?? "Failed to generate PDF" });
                    }
                }
                
                return new JsonResult(new { success = false, message = "Failed to generate PDF" });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in test email endpoint: {ex.Message}");
                Console.WriteLine($"Error in test email endpoint: {ex.Message}");
                return new JsonResult(new { 
                    success = false, 
                    message = ex.Message,
                    stackTrace = ex.StackTrace,
                    innerException = ex.InnerException?.Message
                });
            }
        }

        private async Task SendEmailWithPdfAsync(string recipientEmail, byte[] pdfBytes, string clientName)
        {
            try
            {
                if (string.IsNullOrEmpty(recipientEmail))
                {
                    throw new ArgumentException("Recipient email address cannot be null or empty", nameof(recipientEmail));
                }

                var smtpServer = _configuration["EmailSettings:SmtpServer"];
                var smtpPortStr = _configuration["EmailSettings:SmtpPort"];
                var email = _configuration["EmailSettings:Email"];
                var appCode = _configuration["EmailSettings:AppCode"];

                if (string.IsNullOrEmpty(smtpServer) || string.IsNullOrEmpty(smtpPortStr) || 
                    string.IsNullOrEmpty(email) || string.IsNullOrEmpty(appCode))
                {
                    throw new InvalidOperationException("Email settings are not properly configured. Please check appsettings.json");
                }

                if (!int.TryParse(smtpPortStr, out int smtpPort))
                {
                    throw new InvalidOperationException("Invalid SMTP port number in configuration");
                }

                _logger.LogInformation($"Sending email to {recipientEmail} using server {smtpServer}:{smtpPort}");

                var message = new MimeMessage();
                message.From.Add(new MailboxAddress("NextBroker", email));
                message.To.Add(new MailboxAddress(clientName ?? "Client", recipientEmail));
                message.Bcc.Add(new MailboxAddress("NextBroker", email)); // Add BCC
                message.Subject = "Анализа на клиент - NextBroker";

                var builder = new BodyBuilder();
                builder.HtmlBody = $@"
                    <h2>Почитуван/а,</h2>
                    <p>Во прилог доставуваме анализа за осигурително покритие и информации согласно одредбите од член 50. од Законот за супервизија на осигурување, а со цел заштита на Вашите интереси.</p>
                    <p>Доколку се согласувате Ве замолуваме да ја доставите изјавата потпишана или да се согласите со одговор на e-mail <EMAIL></p>
                    <p>Ви благодариме на довербата и соработката,</p>
                    <p>Со почит,<br>Осигурително Брокерско Друштво ИНКО АД Скопје<br>Бул. Крсте Мисирков бр.1, 1000 Скопје<br>Тел 075/400-912</p>";

                // Add the generated PDF as an attachment
                builder.Attachments.Add("Анализа_на_клиент.pdf", pdfBytes, new ContentType("application", "pdf"));

                // Add the additional document as an attachment
                var documentPath = Path.Combine(_webHostEnvironment.WebRootPath, "documents", "Informacii solasno clen 50ZSO.docx");
                if (System.IO.File.Exists(documentPath))
                {
                    var documentBytes = await System.IO.File.ReadAllBytesAsync(documentPath);
                    builder.Attachments.Add("Informacii_solasno_clen_50ZSO.docx", documentBytes, new ContentType("application", "vnd.openxmlformats-officedocument.wordprocessingml.document"));
                }

                message.Body = builder.ToMessageBody();

                using (var client = new SmtpClient())
                {
                    _logger.LogInformation("Connecting to SMTP server...");
                    await client.ConnectAsync(smtpServer, smtpPort, SecureSocketOptions.StartTls);
                    
                    _logger.LogInformation("Authenticating...");
                    await client.AuthenticateAsync(email, appCode);
                    
                    _logger.LogInformation("Sending email...");
                    await client.SendAsync(message);
                    await client.DisconnectAsync(true);
                    _logger.LogInformation("Email sent successfully");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending email with PDF to {Recipient}", recipientEmail);
                throw;
            }
        }

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("AnalizaNaKlient"))
            {
                return RedirectToAccessDenied();
            }

            // Initialize with 3 default insurers
            Input.Osiguriteli = new List<OsiguritelModel>
            {
                new OsiguritelModel(),
                new OsiguritelModel(),
                new OsiguritelModel()
            };

            // Load insurers (IDs 56-72)
            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();
                
                // Load insurers
                var query = @"
                    SELECT Id, Naziv 
                    FROM Klienti 
                    WHERE Id BETWEEN 56 AND 72 
                    ORDER BY Naziv";

                using (var command = new SqlCommand(query, connection))
                {
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            Osiguriteli.Add(new SelectListItem
                            {
                                Value = reader.GetInt64(0).ToString(),
                                Text = reader.GetString(1)
                            });
                        }
                    }
                }

                // Load risks with their types
                query = @"
                    SELECT Id, Naziv, Tip
                    FROM Rizici
                    ORDER BY Naziv";

                using (var command = new SqlCommand(query, connection))
                {
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            Rizici.Add(new SelectListItem
                            {
                                Value = reader.GetInt64(0).ToString(),
                                Text = reader.GetString(1),
                                Group = new SelectListGroup { Name = reader.IsDBNull(2) ? "Ризик" : reader.GetString(2) }
                            });
                        }
                    }
                }

                // Load products
                query = @"
                    SELECT Id, Ime, KlasaOsiguruvanjeId
                    FROM Produkti
                    ORDER BY Ime";

                using (var command = new SqlCommand(query, connection))
                {
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            Produkti.Add(new SelectListItem
                            {
                                Value = reader.GetInt32(0).ToString(),
                                Text = reader.GetString(1),
                                Group = new SelectListGroup { Name = reader.GetInt32(2).ToString() }
                            });
                        }
                    }
                }

                // Load classes
                query = @"
                    SELECT Id, KlasaBroj, KlasaIme
                    FROM KlasiOsiguruvanje
                    WHERE Disabled = 0
                    ORDER BY KlasaBroj";

                using (var command = new SqlCommand(query, connection))
                {
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            Klasi.Add(new SelectListItem
                            {
                                Value = reader.GetInt32(0).ToString(),
                                Text = $"{reader.GetInt32(1)} - {reader.GetString(2)}"
                            });
                        }
                    }
                }
            }

            return Page();
        }

        public async Task<IActionResult> OnGetSearchKlientiAsync(string mb)
        {
            var klienti = new List<dynamic>();
            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();
                var query = @"
                    SELECT TOP 10 
                        Id,
                        Ime,
                        Prezime,
                        EMBG,
                        MB,
                        EDB,
                        Naziv,
                        Email,
                        KlientFizickoPravnoLice as Tip,
                        UlicaZaKomunikacija,
                        BrojZaKomunikacija
                    FROM Klienti 
                    WHERE 
                        (MB LIKE @SearchTerm OR 
                         EDB LIKE @SearchTerm OR 
                         EMBG LIKE @SearchTerm OR 
                         Ime LIKE @SearchTerm OR 
                         Prezime LIKE @SearchTerm OR 
                         Naziv LIKE @SearchTerm)
                    ORDER BY 
                        CASE 
                            WHEN MB = @SearchTerm THEN 1
                            WHEN EDB = @SearchTerm THEN 2
                            WHEN EMBG = @SearchTerm THEN 3
                            WHEN Ime = @SearchTerm THEN 4
                            WHEN Prezime = @SearchTerm THEN 5
                            WHEN Naziv = @SearchTerm THEN 6
                            ELSE 7
                        END";

                using (var command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@SearchTerm", $"%{mb}%");
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            klienti.Add(new
                            {
                                id = reader.GetInt64(0),
                                ime = reader.IsDBNull(1) ? null : reader.GetString(1),
                                prezime = reader.IsDBNull(2) ? null : reader.GetString(2),
                                embg = reader.IsDBNull(3) ? null : reader.GetString(3),
                                mb = reader.IsDBNull(4) ? null : reader.GetString(4),
                                edb = reader.IsDBNull(5) ? null : reader.GetString(5),
                                naziv = reader.IsDBNull(6) ? null : reader.GetString(6),
                                email = reader.IsDBNull(7) ? null : reader.GetString(7),
                                tip = reader.GetString(8),
                                ulicaZaKomunikacija = reader.IsDBNull(9) ? null : reader.GetString(9),
                                brojZaKomunikacija = reader.IsDBNull(10) ? null : reader.GetString(10)
                            });
                        }
                    }
                }
            }
            return new JsonResult(klienti);
        }

        public async Task<IActionResult> OnPostAsync()
        {
            Console.WriteLine("Starting OnPostAsync method");
            
            if (!await HasPageAccess("AnalizaNaKlient"))
            {
                Console.WriteLine("Access denied to AnalizaNaKlient");
                return RedirectToAccessDenied();
            }

            Console.WriteLine("ModelState.IsValid: " + ModelState.IsValid);
            if (!ModelState.IsValid)
            {
                Console.WriteLine("ModelState errors:");
                foreach (var modelError in ModelState.Values.SelectMany(v => v.Errors))
                {
                    Console.WriteLine($"- {modelError.ErrorMessage}");
                }
                return Page();
            }

            Console.WriteLine($"SelectedKlasaId: {Input?.SelectedKlasaId}");
            if (Input?.SelectedKlasaId == null || Input.SelectedKlasaId == 0)
            {
                Console.WriteLine("Missing SelectedKlasaId");
                ModelState.AddModelError("Input.SelectedKlasaId", "Изберете класа");
                return Page();
            }

            Console.WriteLine($"SelectedProduktId: {Input?.SelectedProduktId}");
            if (Input?.SelectedProduktId == null || Input.SelectedProduktId == 0)
            {
                Console.WriteLine("Missing SelectedProduktId");
                ModelState.AddModelError("Input.SelectedProduktId", "Изберете продукт");
                return Page();
            }

            Console.WriteLine($"Osiguriteli count: {Input?.Osiguriteli?.Count ?? 0}");
            if (Input?.Osiguriteli == null || Input.Osiguriteli.Count < 2)
            {
                Console.WriteLine("Insufficient Osiguriteli");
                ModelState.AddModelError("Input.Osiguriteli", "Изберете најмалку два осигурители");
                return Page();
            }

            Console.WriteLine($"Kombinacii count: {Input?.Kombinacii?.Count ?? 0}");
            if (Input?.Kombinacii == null || Input.Kombinacii.Count == 0)
            {
                Console.WriteLine("No Kombinacii");
                ModelState.AddModelError("Input.Kombinacii", "Изберете најмалку еден ризик");
                return Page();
            }

            try
            {
                Console.WriteLine("Opening database connection");
                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();
                    Console.WriteLine("Starting database transaction");
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // Insert into AnalizaNaKlient
                            var insertAnalizaSql = @"
                                INSERT INTO AnalizaNaKlient (
                                    DateCreated, UsernameCreated, KlientiIdAnalizaDogovoruvac,
                                    Ime, Prezime, EMBG, Naziv, Email, ObrazlozenieZaPredlog,
                                    EDB, MB, Adresa, RiziciOsiguritel, ProvizijaZaSiteProduktiIOsiguriteli
                                ) VALUES (
                                    @DateCreated, @UsernameCreated, @KlientiIdAnalizaDogovoruvac,
                                    @Ime, @Prezime, @EMBG, @Naziv, @Email, @ObrazlozenieZaPredlog,
                                    @EDB, @MB, @Adresa, @RiziciOsiguritel, @ProvizijaZaSiteProduktiIOsiguriteli
                                );
                                SELECT SCOPE_IDENTITY();";

                            Console.WriteLine("Executing AnalizaNaKlient insert");
                            long analizaId;
                            using (var command = new SqlCommand(insertAnalizaSql, connection, transaction))
                            {
                                command.Parameters.AddWithValue("@DateCreated", DateTime.Now);
                                command.Parameters.AddWithValue("@UsernameCreated", HttpContext.Session.GetString("Username") ?? "System");
                                command.Parameters.AddWithValue("@KlientiIdAnalizaDogovoruvac", (object)Input.KlientiIdAnalizaDogovoruvac ?? DBNull.Value);
                                command.Parameters.AddWithValue("@Ime", (object)Input.Ime ?? DBNull.Value);
                                command.Parameters.AddWithValue("@Prezime", (object)Input.Prezime ?? DBNull.Value);
                                command.Parameters.AddWithValue("@EMBG", (object)Input.EMBG ?? DBNull.Value);
                                command.Parameters.AddWithValue("@Naziv", (object)Input.Naziv ?? DBNull.Value);
                                command.Parameters.AddWithValue("@Email", (object)Input.Email ?? DBNull.Value);
                                command.Parameters.AddWithValue("@ObrazlozenieZaPredlog", (object)Input.ObrazlozenieZaPredlog ?? DBNull.Value);
                                command.Parameters.AddWithValue("@EDB", (object)Input.EDB ?? DBNull.Value);
                                command.Parameters.AddWithValue("@MB", (object)Input.MB ?? DBNull.Value);
                                command.Parameters.AddWithValue("@Adresa", (object)Input.Adresa ?? DBNull.Value);
                                command.Parameters.AddWithValue("@RiziciOsiguritel", (object)Input.RiziciOsiguritel ?? DBNull.Value);
                                command.Parameters.AddWithValue("@ProvizijaZaSiteProduktiIOsiguriteli", (object)Input.ProvizijaZaSiteProduktiIOsiguriteli ?? DBNull.Value);

                                var result = await command.ExecuteScalarAsync();
                                Console.WriteLine($"AnalizaNaKlient insert result: {result}");
                                analizaId = Convert.ToInt64(result);
                            }

                            // Handle insurers
                            Console.WriteLine($"Processing {Input.Osiguriteli?.Count ?? 0} insurers");
                            if (Input.Osiguriteli != null && Input.Osiguriteli.Any())
                            {
                                foreach (var osiguritel in Input.Osiguriteli.Where(o => o.Id.HasValue))
                                {
                                    Console.WriteLine($"Processing insurer ID: {osiguritel.Id}");
                                    
                                    // Get insurer name
                                    var insurerName = "";
                                    var getInsurerNameSql = "SELECT Naziv FROM Klienti WHERE Id = @Id";
                                    using (var command = new SqlCommand(getInsurerNameSql, connection, transaction))
                                    {
                                        command.Parameters.AddWithValue("@Id", osiguritel.Id.Value);
                                        var result = await command.ExecuteScalarAsync();
                                        if (result != null)
                                        {
                                            insurerName = result.ToString();
                                            Console.WriteLine($"Found insurer name: {insurerName}");
                                        }
                                    }

                                    // Get AnalizaOsiguritelPravnoEkonomskiOdnosiId
                                    long? pravnoEkonomskiId = null;
                                    var getPravnoEkonomskiIdSql = @"
                                        SELECT TOP 1 Id FROM AnalizaOsiguritelPravnoEkonomskiOdnosi
                                        WHERE KlientiIdAnalizaDogovoruvac = @InsurerId";
                                    using (var command = new SqlCommand(getPravnoEkonomskiIdSql, connection, transaction))
                                    {
                                        command.Parameters.AddWithValue("@InsurerId", osiguritel.Id.Value);
                                        var result = await command.ExecuteScalarAsync();
                                        if (result != null && result != DBNull.Value)
                                        {
                                            pravnoEkonomskiId = Convert.ToInt64(result);
                                            Console.WriteLine($"Found pravnoEkonomskiId: {pravnoEkonomskiId}");
                                        }
                                    }

                                    // Insert into AnalizaNaKlientOsiguriteli
                                    Console.WriteLine("Inserting into AnalizaNaKlientOsiguriteli");
                                    var insertOsiguriteliSql = @"
                                        INSERT INTO AnalizaNaKlientOsiguriteli (
                                            OsiguritelNaziv, PrivizijaZaPredlogPonuda,
                                            PremijaIznos, AnalizaNaKlientId, AnalizaOsiguritelPravnoEkonomskiOdnosiId
                                        ) VALUES (
                                            @OsiguritelNaziv, @PrivizijaZaPredlogPonuda,
                                            @PremijaIznos, @AnalizaNaKlientId, @AnalizaOsiguritelPravnoEkonomskiOdnosiId
                                        )";

                                    using (var command = new SqlCommand(insertOsiguriteliSql, connection, transaction))
                                    {
                                        command.Parameters.AddWithValue("@OsiguritelNaziv", insurerName);
                                        command.Parameters.AddWithValue("@PrivizijaZaPredlogPonuda", (object)osiguritel.PrivizijaZaPredlogPonuda ?? DBNull.Value);
                                        command.Parameters.AddWithValue("@PremijaIznos", (object)osiguritel.PremijaIznos ?? DBNull.Value);
                                        command.Parameters.AddWithValue("@AnalizaNaKlientId", analizaId);
                                        command.Parameters.AddWithValue("@AnalizaOsiguritelPravnoEkonomskiOdnosiId", (object)pravnoEkonomskiId ?? DBNull.Value);

                                        await command.ExecuteNonQueryAsync();
                                        Console.WriteLine("Successfully inserted into AnalizaNaKlientOsiguriteli");
                                    }
                                }
                            }

                            // Handle risks
                            Console.WriteLine($"Processing {Input.Kombinacii?.Count ?? 0} risks");
                            if (Input.Kombinacii != null && Input.Kombinacii.Any())
                            {
                                var insertRisksSql = @"
                                    INSERT INTO AnalizaNaKlientRizici (
                                        RizikIdRizici, KlientiIdAnalizaDogovoruvac,
                                        Pokrieno, Beleshka
                                    ) VALUES (
                                        @RizikIdRizici, @KlientiIdAnalizaDogovoruvac,
                                        @Pokrieno, @Beleshka
                                    )";

                                foreach (var kombinacija in Input.Kombinacii)
                                {
                                    Console.WriteLine($"Inserting risk: RizikId={kombinacija.RizikIdRizici}, KlientiId={kombinacija.KlientiIdAnalizaDogovoruvac}");
                                    using (var command = new SqlCommand(insertRisksSql, connection, transaction))
                                    {
                                        command.Parameters.AddWithValue("@RizikIdRizici", kombinacija.RizikIdRizici);
                                        command.Parameters.AddWithValue("@KlientiIdAnalizaDogovoruvac", kombinacija.KlientiIdAnalizaDogovoruvac);
                                        command.Parameters.AddWithValue("@Pokrieno", kombinacija.Pokrieno);
                                        command.Parameters.AddWithValue("@Beleshka", (object)kombinacija.Beleshka ?? DBNull.Value);

                                        await command.ExecuteNonQueryAsync();
                                        Console.WriteLine("Successfully inserted risk");
                                    }
                                }
                            }

                            Console.WriteLine("Committing transaction");
                            transaction.Commit();

                            TempData["SuccessMessage"] = "Анализата е успешно зачувана.";
                            return RedirectToPage();
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error during database operations: {ex.Message}");
                            Console.WriteLine($"Stack trace: {ex.StackTrace}");
                            transaction.Rollback();
                            _logger.LogError(ex, "Error saving analysis");
                            ModelState.AddModelError("", "Грешка при зачувување на анализата. Ве молиме обидете се повторно.");
                            return Page();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in OnPostAsync: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                _logger.LogError(ex, "Error saving analysis");
                ModelState.AddModelError("", "Грешка при зачувување на анализата. Ве молиме обидете се повторно.");
                return Page();
            }
        }

        public async Task<IActionResult> OnPostAddKombinacijaAsync(long rizikId, long klientId, bool pokrieno, string beleshka)
        {
            if (!await HasPageAccess("AnalizaNaKlient"))
            {
                return RedirectToAccessDenied();
            }

            try
            {
                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();

                    // Delete existing records for this insurer
                    var deleteSql = @"
                        DELETE FROM AnalizaNaKlientRizici
                        WHERE KlientiIdAnalizaDogovoruvac = @KlientId";

                    using (var cmd = new SqlCommand(deleteSql, connection))
                    {
                        cmd.Parameters.AddWithValue("@KlientId", klientId);
                        await cmd.ExecuteNonQueryAsync();
                    }

                    // Insert new combination
                    var insertSql = @"
                        INSERT INTO AnalizaNaKlientRizici (RizikIdRizici, KlientiIdAnalizaDogovoruvac, Pokrieno, Beleshka)
                        VALUES (@RizikId, @KlientId, @Pokrieno, @Beleshka)";

                    using (var cmd = new SqlCommand(insertSql, connection))
                    {
                        cmd.Parameters.AddWithValue("@RizikId", rizikId);
                        cmd.Parameters.AddWithValue("@KlientId", klientId);
                        cmd.Parameters.AddWithValue("@Pokrieno", pokrieno);
                        cmd.Parameters.AddWithValue("@Beleshka", (object)beleshka ?? DBNull.Value);

                        await cmd.ExecuteNonQueryAsync();
                        return new JsonResult(new { success = true });
                    }
                }
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = ex.Message });
            }
        }

        public async Task<IActionResult> OnGetCompareInsurersAsync([FromQuery] string[] insurerIds, [FromQuery] string[] riskIds)
        {
            try
            {
                if (insurerIds == null || insurerIds.Length == 0)
                {
                    return new JsonResult(new { success = false, message = "No insurers selected" });
                }

                // Risks are optional now
                if (riskIds == null || riskIds.Length == 0)
                {
                    // Return empty comparison data if no risks selected
                    return new JsonResult(new { success = true, data = new List<dynamic>() });
                }

                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();

                    // First, let's verify the data exists in the tables
                    var debugSql = @"
                        -- Check if insurers exist
                        SELECT Id, Naziv FROM Klienti WHERE Id IN (SELECT CAST(value AS bigint) FROM STRING_SPLIT(@InsurerIds, ','));
                        
                        -- Check if risks exist
                        SELECT Id, Naziv FROM Rizici WHERE Id IN (SELECT CAST(value AS bigint) FROM STRING_SPLIT(@RiskIds, ','));
                        
                        -- Check if any combinations exist
                        SELECT COUNT(*) FROM AnalizaNaKlientRizici 
                        WHERE KlientiIdAnalizaDogovoruvac IN (SELECT CAST(value AS bigint) FROM STRING_SPLIT(@InsurerIds, ','))
                        AND RizikIdRizici IN (SELECT CAST(value AS bigint) FROM STRING_SPLIT(@RiskIds, ','));";

                    using (var cmd = new SqlCommand(debugSql, connection))
                    {
                        cmd.Parameters.AddWithValue("@InsurerIds", string.Join(",", insurerIds));
                        cmd.Parameters.AddWithValue("@RiskIds", string.Join(",", riskIds));

                        Console.WriteLine("Debug Query Results:");
                        using (var reader = await cmd.ExecuteReaderAsync())
                        {
                            Console.WriteLine("\nSelected Insurers:");
                            while (await reader.ReadAsync())
                            {
                                Console.WriteLine($"ID: {reader.GetInt64(0)}, Name: {reader.GetString(1)}");
                            }

                            await reader.NextResultAsync();
                            Console.WriteLine("\nSelected Risks:");
                            while (await reader.ReadAsync())
                            {
                                Console.WriteLine($"ID: {reader.GetInt64(0)}, Name: {reader.GetString(1)}");
                            }

                            await reader.NextResultAsync();
                            if (await reader.ReadAsync())
                            {
                                Console.WriteLine($"\nExisting Combinations: {reader.GetInt32(0)}");
                            }
                        }
                    }

                    // Now the actual comparison query
                    var sql = @"
                        WITH AllCombinations AS (
                            SELECT 
                                r.Id as RizikId,
                                r.Naziv as RizikNaziv,
                                k.Id as KlientId,
                                k.Naziv as KlientNaziv
                            FROM Rizici r
                            CROSS JOIN Klienti k
                            WHERE k.Id IN (SELECT CAST(value AS bigint) FROM STRING_SPLIT(@InsurerIds, ','))
                            AND r.Id IN (SELECT CAST(value AS bigint) FROM STRING_SPLIT(@RiskIds, ','))
                        )
                        SELECT 
                            ac.RizikNaziv as RiskName,
                            ac.KlientNaziv as InsurerName,
                            ISNULL(ark.Pokrieno, 0) as Pokrieno,
                            ark.Beleshka
                        FROM AllCombinations ac
                        LEFT JOIN AnalizaNaKlientRizici ark ON 
                            ark.RizikIdRizici = ac.RizikId AND 
                            ark.KlientiIdAnalizaDogovoruvac = ac.KlientId
                        ORDER BY ac.RizikNaziv, ac.KlientNaziv";

                    var comparisonData = new List<dynamic>();
                    using (var cmd = new SqlCommand(sql, connection))
                    {
                        cmd.Parameters.AddWithValue("@InsurerIds", string.Join(",", insurerIds));
                        cmd.Parameters.AddWithValue("@RiskIds", string.Join(",", riskIds));

                        Console.WriteLine("\nExecuting comparison query with parameters:");
                        Console.WriteLine($"InsurerIds: {string.Join(",", insurerIds)}");
                        Console.WriteLine($"RiskIds: {string.Join(",", riskIds)}");

                        using (var reader = await cmd.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                var item = new
                                {
                                    riskName = reader.GetString(0),
                                    insurerName = reader.GetString(1),
                                    pokrieno = !reader.IsDBNull(2) && reader.GetBoolean(2),
                                    beleshka = reader.IsDBNull(3) ? null : reader.GetString(3)
                                };
                                comparisonData.Add(item);
                                Console.WriteLine($"Found: Risk={item.riskName}, Insurer={item.insurerName}, Covered={item.pokrieno}");
                            }
                        }
                    }

                    return new JsonResult(new { success = true, data = comparisonData });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in OnGetCompareInsurersAsync: {ex.Message}");
                Console.WriteLine($"Stack Trace: {ex.StackTrace}");
                return new JsonResult(new { success = false, message = ex.Message });
            }
        }

        public async Task<IActionResult> OnPostAddRizikAsync([FromBody] System.Text.Json.JsonElement data)
        {
            if (!await HasPageAccess("AnalizaNaKlient"))
            {
                return RedirectToAccessDenied();
            }

            try
            {
                string naziv = data.GetProperty("naziv").GetString();
                string tip = data.GetProperty("tip").GetString();
                string produktId = data.GetProperty("produktId").GetString();

                if (string.IsNullOrEmpty(naziv))
                {
                    return new JsonResult(new { success = false, message = "Називот на ризикот е задолжителен" });
                }

                if (string.IsNullOrEmpty(tip))
                {
                    return new JsonResult(new { success = false, message = "Типот на ризикот е задолжителен" });
                }

                if (string.IsNullOrEmpty(produktId))
                {
                    return new JsonResult(new { success = false, message = "Продуктот е задолжителен" });
                }

                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();

                    // Check if risk with same name exists
                    var checkSql = "SELECT COUNT(*) FROM Rizici WHERE Naziv = @Naziv";
                    using (var cmd = new SqlCommand(checkSql, connection))
                    {
                        cmd.Parameters.AddWithValue("@Naziv", naziv);
                        var exists = (int)await cmd.ExecuteScalarAsync() > 0;
                        if (exists)
                        {
                            return new JsonResult(new { success = false, message = "Ризик со ист назив веќе постои" });
                        }
                    }

                    // Insert new risk
                    var sql = @"
                        INSERT INTO Rizici (Naziv, Tip, ProduktId)
                        OUTPUT INSERTED.Id, INSERTED.Naziv
                        VALUES (@Naziv, @Tip, @ProduktId)";

                    using (var cmd = new SqlCommand(sql, connection))
                    {
                        cmd.Parameters.AddWithValue("@Naziv", naziv);
                        cmd.Parameters.AddWithValue("@Tip", tip);
                        cmd.Parameters.AddWithValue("@ProduktId", produktId);
                        
                        using (var reader = await cmd.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                return new JsonResult(new { 
                                    success = true, 
                                    id = reader.GetInt64(0).ToString(),
                                    naziv = reader.GetString(1)
                                });
                            }
                        }
                    }
                }

                return new JsonResult(new { success = false, message = "Грешка при зачувување на ризик" });
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = ex.Message });
            }
        }

        public async Task<IActionResult> OnGetCheckKombinacijaAsync(long rizikId, long klientId)
        {
            try
            {
                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();

                    var sql = @"
                        SELECT COUNT(*)
                        FROM AnalizaNaKlientRizici
                        WHERE RizikIdRizici = @RizikId 
                        AND KlientiIdAnalizaDogovoruvac = @KlientId";

                    using (var cmd = new SqlCommand(sql, connection))
                    {
                        cmd.Parameters.AddWithValue("@RizikId", rizikId);
                        cmd.Parameters.AddWithValue("@KlientId", klientId);

                        var count = (int)await cmd.ExecuteScalarAsync();
                        return new JsonResult(new { exists = count > 0 });
                    }
                }
            }
            catch (Exception ex)
            {
                return new JsonResult(new { exists = false, error = ex.Message });
            }
        }

        public async Task<IActionResult> OnPostGeneratePdfAsync([FromBody] PdfRequestModel request)
        {
            if (!await HasPageAccess("AnalizaNaKlient"))
            {
                return RedirectToAccessDenied();
            }

            try
            {
                // Internal implementation for when called directly from the UI - default to not sending email
                return await GeneratePdfInternalAsync(request, sendEmail: false);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error generating PDF: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                return new JsonResult(new { success = false, message = ex.Message });
            }
        }

        // Internal method that handles the PDF generation with an option to send email
        private async Task<IActionResult> GeneratePdfInternalAsync(PdfRequestModel request, bool sendEmail)
        {
            try
            {
                // Debug logging
                Console.WriteLine($"Received PDF request with {request.InsurerIds?.Count ?? 0} insurers and {request.RiskIds?.Count ?? 0} risks");
                Console.WriteLine($"Insurer IDs: {string.Join(", ", request.InsurerIds ?? new List<long>())}");
                Console.WriteLine($"Risk IDs: {string.Join(", ", request.RiskIds ?? new List<long>())}");
                Console.WriteLine($"Send Email: {sendEmail}");

                if (request.InsurerIds == null || request.InsurerIds.Count < 2)
                {
                    return new JsonResult(new { success = false, message = "Потребни се најмалку два осигурители" });
                }

                // Risks are now optional - no validation needed

                // Get insurer names and data from database
                var insurerNames = new Dictionary<long, string>();
                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();
                    if (request.InsurerIds.Any())
                    {
                        var query = "SELECT Id, Naziv FROM Klienti WHERE Id IN (" + string.Join(",", request.InsurerIds) + ")";
                        using (var command = new SqlCommand(query, connection))
                        {
                            using (var reader = await command.ExecuteReaderAsync())
                            {
                                while (await reader.ReadAsync())
                                {
                                    insurerNames[reader.GetInt64(0)] = reader.GetString(1);
                                }
                            }
                        }
                    }
                }

                // Update request with insurer names
                if (request.AdditionalData.Insurers == null)
                {
                    request.AdditionalData.Insurers = new List<InsurerData>();
                }

                // If Insurers list is empty, populate it from the insurerNames
                if (!request.AdditionalData.Insurers.Any())
                {
                    foreach (var insurerId in request.InsurerIds)
                    {
                        if (insurerNames.ContainsKey(insurerId))
                        {
                            request.AdditionalData.Insurers.Add(new InsurerData
                            {
                                Name = insurerNames[insurerId],
                                PravnoEkonomskiOdnosi = request.AdditionalData.PravnoEkonomskiOdnosi,
                                PrivizijaZaPredlogPonuda = request.AdditionalData.PrivizijaZaPredlogPonuda,
                                ProvizijaZaSiteProdukti = request.AdditionalData.ProvizijaZaSiteProdukti,
                                PremijaIznos = request.AdditionalData.PremijaIznos,
                                RiziciOsiguritel = request.AdditionalData.RiziciOsiguritel  // Use the value from AdditionalData
                            });
                        }
                    }
                }
                else
                {
                    // Update names in existing Insurers list by matching IDs
                    foreach (var insurer in request.AdditionalData.Insurers)
                    {
                        if (insurerNames.ContainsKey(insurer.Id))
                        {
                            insurer.Name = insurerNames[insurer.Id];
                        }
                    }
                }

                // Get the comparison data
                var comparisonData = await GetComparisonDataAsync(request.InsurerIds, request.RiskIds);

                // Debug logging
                Console.WriteLine($"Retrieved {comparisonData.Insurers.Count} insurers and {comparisonData.Risks.Count} risks");
                foreach (var insurer in comparisonData.Insurers)
                {
                    Console.WriteLine($"Insurer: {insurer}");
                }
                foreach (var risk in comparisonData.Risks)
                {
                    Console.WriteLine($"Risk: {risk}");
                }

                // Register custom encoding and font
                var fontPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "fonts", "arial.ttf");
                if (!System.IO.File.Exists(fontPath))
                {
                    return new JsonResult(new { success = false, message = "Font file not found" });
                }

                // Register the font with iTextSharp
                var baseFont = BaseFont.CreateFont(fontPath, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
                var titleFont = new Font(baseFont, 18, Font.BOLD);
                var headerFont = new Font(baseFont, 11, Font.BOLD);
                var cellFont = new Font(baseFont, 10, Font.NORMAL);
                var contentFont = new Font(baseFont, 11, Font.NORMAL);
                var sectionFont = new Font(baseFont, 12, Font.BOLD);

                // Create PDF document
                var doc = new Document(PageSize.A4.Rotate(), 50, 50, 25, 25);
                var memoryStream = new MemoryStream();
                var writer = PdfWriter.GetInstance(doc, memoryStream);

                doc.Open();

                // Add title
                var title = new Paragraph("Споредба на осигурители", titleFont);
                title.Alignment = Element.ALIGN_CENTER;
                title.SpacingAfter = 30f;
                doc.Add(title);

                // Define colors (used in multiple places)
                var headerBgColor = new BaseColor(248, 249, 250); // #f8f9fa
                var borderColor = new BaseColor(222, 226, 230); // #dee2e6
                var successColor = new BaseColor(40, 167, 69); // Bootstrap success color
                var dangerColor = new BaseColor(220, 53, 69); // Bootstrap danger color

                // Only create comparison table if there are risks selected
                if (comparisonData.Risks.Count > 0)
                {
                    // Create table for comparison
                    var table = new PdfPTable(comparisonData.Insurers.Count + 1);
                    table.WidthPercentage = 100;
                    table.SpacingBefore = 10f;
                    table.SpacingAfter = 30f;

                    // Set relative column widths
                    float[] columnWidths = new float[comparisonData.Insurers.Count + 1];
                    columnWidths[0] = 3f; // Risk column wider
                    for (int i = 1; i < columnWidths.Length; i++)
                    {
                        columnWidths[i] = 2f; // Insurer columns
                    }
                    table.SetWidths(columnWidths);

                    // Add headers
                    var riskHeader = new PdfPCell(new Phrase("Ризик", headerFont));
                    riskHeader.BackgroundColor = headerBgColor;
                    riskHeader.HorizontalAlignment = Element.ALIGN_LEFT;
                    riskHeader.VerticalAlignment = Element.ALIGN_MIDDLE;
                    riskHeader.Padding = 10f;
                    riskHeader.BorderColor = borderColor;
                    table.AddCell(riskHeader);

                    foreach (var insurer in comparisonData.Insurers)
                    {
                        var insurerHeader = new PdfPCell(new Phrase(insurer, headerFont));
                        insurerHeader.BackgroundColor = headerBgColor;
                        insurerHeader.HorizontalAlignment = Element.ALIGN_CENTER;
                        insurerHeader.VerticalAlignment = Element.ALIGN_MIDDLE;
                        insurerHeader.Padding = 10f;
                        insurerHeader.BorderColor = borderColor;
                        table.AddCell(insurerHeader);
                    }

                    // Add data rows
                    foreach (var risk in comparisonData.Risks)
                    {
                        // Risk name cell
                        var riskCell = new PdfPCell(new Phrase(risk, cellFont));
                        riskCell.HorizontalAlignment = Element.ALIGN_LEFT;
                        riskCell.VerticalAlignment = Element.ALIGN_MIDDLE;
                        riskCell.PaddingLeft = 15f;
                        riskCell.PaddingTop = 10f;
                        riskCell.PaddingBottom = 10f;
                        riskCell.BorderColor = borderColor;
                        table.AddCell(riskCell);

                        // Coverage cells for each insurer
                        foreach (var insurer in comparisonData.Insurers)
                        {
                            var coverage = comparisonData.GetCoverage(risk, insurer);
                            var textColor = coverage.Pokrieno ? successColor : dangerColor;

                            var phrase = new Phrase();

                            // Add symbol using ZapfDingbats
                            var zapfFont = new Font(Font.FontFamily.ZAPFDINGBATS, 12, Font.NORMAL, textColor);
                            phrase.Add(new Chunk(coverage.Pokrieno ? "4" : "8", zapfFont)); // 4 is checkmark, 8 is X in ZapfDingbats

                            // Add space and text
                            phrase.Add(new Chunk(" ", new Font(baseFont, 6, Font.NORMAL))); // small space
                            phrase.Add(new Chunk(coverage.Pokrieno ? "Да" : "Не", new Font(baseFont, 10, Font.NORMAL, textColor)));

                            if (!string.IsNullOrEmpty(coverage.Beleshka))
                            {
                                phrase.Add(new Chunk("\n(" + coverage.Beleshka + ")", new Font(baseFont, 9, Font.NORMAL, BaseColor.GRAY)));
                            }

                            var coverageCell = new PdfPCell(phrase);
                            coverageCell.HorizontalAlignment = Element.ALIGN_CENTER;
                            coverageCell.VerticalAlignment = Element.ALIGN_MIDDLE;
                            coverageCell.PaddingTop = 10f;
                            coverageCell.PaddingBottom = 10f;
                            coverageCell.BorderColor = borderColor;
                            table.AddCell(coverageCell);
                        }
                    }

                    doc.Add(table);
                }
                else
                {
                    // Add a message when no risks are selected
                    var noRisksMessage = new Paragraph(new Phrase("Не се избрани ризици за споредба.", cellFont));
                    noRisksMessage.SpacingBefore = 10f;
                    noRisksMessage.SpacingAfter = 30f;
                    noRisksMessage.Alignment = Element.ALIGN_CENTER;
                    doc.Add(noRisksMessage);
                }

                // Add a line break after the table
                doc.Add(new Paragraph("\n"));

                // Add additional information section title
                var additionalInfoTitle = new Paragraph("Дополнителни информации", titleFont);
                additionalInfoTitle.Alignment = Element.ALIGN_LEFT;
                additionalInfoTitle.SpacingBefore = 20f;
                additionalInfoTitle.SpacingAfter = 15f;
                doc.Add(additionalInfoTitle);

                // Add insurer-specific information
                if (request.AdditionalData.Insurers != null && request.AdditionalData.Insurers.Any())
                {
                    foreach (var insurer in request.AdditionalData.Insurers)
                    {
                        Console.WriteLine($"\nProcessing insurer data for PDF:");
                        Console.WriteLine($"Name: {insurer.Name}");
                        Console.WriteLine($"PravnoEkonomskiOdnosi: {insurer.PravnoEkonomskiOdnosi}");
                        Console.WriteLine($"PrivizijaZaPredlogPonuda: {insurer.PrivizijaZaPredlogPonuda}");
                        Console.WriteLine($"ProvizijaZaSiteProdukti: {insurer.ProvizijaZaSiteProdukti}");
                        Console.WriteLine($"PremijaIznos: {insurer.PremijaIznos}");
                        Console.WriteLine($"RiziciOsiguritel: {insurer.RiziciOsiguritel}");

                        // Create insurer title with explicit font encoding
                        var insurerTitlePhrase = new Phrase(new Chunk(insurer.Name ?? "-", sectionFont));
                        var insurerTitle = new Paragraph(insurerTitlePhrase);
                        insurerTitle.SpacingBefore = 15f;
                        insurerTitle.SpacingAfter = 10f;
                        doc.Add(insurerTitle);

                        // Create a table for insurer-specific information
                        var insurerTable = new PdfPTable(2);
                        insurerTable.WidthPercentage = 100;
                        insurerTable.SetWidths(new float[] { 1.5f, 3f });

                        void AddInsurerInfoRow(string label, string content)
                        {
                            // Label cell with explicit font encoding
                            var labelChunk = new Chunk(label, sectionFont);
                            var labelCell = new PdfPCell(new Phrase(labelChunk));
                            labelCell.BorderColor = borderColor;
                            labelCell.BackgroundColor = headerBgColor;
                            labelCell.Padding = 8f;
                            labelCell.MinimumHeight = 30f;
                            insurerTable.AddCell(labelCell);

                            // Content cell with explicit font encoding and null check
                            string displayContent = content;
                            if (string.IsNullOrWhiteSpace(displayContent))
                            {
                                displayContent = "-";
                            }
                            var contentChunk = new Chunk(displayContent, contentFont);
                            var contentCell = new PdfPCell(new Phrase(contentChunk));
                            contentCell.BorderColor = borderColor;
                            contentCell.Padding = 8f;
                            contentCell.MinimumHeight = 30f;
                            insurerTable.AddCell(contentCell);
                        }

                        // Add insurer-specific fields with explicit content handling
                        AddInsurerInfoRow("Правно-економски односи со осигурител:", 
                            insurer.PravnoEkonomskiOdnosi);
                        AddInsurerInfoRow("Провизија за предлог понуда:", 
                            insurer.PrivizijaZaPredlogPonuda);
                        AddInsurerInfoRow("Премија износ:", 
                            insurer.PremijaIznos.HasValue ? 
                            insurer.PremijaIznos.Value.ToString("N2") + " ден." : 
                            null);
                        // Removed RiziciOsiguritel field from per-insurer section

                        doc.Add(insurerTable);
                        doc.Add(new Paragraph("\n"));
                    }
                }
                else
                {
                    Console.WriteLine("No insurers found in request.AdditionalData.Insurers");
                }

                // Add a line break before final information
                doc.Add(new Paragraph("\n"));

                // Add final information (ObrazlozenieZaPredlog)
                var finalInfoTable = new PdfPTable(2);
                finalInfoTable.WidthPercentage = 100;
                finalInfoTable.SetWidths(new float[] { 1.5f, 3f });

                void AddFinalInfoRow(string label, string content)
                {
                    // Label cell
                    var labelCell = new PdfPCell(new Phrase(label, sectionFont));
                    labelCell.BorderColor = borderColor;
                    labelCell.BackgroundColor = headerBgColor;
                    labelCell.Padding = 8f;
                    labelCell.MinimumHeight = 30f;
                    finalInfoTable.AddCell(labelCell);

                    // Content cell
                    var contentCell = new PdfPCell(new Phrase(content ?? "-", contentFont));
                    contentCell.BorderColor = borderColor;
                    contentCell.Padding = 8f;
                    contentCell.MinimumHeight = 30f;
                    finalInfoTable.AddCell(contentCell);
                }

                // Add Ризици за предмет на осигурување as a global field
                Console.WriteLine($"Debug - Before adding RiziciOsiguritel to PDF:");
                Console.WriteLine($"Debug - request.AdditionalData.RiziciOsiguritel value: '{request.AdditionalData.RiziciOsiguritel}'");
                Console.WriteLine($"Debug - request.AdditionalData.RiziciOsiguritel is null: {request.AdditionalData.RiziciOsiguritel == null}");
                Console.WriteLine($"Debug - request.AdditionalData.RiziciOsiguritel is empty: {string.IsNullOrEmpty(request.AdditionalData.RiziciOsiguritel)}");
                Console.WriteLine($"Debug - request.AdditionalData.RiziciOsiguritel length: {request.AdditionalData.RiziciOsiguritel?.Length ?? 0}");

                var riziciValue = !string.IsNullOrEmpty(request.AdditionalData.RiziciOsiguritel) ? 
                    request.AdditionalData.RiziciOsiguritel : "-";
                Console.WriteLine($"Debug - Final riziciValue to be added to PDF: '{riziciValue}'");

                AddFinalInfoRow("Ризици за предмет на осигурување:", riziciValue);

                // Add ProvizijaZaSiteProdukti field
                AddFinalInfoRow("Провизија за сите производи и осигурители:", 
                    "https://inco.com.mk/ProvizijaOsiguriteli");

                // Add a line break
                doc.Add(new Paragraph("\n"));

                // Add ObrazlozenieZaPredlog
                AddFinalInfoRow("Образложение за предлог:", 
                    request.AdditionalData.ObrazlozenieZaPredlog);

                doc.Add(finalInfoTable);
                // Add a line break before declaration
                doc.Add(new Paragraph("\n\n"));

                // Always start Izjava on a new page
                doc.NewPage();

                // Add declaration title
                var declarationTitle = new Paragraph("Изјава", sectionFont);
                declarationTitle.SpacingAfter = 15f;
                doc.Add(declarationTitle);

                // Create declaration text based on whether it's a person or company
                string declarationText;
                if (!string.IsNullOrEmpty(request.AdditionalData.Naziv))
                {
                    // Company declaration
                    declarationText = $"Јас __________________(Име, презиме), ЕМБГ____________________, адреса " +
                        $"во својство на застапник на {request.AdditionalData.Naziv} (Назив на правно лице), " +
                        "изјавувам дека ОБД ИНКО АД Скопје, со дозвола за вршење осигурително брокерски работи број УП 14-1-16, " +
                        "издадена на 14.03.2025 година го имам овластено да посредува во мое име и моја сметка за склучување на полиси " +
                        "за осигурување од сите класи, ги примив и разбрав информациите од член 50 согласно Закон за супервизија во осигурување, " +
                        "информиран/а сум за провизијата која ја прима Осигурително Брокерското Друштво од Осигурителите, " +
                        "ја примив и разбрав анализата за осигурително покрите и сум согласен со предложената понуда од ОБД ИНКО АД Скопјe, " +
                        "се согласувам за склучување на договор за осигурување.";
                }
                else if (!string.IsNullOrEmpty(request.AdditionalData.Ime) || !string.IsNullOrEmpty(request.AdditionalData.Prezime))
                {
                    // Personal declaration
                    var nameField = !string.IsNullOrEmpty(request.AdditionalData.Ime) && !string.IsNullOrEmpty(request.AdditionalData.Prezime) ?
                        $"{request.AdditionalData.Ime} {request.AdditionalData.Prezime}" :
                        "__________________";
                    
                    declarationText = $"Јас {nameField} (Име, презиме), " +
                        $"ЕМБГ{(string.IsNullOrEmpty(request.AdditionalData.EMBG) ? "__________________" : request.AdditionalData.EMBG)}, " +
                        $"адреса {(string.IsNullOrEmpty(request.AdditionalData.Adresa) ? "_____________________" : request.AdditionalData.Adresa)} " +
                        "изјавувам дека ОБД ИНКО АД Скопје, со дозвола за вршење осигурително брокерски работи број УП 14-1-16, " +
                        "издадена на 14.03.2025 година, го имам овластено да посредува во мое име и моја сметка за склучување на полиси " +
                        "за осигурување од сите класи, ги примив и разбрав информациите од член 50 согласно Закон за супервизија во осигурување, " +
                        "информиран/а сум за провизијата која ја прима Осигурително Брокерското Друштво од Осигурителите, " +
                        "ја примив и разбрав анализата за осигурително покрите и сум согласен со предложената понуда од ОБД ИНКО АД Скопјe, " +
                        "се согласувам за склучување на договор за осигурување.";
                }
                else
                {
                    declarationText = "";
                }

                if (!string.IsNullOrEmpty(declarationText))
                {
                    // Add declaration paragraph with proper formatting
                    var declaration = new Paragraph(declarationText, contentFont);
                    declaration.Alignment = Element.ALIGN_JUSTIFIED;
                    declaration.SetLeading(0f, 1.5f); // Increase line spacing
                    declaration.SpacingAfter = 30f;
                    doc.Add(declaration);

                    // Add date line
                    var dateLine = new Paragraph("\n\nДата: _______________", contentFont);
                    dateLine.Alignment = Element.ALIGN_LEFT;
                    doc.Add(dateLine);

                    // Add signature line
                    var signatureLine = new Paragraph("Потпис: _______________", contentFont);
                    signatureLine.Alignment = Element.ALIGN_RIGHT;
                    signatureLine.SpacingBefore = 0f;
                    doc.Add(signatureLine);
                }

                doc.Close();

                // Convert to base64
                var pdfBytes = memoryStream.ToArray();
                var base64Pdf = Convert.ToBase64String(pdfBytes);

                // Send email with PDF if email is provided and sendEmail flag is true
                if (sendEmail && !string.IsNullOrEmpty(request.AdditionalData.Email))
                {
                    try
                    {
                        Console.WriteLine($"Sending email to {request.AdditionalData.Email}");
                        await SendEmailWithPdfAsync(
                            request.AdditionalData.Email,
                            pdfBytes,
                            !string.IsNullOrEmpty(request.AdditionalData.Naziv) ? 
                                request.AdditionalData.Naziv : 
                                $"{request.AdditionalData.Ime} {request.AdditionalData.Prezime}"
                        );
                        Console.WriteLine("Email sent successfully");
                    }
                    catch (Exception emailEx)
                    {
                        Console.WriteLine($"Error sending email: {emailEx.Message}");
                        Console.WriteLine($"Stack trace: {emailEx.StackTrace}");
                        // Continue with PDF generation even if email fails
                    }
                }
                else
                {
                    Console.WriteLine($"Not sending email. SendEmail flag: {sendEmail}, Email: {request.AdditionalData.Email}");
                }

                return new JsonResult(new { success = true, pdfContent = base64Pdf });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error generating PDF: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                return new JsonResult(new { success = false, message = ex.Message });
            }
        }

        private async Task<ComparisonData> GetComparisonDataAsync(List<long> insurerIds, List<long> riskIds)
        {
            var comparisonData = new ComparisonData();
            
            using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();

                // Get insurer names
                var insurerSql = "SELECT Id, Naziv FROM Klienti WHERE Id IN (" + string.Join(",", insurerIds) + ")";
                using (var command = new SqlCommand(insurerSql, connection))
                {
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            comparisonData.Insurers.Add(reader.GetString(1));
                        }
                    }
                }

                // Only get risk names if we have risks
                if (riskIds != null && riskIds.Count > 0)
                {
                    var riskSql = "SELECT Id, Naziv FROM Rizici WHERE Id IN (" + string.Join(",", riskIds) + ")";
                    using (var command = new SqlCommand(riskSql, connection))
                    {
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                comparisonData.Risks.Add(reader.GetString(1));
                            }
                        }
                    }

                    // Get coverage data only if we have risks
                    var coverageSql = @"
                        SELECT r.Naziv as RiskName, k.Naziv as InsurerName, ark.Pokrieno, ark.Beleshka
                        FROM AnalizaNaKlientRizici ark
                        JOIN Rizici r ON ark.RizikIdRizici = r.Id
                        JOIN Klienti k ON ark.KlientiIdAnalizaDogovoruvac = k.Id
                        WHERE k.Id IN (" + string.Join(",", insurerIds) + @")
                        AND r.Id IN (" + string.Join(",", riskIds) + ")";

                    using (var command = new SqlCommand(coverageSql, connection))
                    {
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                var riskName = reader.GetString(0);
                                var insurerName = reader.GetString(1);
                                var pokrieno = reader.GetBoolean(2);
                                var beleshka = reader.IsDBNull(3) ? null : reader.GetString(3);

                                comparisonData.AddCoverage(riskName, insurerName, pokrieno, beleshka);
                            }
                        }
                    }
                }
            }

            return comparisonData;
        }

        public class AnalizaInputModel
        {
            [Display(Name = "Договорувач")]
            public long? KlientiIdAnalizaDogovoruvac { get; set; }

            [Display(Name = "Класа")]
            public int? SelectedKlasaId { get; set; }

            [Display(Name = "Продукт")]
            public int? SelectedProduktId { get; set; }

            [Display(Name = "Име")]
            public string? Ime { get; set; }

            [Display(Name = "Презиме")]
            public string? Prezime { get; set; }

            [Display(Name = "ЕМБГ")]
            public string? EMBG { get; set; }

            [Display(Name = "Назив")]
            public string? Naziv { get; set; }

            [Required(ErrorMessage = "Email е задолжително поле")]
            [Display(Name = "Е-пошта")]
            public string? Email { get; set; }

            [Display(Name = "Адреса")]
            public string? Adresa { get; set; }

            [Display(Name = "Образложение за предлог")]
            public string? ObrazlozenieZaPredlog { get; set; }

            [Display(Name = "ЕДБ")]
            public string? EDB { get; set; }

            [Display(Name = "МБ")]
            public string? MB { get; set; }

            [Display(Name = "Правно-економски односи со осигурител")]
            public string? PravnoEkonomskiOdnosiOsiguritel { get; set; }

            [Display(Name = "Провизија за предлог понуда")]
            public string? PrivizijaZaPredlogPonuda { get; set; }

            [Display(Name = "Премија износ")]
            public decimal? PremijaIznos { get; set; }

            [Display(Name = "Ризици за предмет на осигурување")]
            public string? RiziciOsiguritel { get; set; }

            [Display(Name = "Провизија за сите производи и осигурители")]
            public string? ProvizijaZaSiteProduktiIOsiguriteli { get; set; }

            public List<OsiguritelModel> Osiguriteli { get; set; } = new List<OsiguritelModel>();
            public List<KombinacijaModel> Kombinacii { get; set; } = new List<KombinacijaModel>();
        }

        public class OsiguritelModel
        {
            public long? Id { get; set; }
            public string? PravnoEkonomskiOdnosiOsiguritel { get; set; }
            public string? PrivizijaZaPredlogPonuda { get; set; }
            public string? ProvizijaZaSiteProduktiIOsiguriteli { get; set; }
            public decimal? PremijaIznos { get; set; }
            public string? RiziciOsiguritel { get; set; }
        }

        public class KombinacijaModel
        {
            public long RizikIdRizici { get; set; }
            public long KlientiIdAnalizaDogovoruvac { get; set; }
            public bool Pokrieno { get; set; }
            public string? Beleshka { get; set; }
        }

        public class Rizik
        {
            public long Id { get; set; }
            public string Naziv { get; set; }
            public string Tip { get; set; }
        }

        public class PdfRequestModel
        {
            public List<long> InsurerIds { get; set; }
            public List<long> RiskIds { get; set; }
            public AdditionalDataModel AdditionalData { get; set; }
        }

        public class AdditionalDataModel
        {
            public string PravnoEkonomskiOdnosi { get; set; }
            public string PrivizijaZaPredlogPonuda { get; set; }
            public string ProvizijaZaSiteProdukti { get; set; }
            public decimal? PremijaIznos { get; set; }
            public string ObrazlozenieZaPredlog { get; set; }
            public string Ime { get; set; }
            public string Prezime { get; set; }
            public string EMBG { get; set; }
            public string Adresa { get; set; }
            public string Naziv { get; set; }
            public string Email { get; set; }
            public string RiziciOsiguritel { get; set; }
            public List<InsurerData> Insurers { get; set; } = new List<InsurerData>();
        }

        public class InsurerData
        {
            public int Id { get; set; }
            public string? Name { get; set; }
            public string? PravnoEkonomskiOdnosi { get; set; }
            public string? PravnoEkonomskiOdnosiOsiguritel { get; set; }
            public string? PrivizijaZaPredlogPonuda { get; set; }
            public string? ProvizijaZaSiteProdukti { get; set; }
            public decimal? PremijaIznos { get; set; }
            public string? RiziciOsiguritel { get; set; }
        }

        public class ComparisonData
        {
            public List<string> Insurers { get; set; } = new List<string>();
            public List<string> Risks { get; set; } = new List<string>();
            private Dictionary<string, Dictionary<string, CoverageInfo>> _coverageData = new Dictionary<string, Dictionary<string, CoverageInfo>>();

            public void AddCoverage(string risk, string insurer, bool pokrieno, string beleshka)
            {
                if (!_coverageData.ContainsKey(risk))
                {
                    _coverageData[risk] = new Dictionary<string, CoverageInfo>();
                }
                _coverageData[risk][insurer] = new CoverageInfo { Pokrieno = pokrieno, Beleshka = beleshka };
            }

            public CoverageInfo GetCoverage(string risk, string insurer)
            {
                if (_coverageData.TryGetValue(risk, out var insurerData) &&
                    insurerData.TryGetValue(insurer, out var coverage))
                {
                    return coverage;
                }
                return new CoverageInfo { Pokrieno = false, Beleshka = null };
            }
        }

        public class CoverageInfo
        {
            public bool Pokrieno { get; set; }
            public string Beleshka { get; set; }
        }


        [HttpGet]
        public async Task<IActionResult> OnGetPravnoEkonomskiOdnosi([FromQuery] long insurerId)
        {
            try
            {
                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();

                    var query = @"
                        SELECT TOP 1 PravnoEkonomskiOdnosiOsiguritel
                        FROM AnalizaOsiguritelPravnoEkonomskiOdnosi
                        WHERE KlientiIdAnalizaDogovoruvac = @insurerId";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@insurerId", insurerId);
                        var result = await command.ExecuteScalarAsync();
                        
                        return new JsonResult(new
                        {
                            pravnoEkonomskiOdnosi = result?.ToString()
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                return new JsonResult(new { error = ex.Message }) { StatusCode = 500 };
            }
        }

        [HttpPost]
        public async Task<IActionResult> OnPostSavePravnoEkonomskiOdnosi([FromBody] SavePravnoEkonomskiOdnosiRequest request)
        {
            if (!await HasPageAccess("AnalizaNaKlient"))
            {
                return RedirectToAccessDenied();
            }

            try
            {
                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();

                    // Check if record already exists
                    var checkQuery = @"
                        SELECT TOP 1 Id
                        FROM AnalizaOsiguritelPravnoEkonomskiOdnosi
                        WHERE KlientiIdAnalizaDogovoruvac = @insurerId";

                    using (var checkCommand = new SqlCommand(checkQuery, connection))
                    {
                        checkCommand.Parameters.AddWithValue("@insurerId", request.InsurerId);
                        var existingId = await checkCommand.ExecuteScalarAsync();

                        if (existingId != null)
                        {
                            return new JsonResult(new { success = false, message = "Веќе постои запис за овој осигурител" });
                        }
                    }

                    // Insert new record
                    var insertQuery = @"
                        INSERT INTO AnalizaOsiguritelPravnoEkonomskiOdnosi (KlientiIdAnalizaDogovoruvac, PravnoEkonomskiOdnosiOsiguritel)
                        VALUES (@insurerId, @pravnoEkonomskiOdnosi)";

                    using (var command = new SqlCommand(insertQuery, connection))
                    {
                        command.Parameters.AddWithValue("@insurerId", request.InsurerId);
                        command.Parameters.AddWithValue("@pravnoEkonomskiOdnosi", request.PravnoEkonomskiOdnosi);
                        await command.ExecuteNonQueryAsync();
                        return new JsonResult(new { success = true });
                    }
                }
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = ex.Message });
            }
        }

        public class SavePravnoEkonomskiOdnosiRequest
        {
            public long InsurerId { get; set; }
            public string PravnoEkonomskiOdnosi { get; set; }
        }

        public class KlasaItem
        {
            public int Id { get; set; }
            public string KlasaIme { get; set; }
            public int KlasaBroj { get; set; }
        }

        public class ProduktItem
        {
            public int Id { get; set; }
            public string Ime { get; set; }
        }

        public async Task<IActionResult> OnGetProduktiByKlasaAsync(int klasaId)
        {
            try
            {
                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();
                    var command = new SqlCommand(
                        "SELECT Id, Ime FROM Produkti WHERE KlasaOsiguruvanjeId = @KlasaId ORDER BY Ime",
                        connection);
                    command.Parameters.AddWithValue("@KlasaId", klasaId);

                    var reader = await command.ExecuteReaderAsync();
                    var produkti = new List<ProduktItem>();

                    while (await reader.ReadAsync())
                    {
                        produkti.Add(new ProduktItem
                        {
                            Id = reader.GetInt32(0),
                            Ime = reader.GetString(1)
                        });
                    }

                    return new JsonResult(produkti);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching products for class {KlasaId}", klasaId);
                return new JsonResult(new List<ProduktItem>());
            }
        }

        public async Task<IActionResult> OnGetKlasiByProduktAsync(int produktId)
        {
            try
            {
                var klasi = new List<KlasaItem>();
                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();
                    var query = @"
                        SELECT ko.Id, ko.KlasaIme, ko.KlasaBroj
                        FROM KlasiOsiguruvanje ko
                        INNER JOIN Produkti p ON p.KlasaOsiguruvanjeID = ko.Id
                        WHERE p.Id = @ProduktId
                        AND ko.IsActive = 1
                        ORDER BY ko.KlasaBroj";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@ProduktId", produktId);
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                klasi.Add(new KlasaItem
                                {
                                    Id = reader.GetInt32(0),
                                    KlasaIme = reader.GetString(1),
                                    KlasaBroj = reader.GetInt32(2)
                                });
                            }
                        }
                    }
                }
                return new JsonResult(klasi);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching classes by product");
                return new JsonResult(new List<KlasaItem>());
            }
        }

        public async Task<IActionResult> OnGetRiziciByProduktAsync(int produktId)
        {
            try
            {
                Console.WriteLine($"Debug - OnGetRiziciByProduktAsync called with produktId: {produktId}");
                var rizici = new List<object>();
                using (var connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();
                    var query = @"
                        SELECT r.Id, r.Naziv, r.Tip, r.ProduktId
                        FROM Rizici r
                        WHERE r.ProduktId = @ProduktId
                        ORDER BY r.Naziv";

                    Console.WriteLine($"Debug - Executing query with produktId: {produktId}");
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@ProduktId", produktId);
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                var rizik = new
                                {
                                    id = reader.GetInt64(0),
                                    naziv = reader.GetString(1),
                                    tip = reader.IsDBNull(2) ? null : reader.GetString(2),
                                    produktId = reader.GetInt32(3)
                                };
                                Console.WriteLine($"Debug - Found risk: {rizik.naziv} (ID: {rizik.id}, ProductID: {rizik.produktId})");
                                rizici.Add(rizik);
                            }
                        }
                    }
                }
                Console.WriteLine($"Debug - Returning {rizici.Count} risks for product {produktId}");
                return new JsonResult(rizici);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Debug - Error in OnGetRiziciByProduktAsync: {ex.Message}");
                Console.WriteLine($"Debug - Stack trace: {ex.StackTrace}");
                _logger.LogError(ex, "Error fetching risks for product {ProduktId}", produktId);
                return new JsonResult(new { error = "Error fetching risks" });
            }
        }
    }
}


