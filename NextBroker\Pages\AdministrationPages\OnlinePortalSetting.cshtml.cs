using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using RazorPortal.Services;
using System.ComponentModel.DataAnnotations;

namespace NextBroker.Pages.AdministrationPages
{
    public class OnlinePortalSettingModel : SecurePageModel
    {
        public OnlinePortalSettingModel(IConfiguration configuration)
            : base(configuration)
        {
        }

        public List<UserModel> Users { get; set; } = new List<UserModel>();
        public bool OnlinePortalEnabled { get; set; }
        public bool OnlinePortalPayments { get; set; }
        public string EmailZaObnovaNaPolisa { get; set; } = "";

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("OnlinePortalSetting"))
            {
                return RedirectToAccessDenied();
            }

            await LoadPortalSettings();
            await LoadUsers();
            return Page();
        }

        public async Task<IActionResult> OnPostUpdateUserAsync(long id, string email, bool locked)
        {
            if (!await HasPageAccess("OnlinePortalSetting"))
            {
                return RedirectToAccessDenied();
            }

            try
            {
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    using (SqlCommand cmd = new SqlCommand(@"
                        UPDATE users
                        SET email = @Email, locked = @Locked
                        WHERE id = @Id", connection))
                    {
                        cmd.Parameters.AddWithValue("@Id", id);
                        cmd.Parameters.AddWithValue("@Email", email ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@Locked", locked);

                        await cmd.ExecuteNonQueryAsync();
                    }
                }

                return new JsonResult(new { success = true, message = "Корисникот е успешно ажуриран" });
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = $"Грешка при ажурирање на корисникот: {ex.Message}" });
            }
        }

        public async Task<IActionResult> OnPostToggleSettingAsync(string settingName, bool value)
        {
            if (!await HasPageAccess("OnlinePortalSetting"))
            {
                return RedirectToAccessDenied();
            }

            try
            {
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    using (SqlCommand cmd = new SqlCommand(@"
                        UPDATE environmentvariables
                        SET VariableValue = @Value
                        WHERE VariableName = @SettingName", connection))
                    {
                        cmd.Parameters.AddWithValue("@SettingName", settingName);
                        cmd.Parameters.AddWithValue("@Value", value ? "1" : "0");

                        await cmd.ExecuteNonQueryAsync();
                    }
                }

                return new JsonResult(new { success = true, message = "Поставката е успешно ажурирана" });
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = $"Грешка при ажурирање на поставката: {ex.Message}" });
            }
        }

        public async Task<IActionResult> OnPostUpdateEmailAsync(string emailValue)
        {
            if (!await HasPageAccess("OnlinePortalSetting"))
            {
                return RedirectToAccessDenied();
            }

            try
            {
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    using (SqlCommand cmd = new SqlCommand(@"
                        UPDATE environmentvariables
                        SET VariableValue = @EmailValue
                        WHERE VariableName = 'EmailZaObnovaNaPolisa'", connection))
                    {
                        cmd.Parameters.AddWithValue("@EmailValue", emailValue ?? "");
                        await cmd.ExecuteNonQueryAsync();
                    }
                }

                return new JsonResult(new { success = true, message = "Е-поштата е успешно ажурирана" });
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, message = $"Грешка при ажурирање на е-поштата: {ex.Message}" });
            }
        }

        private async Task LoadPortalSettings()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                // Load OnlinePortalEnabled
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT VariableValue
                    FROM environmentvariables
                    WHERE VariableName = 'OnlinePortalEnabled'", connection))
                {
                    var result = await cmd.ExecuteScalarAsync();
                    OnlinePortalEnabled = result?.ToString() == "1";
                }

                // Load OnlinePortalPayments
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT VariableValue
                    FROM environmentvariables
                    WHERE VariableName = 'OnlinePortalPayments'", connection))
                {
                    var result = await cmd.ExecuteScalarAsync();
                    OnlinePortalPayments = result?.ToString() == "1";
                }

                // Load EmailZaObnovaNaPolisa
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT VariableValue
                    FROM environmentvariables
                    WHERE VariableName = 'EmailZaObnovaNaPolisa'", connection))
                {
                    var result = await cmd.ExecuteScalarAsync();
                    EmailZaObnovaNaPolisa = result?.ToString() ?? "";
                }
            }
        }

        private async Task LoadUsers()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT id, firstname, lastname, username, email, locked, emb
                    FROM users
                    WHERE username NOT IN ('admin', 'nextadmin')
                    ORDER BY lastname, firstname", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var users = new List<UserModel>();
                    while (await reader.ReadAsync())
                    {
                        users.Add(new UserModel
                        {
                            Id = reader.GetInt64(0), // id
                            FirstName = reader.IsDBNull(1) ? "" : reader.GetString(1), // firstname
                            LastName = reader.IsDBNull(2) ? "" : reader.GetString(2), // lastname
                            Username = reader.IsDBNull(3) ? "" : reader.GetString(3), // username
                            Email = reader.IsDBNull(4) ? "" : reader.GetString(4), // email
                            Locked = reader.IsDBNull(5) ? false : reader.GetBoolean(5), // locked
                            EMB = reader.IsDBNull(6) ? "" : reader.GetString(6) // emb
                        });
                    }
                    Users = users;
                }
            }
        }

        public class UserModel
        {
            public long Id { get; set; }

            [Display(Name = "Име")]
            public string FirstName { get; set; } = "";

            [Display(Name = "Презиме")]
            public string LastName { get; set; } = "";

            [Display(Name = "Корисничко име")]
            public string Username { get; set; } = "";

            [Display(Name = "Е-пошта")]
            public string Email { get; set; } = "";

            [Display(Name = "Заклучен")]
            public bool Locked { get; set; }

            [Display(Name = "ЕМБ")]
            public string EMB { get; set; } = "";
        }
    }
}