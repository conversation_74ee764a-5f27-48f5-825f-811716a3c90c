@page
@model NextBroker.Pages.Pregledi.IzvestajZaNaplataIzlezniFakturiModel
@{
    ViewData["Title"] = "Извештај за наплата излезни фактури";
    Layout = "_Layout";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-file-invoice-dollar"></i>
                        Извештај за наплата излезни фактури
                    </h3>
                </div>
                <div class="card-body">
                    <!-- Filter Form -->
                    <form method="post" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="Filter.DatumOd" class="form-label"></label>
                                    <input asp-for="Filter.DatumOd" class="form-control" type="date" />
                                    <span asp-validation-for="Filter.DatumOd" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="Filter.DatumDo" class="form-label"></label>
                                    <input asp-for="Filter.DatumDo" class="form-control" type="date" />
                                    <span asp-validation-for="Filter.DatumDo" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search"></i> Прикажи
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>

                    @if (Model.HasData)
                    {
                        <!-- Export Button -->
                        <div class="mb-3">
                            <form method="post" asp-page-handler="ExportToExcel" style="display: inline;">
                                <input asp-for="Filter.DatumOd" type="hidden" />
                                <input asp-for="Filter.DatumDo" type="hidden" />
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-file-excel"></i> Извези во Excel
                                </button>
                            </form>
                            <span class="text-muted ml-2">Вкупно записи: @Model.Results.Count</span>
                        </div>

                        <!-- Search Box -->
                        <div class="mb-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <input type="text" id="searchInput" class="form-control" placeholder="Пребарај во табелата...">
                                </div>
                            </div>
                        </div>

                        <!-- Results Table -->
                        <div class="table-responsive">
                            <table id="resultsTable" class="table table-striped table-bordered table-hover">
                                <thead class="thead-dark">
                                    <tr>
                                        <th>Датум на фактура</th>
                                        <th>Број на фактура</th>
                                        <th>Број на полиса</th>
                                        <th>Рок на плаќање</th>
                                        <th>Договорувач</th>
                                        <th>Телефон договорувач</th>
                                        <th>Матичен број договорувач</th>
                                        <th>Адреса договорувач</th>
                                        <th>Осигуреник</th>
                                        <th>Телефон осигуреник</th>
                                        <th>Матичен број осигуреник</th>
                                        <th>Адреса осигуреник</th>
                                        <th>Осигурител</th>
                                        <th>Класа</th>
                                        <th>Продукт</th>
                                        <th>Вработен соработник</th>
                                        <th>Матичен број на соработник</th>
                                        <th>Износ премија за наплата</th>
                                        <th>Износ премија за наплата во рок</th>
                                        <th>Платено во рок</th>
                                        <th>Вкупни уплати од договорувач</th>
                                        <th>Долг по доспеани рати</th>
                                        <th>Вкупен долг</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var result in Model.Results)
                                    {
                                        <tr>
                                            <td>@(result.DatumNaFaktura?.ToString("dd.MM.yyyy") ?? "")</td>
                                            <td>@result.BrojNaFaktura</td>
                                            <td>@result.BrojNaPolisa</td>
                                            <td>@(result.RokNaPlakanje?.ToString("dd.MM.yyyy") ?? "")</td>
                                            <td>@result.Dogovoruvac</td>
                                            <td>@result.TelefonDogovoruvac</td>
                                            <td>@result.MaticenBrojDogovoruvac</td>
                                            <td>@result.AdresaDogovoruvac</td>
                                            <td>@result.Osigurenik</td>
                                            <td>@result.TelefonOsigurenik</td>
                                            <td>@result.MaticenBrojOsigurenik</td>
                                            <td>@result.AdresaOsigurenik</td>
                                            <td>@result.Osiguritel</td>
                                            <td>@result.Klasa</td>
                                            <td>@result.Produkt</td>
                                            <td>@result.VrabotenSorabotnik</td>
                                            <td>@result.MaticenBrojSorabotnik</td>
                                            <td class="text-right">@(result.IznosPremijaZaNaplata?.ToString("N2") ?? "")</td>
                                            <td class="text-right">@(result.IznosPremijaZaNaplataVoRok?.ToString("N2") ?? "")</td>
                                            <td class="text-center">
                                                @if (result.PlatenoVoRok == "Да")
                                                {
                                                    <span class="badge badge-success">@result.PlatenoVoRok</span>
                                                }
                                                else
                                                {
                                                    <span class="badge badge-danger">@result.PlatenoVoRok</span>
                                                }
                                            </td>
                                            <td class="text-right">@(result.VkupniUplatiOdDogovoruvac?.ToString("N2") ?? "")</td>
                                            <td class="text-right">@(result.DolgPoDospeanRati?.ToString("N2") ?? "")</td>
                                            <td class="text-right">@(result.VkupenDolg?.ToString("N2") ?? "")</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else if (ViewContext.HttpContext.Request.Method == "POST")
                    {
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            Нема податоци за прикажување во избраниот период.
                        </div>
                    }
                    else
                    {
                        <div class="alert alert-secondary">
                            <i class="fas fa-info-circle"></i>
                            Изберете датумски период и кликнете на "Прикажи" за да ги видите резултатите.
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Initialize search functionality
            $('#searchInput').on('keyup', function() {
                var value = $(this).val().toLowerCase();
                $('#resultsTable tbody tr').filter(function() {
                    $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
                });
            });

            // Set default dates if not already set
            var datumOd = $('#Filter_DatumOd').val();
            var datumDo = $('#Filter_DatumDo').val();

            if (!datumOd) {
                var firstDayOfYear = new Date(new Date().getFullYear(), 0, 1);
                $('#Filter_DatumOd').val(firstDayOfYear.toISOString().split('T')[0]);
            }

            if (!datumDo) {
                var today = new Date();
                $('#Filter_DatumDo').val(today.toISOString().split('T')[0]);
            }
        });
    </script>
}