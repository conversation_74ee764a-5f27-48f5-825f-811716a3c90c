@page
@model NextBroker.Pages.Finansii.DokumentZaOdobruvanjeIzleznaFakturaKonKlientModel
@{
    ViewData["Title"] = "Документ за одобрување излезна фактура кон клиент";
    Layout = "_Layout";
}

@if (TempData["SuccessMessage"] != null)
{
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        @TempData["SuccessMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

@if (TempData["ErrorMessage"] != null)
{
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        @TempData["ErrorMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
}

<div class="container-fluid">
    <!-- First Section: Search and Results -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">@ViewData["Title"]</h3>
                </div>
                <div class="card-body">
                    <!-- Date Filter Form -->
                    <form method="post" class="mb-4">
                        <div class="row">
                            <div class="col-md-4">
                                <label asp-for="DatumOd" class="form-label">Датум од:</label>
                                <input asp-for="DatumOd" type="date" class="form-control" />
                                <span asp-validation-for="DatumOd" class="text-danger"></span>
                            </div>
                            <div class="col-md-4">
                                <label asp-for="DatumDo" class="form-label">Датум до:</label>
                                <input asp-for="DatumDo" type="date" class="form-control" />
                                <span asp-validation-for="DatumDo" class="text-danger"></span>
                            </div>
                            <div class="col-md-4 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> Пребарај
                                </button>
                            </div>
                        </div>
                    </form>

                    <!-- Results Table -->
                    @if (Model.FakturaResults.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Датум на фактура</th>
                                        <th>Број на фактура</th>
                                        <th>Број на полиса</th>
                                        <th>Рок на плаќање</th>
                                        <th>Договорувач</th>
                                        <th>Осигуреник</th>
                                        <th>Осигурител</th>
                                        <th>Продукт</th>
                                        <th>Износ</th>
                                        <th>Износ со попуст</th>
                                        <th>Акции</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var item in Model.FakturaResults)
                                    {
                                        <tr>
                                            <td>@item.DatumNaFaktura?.ToString("dd.MM.yyyy")</td>
                                            <td>@item.BrojNaFaktura</td>
                                            <td>@item.BrojNaPolisa</td>
                                            <td>@item.RokNaPlakanje?.ToString("dd.MM.yyyy")</td>
                                            <td>@item.Dogovoruvac?.Trim()</td>
                                            <td>@item.Osigurenik?.Trim()</td>
                                            <td>@item.Osiguritel</td>
                                            <td>@item.Produkt</td>
                                            <td>@item.Iznos?.ToString("N2")</td>
                                            <td>@item.IznosSoPopust?.ToString("N2")</td>
                                            <td>
                                                @if (!Model.ExistingApprovals.Contains(item.BrojNaPolisa ?? ""))
                                                {
                                                    <button type="button" class="btn btn-success btn-sm"
                                                            onclick="openOdobruvanjeModal('@item.BrojNaFaktura', '@item.BrojNaPolisa', '@item.Iznos', '@item.IznosSoPopust')">
                                                        <i class="fas fa-check"></i> Генерирај Одобрување
                                                    </button>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">
                                                        <i class="fas fa-check-circle"></i> Веќе одобрено
                                                    </span>
                                                }
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else if (ViewContext.HttpContext.Request.Method == "POST")
                    {
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> Нема резултати за избраниот период.
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- Second Section: Approval Records -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Историја на одобрувања</h3>
                </div>
                <div class="card-body">
                    @if (Model.OdobruvanjeRecords.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>ID</th>
                                        <th>Датум креирано</th>
                                        <th>Корисник</th>
                                        <th>Број на фактура</th>
                                        <th>Број на полиса</th>
                                        <th>Премија</th>
                                        <th>Изноа за плаќање во рок</th>
                                        <th>Клиент ID</th>
                                        <th>Одобрување кон</th>
                                        <th>Број на документ</th>
                                        <th>Датум на документ</th>
                                        <th>Одобрување</th>
                                        <th>Сторно</th>
                                        <th>Акции</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var record in Model.OdobruvanjeRecords)
                                    {
                                        <tr>
                                            <td>@record.Id</td>
                                            <td>@record.DateCreated?.ToString("dd.MM.yyyy HH:mm")</td>
                                            <td>@record.UsernameCreated</td>
                                            <td>@record.BrojNaFaktura</td>
                                            <td>@record.BrojNaPolisa</td>
                                            <td>@record.Premija?.ToString("N2")</td>
                                            <td>@record.Odobruvanje?.ToString("N2")</td>
                                            <td>@record.KlientiIdOdobruvanjeKon</td>
                                            <td>@record.OdobruvanjeKon</td>
                                            <td>@record.BrojNaDokument</td>
                                            <td>@record.DatumNaDokument?.ToString("dd.MM.yyyy")</td>
                                            <td>@record.Iznos?.ToString("N2")</td>
                                            <td>@(record.Storno ? "да" : "не")</td>
                                            <td>
                                                @if (!record.Storno)
                                                {
                                                    <form method="post" asp-page-handler="StornoOdobruvanje" style="display: inline;">
                                                        <input type="hidden" name="id" value="@record.Id" />
                                                        <button type="submit" class="btn btn-danger btn-sm"
                                                                onclick="return confirm('Дали сте сигурни дека сакате да го сторнирате ова одобрување?')">
                                                            <i class="fas fa-times"></i> Сторнирај
                                                        </button>
                                                    </form>
                                                    <button type="button" class="btn btn-primary btn-sm ms-1"
                                                            onclick="generateApprovalPDF(@record.Id, '@record.BrojNaFaktura', '@record.BrojNaPolisa', '@record.Premija', '@record.Odobruvanje', '@record.OdobruvanjeKon', '@record.BrojNaDokument', '@record.DatumNaDokument?.ToString("dd.MM.yyyy")', '@record.Iznos', '@record.DateCreated?.ToString("dd.MM.yyyy")')">
                                                        <i class="fas fa-file-pdf"></i> Генерирај PDF
                                                    </button>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">Сторнирано</span>
                                                }
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> Нема записи за одобрувања.
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal for Creating Approval -->
<div class="modal fade" id="odobruvanjeModal" tabindex="-1" aria-labelledby="odobruvanjeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form method="post" asp-page-handler="CreateOdobruvanje">
                <div class="modal-header">
                    <h5 class="modal-title" id="odobruvanjeModalLabel">Креирај одобрување за плаќање во рок</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <label asp-for="OdobruvanjeInput.BrojNaFaktura" class="form-label">Број на фактура:</label>
                            <input asp-for="OdobruvanjeInput.BrojNaFaktura" class="form-control" readonly />
                            <span asp-validation-for="OdobruvanjeInput.BrojNaFaktura" class="text-danger"></span>
                        </div>
                        <div class="col-md-6">
                            <label asp-for="OdobruvanjeInput.BrojNaPolisa" class="form-label">Број на полиса:</label>
                            <input asp-for="OdobruvanjeInput.BrojNaPolisa" class="form-control" readonly />
                            <span asp-validation-for="OdobruvanjeInput.BrojNaPolisa" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label asp-for="OdobruvanjeInput.Premija" class="form-label">Премија (Износ):</label>
                            <input asp-for="OdobruvanjeInput.Premija" type="number" step="any" class="form-control" onchange="calculateIznos()" />
                            <span asp-validation-for="OdobruvanjeInput.Premija" class="text-danger"></span>
                        </div>
                        <div class="col-md-6">
                            <label asp-for="OdobruvanjeInput.Odobruvanje" class="form-label">Износ за плаќање во рок:</label>
                            <input asp-for="OdobruvanjeInput.Odobruvanje" type="number" step="any" class="form-control" onchange="calculateIznos()" />
                            <span asp-validation-for="OdobruvanjeInput.Odobruvanje" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label class="form-label">Клиент за одобрување (пребарувај по МБ, ЕДБ, ЕМБГ, име или назив)</label>
                            <div class="input-group">
                                <button type="button" class="btn btn-outline-secondary btn-sm clear-field" data-target="klientOdobruvanje" style="width: 30px; height: 30px; padding: 0; margin-right: 5px;">
                                    <i class="fas fa-eraser" style="font-size: 0.7rem;"></i>
                                </button>
                                <input type="text" id="klientOdobruvanjeMBSearch" class="form-control"
                                       autocomplete="off"
                                       placeholder="Пребарувај по МБ, ЕДБ, ЕМБГ, име/назив..." />
                                <input type="hidden" asp-for="OdobruvanjeInput.KlientiIdOdobruvanjeKon" id="KlientiIdOdobruvanjeKon" />
                            </div>
                            <div id="klientOdobruvanjeSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1"
                                 style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                            </div>
                            <span asp-validation-for="OdobruvanjeInput.KlientiIdOdobruvanjeKon" class="text-danger"></span>
                        </div>
                        <div class="col-md-6">
                            <label asp-for="OdobruvanjeInput.BrojNaDokument" class="form-label">Број на документ:</label>
                            <input asp-for="OdobruvanjeInput.BrojNaDokument" class="form-control" />
                            <span asp-validation-for="OdobruvanjeInput.BrojNaDokument" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <label asp-for="OdobruvanjeInput.DatumNaDokument" class="form-label">Датум на документ:</label>
                            <input asp-for="OdobruvanjeInput.DatumNaDokument" type="date" class="form-control" />
                            <span asp-validation-for="OdobruvanjeInput.DatumNaDokument" class="text-danger"></span>
                        </div>
                        <div class="col-md-6">
                            <label asp-for="OdobruvanjeInput.Iznos" class="form-label">Одобрување:</label>
                            <input asp-for="OdobruvanjeInput.Iznos" type="number" step="any" class="form-control" />
                            <span asp-validation-for="OdobruvanjeInput.Iznos" class="text-danger"></span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Откажи</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Зачувај одобрување
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function openOdobruvanjeModal(brojNaFaktura, brojNaPolisa, iznos, iznosSoPopust) {
    // Set the form values
    document.querySelector('input[name="OdobruvanjeInput.BrojNaFaktura"]').value = brojNaFaktura || '';
    document.querySelector('input[name="OdobruvanjeInput.BrojNaPolisa"]').value = brojNaPolisa || '';
    document.querySelector('input[name="OdobruvanjeInput.Premija"]').value = iznos || '';
    document.querySelector('input[name="OdobruvanjeInput.Odobruvanje"]').value = iznosSoPopust || '';

    // Clear client search field
    document.getElementById('klientOdobruvanjeMBSearch').value = '';
    document.getElementById('KlientiIdOdobruvanjeKon').value = '';
    document.getElementById('klientOdobruvanjeSearchResults').style.display = 'none';

    // Calculate the initial difference (Iznos - IznosSoPopust)
    calculateIznos();

    // Show the modal
    var modal = new bootstrap.Modal(document.getElementById('odobruvanjeModal'));
    modal.show();

    // Initialize search functionality after modal is shown
    setTimeout(function() {
        initializeClientSearch();
    }, 100);
}

function calculateIznos() {
    var premijaInput = document.querySelector('input[name="OdobruvanjeInput.Premija"]');
    var odobruvanjeInput = document.querySelector('input[name="OdobruvanjeInput.Odobruvanje"]');
    var iznosInput = document.querySelector('input[name="OdobruvanjeInput.Iznos"]');

    var premijaValue = parseFloat(premijaInput.value) || 0;
    var odobruvanjeValue = parseFloat(odobruvanjeInput.value) || 0;
    var razlika = premijaValue - odobruvanjeValue;

    iznosInput.value = razlika.toFixed(2);
}

// Client search functionality
let searchTimeout;
let searchInitialized = false;

function initializeClientSearch() {
    // Prevent multiple initializations
    if (searchInitialized) {
        return;
    }
    searchInitialized = true;

    console.log('Initializing client search...');

    // Remove any existing event handlers
    $('#klientOdobruvanjeMBSearch').off('input.clientSearch');

    // Add input event handler
    $('#klientOdobruvanjeMBSearch').on('input.clientSearch', function() {
        const searchTerm = $(this).val().trim();
        const resultsDiv = $('#klientOdobruvanjeSearchResults');

        console.log('Search term:', searchTerm);

        clearTimeout(searchTimeout);

        if (searchTerm.length < 2) {
            resultsDiv.hide();
            return;
        }

        searchTimeout = setTimeout(() => {
            console.log('Making AJAX request...');
            const url = `${window.location.pathname}?handler=SearchKlienti&mb=${encodeURIComponent(searchTerm)}`;
            console.log('Request URL:', url);
            $.get(url)
                .done(function(data) {
                    console.log('Search results:', data);
                    if (data && data.length > 0) {
                        let html = '<div class="list-group">';
                        data.forEach(function(client) {
                            const displayName = client.tip === 'P' ? client.naziv : `${client.ime} ${client.prezime}`.trim();
                            const identifiers = [client.mb, client.edb, client.embg].filter(x => x).join(' | ');

                            html += `
                                <a href="#" class="list-group-item list-group-item-action"
                                   onclick="selectClient(${client.id}, '${displayName.replace(/'/g, "\\'")}')">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">${displayName}</h6>
                                        <small>${client.tip === 'P' ? 'Правно' : 'Физичко'}</small>
                                    </div>
                                    <p class="mb-1">${identifiers}</p>
                                </a>`;
                        });
                        html += '</div>';
                        resultsDiv.html(html).show();
                    } else {
                        resultsDiv.html(`
                            <div class="list-group">
                                <div class="list-group-item text-center">
                                    <p class="mb-2">Нема пронајдени резултати</p>
                                </div>
                            </div>
                        `).show();
                    }
                })
                .fail(function(xhr, status, error) {
                    console.error('Search failed:', error);
                    resultsDiv.html(`
                        <div class="list-group">
                            <div class="list-group-item text-center">
                                <p class="text-danger mb-2">Грешка при пребарување</p>
                            </div>
                        </div>
                    `).show();
                });
        }, 300);
    });

    // Hide results when clicking outside
    $(document).on('click.clientSearch', function(e) {
        if (!$(e.target).closest('#klientOdobruvanjeMBSearch, #klientOdobruvanjeSearchResults').length) {
            $('#klientOdobruvanjeSearchResults').hide();
        }
    });
}

$(document).ready(function() {
    // Clear field functionality
    $(document).on('click', '.clear-field', function() {
        const target = $(this).data('target');
        if (target === 'klientOdobruvanje') {
            $('#klientOdobruvanjeMBSearch').val('');
            $('#KlientiIdOdobruvanjeKon').val('');
            $('#klientOdobruvanjeSearchResults').hide();
        }
    });
});

function selectClient(clientId, displayName) {
    console.log('Selecting client:', clientId, displayName);
    $('#klientOdobruvanjeMBSearch').val(displayName);
    $('#KlientiIdOdobruvanjeKon').val(clientId);
    $('#klientOdobruvanjeSearchResults').hide();
}

// PDF Generation function for approval documents
function generateApprovalPDF(recordId, brojNaFaktura, brojNaPolisa, premija, odobruvanje, odobruvanjeKon, brojNaDokument, datumNaDokument, iznos, dateCreated) {
    // Create a temporary div for the PDF content
    const tempDiv = document.createElement('div');
    tempDiv.id = 'approvalPdfContent';
    tempDiv.style.position = 'absolute';
    tempDiv.style.left = '-9999px';
    tempDiv.style.top = '0';
    tempDiv.style.width = '794px';
    tempDiv.style.padding = '20px';
    tempDiv.style.backgroundColor = 'white';
    tempDiv.style.fontFamily = 'Arial, sans-serif';
    
    // Format current date
    const today = new Date();
    const formattedToday = today.getDate().toString().padStart(2, '0') + '.' + 
                          (today.getMonth() + 1).toString().padStart(2, '0') + '.' + 
                          today.getFullYear();
    
    // Create the approval document HTML with similar design to the invoice
    const approvalHtml = `
        <div style="font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; position: relative; padding: 20px 40px;">
            <!-- Background Logo -->
            <div style="position: absolute; top: 20px; right: 40px; width: 150px; opacity: 0.15; z-index: 0; pointer-events: none;">
                <img src="/images/logo/INCO_LOGO_Regular.svg" style="width: 100%; height: auto;" />
            </div>
            
            <!-- Decorative Corner Elements -->
            <div style="position: absolute; top: 10px; left: 10px; width: 20px; height: 20px; border-left: 1px solid rgba(212, 175, 55, 0.25); border-top: 1px solid rgba(212, 175, 55, 0.25);"></div>
            <div style="position: absolute; top: 10px; right: 10px; width: 20px; height: 20px; border-right: 1px solid rgba(212, 175, 55, 0.25); border-top: 1px solid rgba(212, 175, 55, 0.25);"></div>
            <div style="position: absolute; bottom: 10px; left: 10px; width: 20px; height: 20px; border-left: 1px solid rgba(212, 175, 55, 0.25); border-bottom: 1px solid rgba(212, 175, 55, 0.25);"></div>
            <div style="position: absolute; bottom: 10px; right: 10px; width: 20px; height: 20px; border-right: 1px solid rgba(212, 175, 55, 0.25); border-bottom: 1px solid rgba(212, 175, 55, 0.25);"></div>
            
            <div style="position: relative; z-index: 1;">
                <div style="text-align: center; margin-bottom: 30px; padding-right: 160px;">
                    <h3 style="color: #2F4F4F; margin-bottom: 5px; font-size: 22px; text-shadow: 1px 1px 1px rgba(0,0,0,0.1);">Осигурително Брокерско Друштво ИНКО АД Скопје</h3>
                    <h4 style="color: #000; margin-top: 10px; font-size: 18px;">ДОКУМЕНТ ЗА ОДОБРУВАЊЕ ЗА ПЛАЌАЊЕ ВО РОК</h4>
                    <h5 style="color: #666; margin-top: 5px; font-size: 14px;">Документ бр: ${recordId}</h5>
                </div>
                
                <div style="margin-bottom: 20px; background-color: rgba(47, 79, 79, 0.03); padding: 15px; border-radius: 6px; border: 1px solid rgba(212, 175, 55, 0.2); box-shadow: 0 1px 3px rgba(0,0,0,0.05);">
                    <p style="margin: 5px 0; font-size: 13px;"><strong style="color: #2F4F4F; min-width: 180px; display: inline-block;">Датум на креирање:</strong> ${dateCreated || formattedToday}</p>
                    <p style="margin: 5px 0; font-size: 13px;"><strong style="color: #2F4F4F; min-width: 180px; display: inline-block;">Број на фактура:</strong> ${brojNaFaktura || 'Н/А'}</p>
                    <p style="margin: 5px 0; font-size: 13px;"><strong style="color: #2F4F4F; min-width: 180px; display: inline-block;">Број на полиса:</strong> ${brojNaPolisa || 'Н/А'}</p>
                    <p style="margin: 5px 0; font-size: 13px;"><strong style="color: #2F4F4F; min-width: 180px; display: inline-block;">Одобрување кон:</strong> ${odobruvanjeKon || 'Н/А'}</p>
                    <p style="margin: 5px 0; font-size: 13px;"><strong style="color: #2F4F4F; min-width: 180px; display: inline-block;">Број на документ:</strong> ${brojNaDokument || 'Н/А'}</p>
                    <p style="margin: 5px 0; font-size: 13px;"><strong style="color: #2F4F4F; min-width: 180px; display: inline-block;">Датум на документ:</strong> ${datumNaDokument || 'Н/А'}</p>
                </div>
                
                <table style="width: 100%; border-collapse: collapse; margin-bottom: 30px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); background-color: white;">
                    <thead>
                        <tr style="background: linear-gradient(90deg, #2F4F4F, #1a2f2f); color: white;">
                            <th style="border: 1px solid #ddd; padding: 12px; text-align: left; font-size: 14px;">Опис</th>
                            <th style="border: 1px solid #ddd; padding: 12px; text-align: right; font-size: 14px;">Износ (МКД)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr style="background-color: rgba(47, 79, 79, 0.02);">
                            <td style="border: 1px solid #ddd; padding: 12px; font-size: 13px;">Премија (Износ)</td>
                            <td style="border: 1px solid #ddd; padding: 12px; text-align: right; font-size: 13px;">${premija ? parseFloat(premija).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2}) : '0.00'}</td>
                        </tr>
                        <tr style="background-color: white;">
                            <td style="border: 1px solid #ddd; padding: 12px; font-size: 13px;">Износ за плаќање во рок </td>
                            <td style="border: 1px solid #ddd; padding: 12px; text-align: right; font-size: 13px;">${odobruvanje ? parseFloat(odobruvanje).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2}) : '0.00'}</td>
                        </tr>
                        <tr style="background-color: rgba(47, 79, 79, 0.02); border-top: 2px solid #2F4F4F;">
                            <td style="border: 1px solid #ddd; padding: 12px; font-size: 14px; font-weight: bold; color: #2F4F4F;">Износ одобрување</td>
                            <td style="border: 1px solid #ddd; padding: 12px; text-align: right; font-size: 14px; font-weight: bold; color: #2F4F4F;">${iznos ? parseFloat(iznos).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2}) : '0.00'}</td>
                        </tr>
                    </tbody>
                </table>

                <div style="margin-bottom: 30px; background-color: rgba(47, 79, 79, 0.03); padding: 15px; border-radius: 6px; border: 1px solid rgba(212, 175, 55, 0.2); box-shadow: 0 1px 3px rgba(0,0,0,0.05);">
                    <p style="margin: 5px 0; color: #000; font-size: 13px; line-height: 1.5;">Овој документ претставува одобрување за плаќање во рок на фактурираниот износ со попуст.</p>
                    <p style="margin: 5px 0; color: #000; font-size: 13px; line-height: 1.5;">Документот е генериран автоматски од системот и служи како потврда за одобрениот попуст.</p>
                </div>

                <div style="margin-top: 50px; margin-bottom: 30px;">
                    <!-- Gold Accent Line -->
                    <div style="position: relative; margin-bottom: 20px;">
                        <div style="position: absolute; top: -10px; left: 0; right: 0; height: 1px; background: linear-gradient(90deg, #D4AF37, transparent); opacity: 0.3;"></div>
                    </div>
                    
                    <div style="display: flex; justify-content: space-between; align-items: flex-end;">
                        <div style="text-align: left; flex: 1;">
                            <p style="margin-bottom: 5px; font-size: 13px; color: #2F4F4F;"><strong>Датум на издавање:</strong></p>
                            <p style="margin: 0; font-size: 13px;">${formattedToday}</p>
                        </div>
                        <div style="text-align: center; flex: 1;">
                            <p style="margin-bottom: 40px; font-size: 13px; color: #2F4F4F;"><strong>Потпис</strong></p>
                            <div style="border-top: 1px solid #2F4F4F; width: 200px; margin: 0 auto;"></div>
                        </div>
                        <div style="flex: 1;">
                            <!-- Empty space for balance -->
                        </div>
                    </div>
                </div>

                <!-- Gold Accent Bottom -->
                <div style="position: relative; margin-top: 30px;">
                    <div style="position: absolute; top: -10px; left: 0; right: 0; height: 1px; background: linear-gradient(90deg, transparent, #D4AF37, transparent); opacity: 0.3;"></div>
                    <div style="position: absolute; top: -8px; left: 0; right: 0; height: 1px; background: linear-gradient(90deg, transparent, #D4AF37, transparent); opacity: 0.2;"></div>
                    
                    <div style="display: flex; justify-content: space-between; font-size: 0.85em; background-color: rgba(47, 79, 79, 0.03); padding: 15px; border-radius: 6px; border: 1px solid rgba(212, 175, 55, 0.2); box-shadow: 0 1px 3px rgba(0,0,0,0.05);">
                        <div>
                            <p style="margin: 3px 0;"><strong style="color: #2F4F4F;">ЕДБ 4080025630210</strong></p>
                            <p style="margin: 3px 0; color: #000;">Сметка: 210 078354340265</p>
                        </div>
                        <div>
                            <p style="margin: 3px 0;"><strong style="color: #2F4F4F;">МБ 7835434</strong></p>
                            <p style="margin: 3px 0; color: #000;">Банка: НЛБ Банка АД Скопје</p>
                        </div>
                        <div>
                            <p style="margin: 3px 0;"><strong style="color: #2F4F4F;">Контакт маил: <EMAIL></strong></p>
                            <p style="margin: 3px 0; color: #000;">Адреса: Ул.11 Октомври бр.86/1-1 , Скопје</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    tempDiv.innerHTML = approvalHtml;
    document.body.appendChild(tempDiv);
    
    // Use html2canvas to capture the element as an image
    html2canvas(tempDiv, {
        scale: 1.5,
        useCORS: true,
        logging: false,
        allowTaint: true,
        backgroundColor: '#ffffff'
    }).then(canvas => {
        // Remove the temporary div
        document.body.removeChild(tempDiv);
        
        // Initialize jsPDF
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF('p', 'mm', 'a4', true);
        
        // Calculate dimensions to fill the page
        const imgData = canvas.toDataURL('image/jpeg', 0.8);
        const pageWidth = doc.internal.pageSize.getWidth();
        const pageHeight = doc.internal.pageSize.getHeight();
        
        // Use margins
        const margin = 5;
        const usableWidth = pageWidth - (margin * 2);
        const usableHeight = pageHeight - (margin * 2);
        
        // Add the image to the PDF
        doc.addImage(imgData, 'JPEG', margin, margin, usableWidth, usableHeight);
        
        // Generate filename
        const fileName = `odobruvanje_${brojNaPolisa || recordId}_${new Date().toISOString().slice(0, 10)}.pdf`;
        
        // Save the PDF
        doc.save(fileName);
    }).catch(function(error) {
        console.error('Error generating approval PDF: ', error);
        alert('Грешка при генерирање на PDF: ' + error);
    });
}
</script>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <!-- Add jsPDF and html2canvas libraries for PDF generation -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
}

<style>
    #klientOdobruvanjeSearchResults .list-group-item.text-center {
        padding: 1rem;
    }
    #klientOdobruvanjeSearchResults .list-group-item.text-center p {
        color: #6c757d;
        margin-bottom: 0.75rem;
    }
    #klientOdobruvanjeSearchResults .list-group-item-action:hover {
        background-color: #f8f9fa;
    }
    #klientOdobruvanjeSearchResults .list-group-item h6 {
        color: #495057;
        font-weight: 600;
    }
    #klientOdobruvanjeSearchResults .list-group-item p {
        color: #6c757d;
        font-size: 0.875rem;
    }
    #klientOdobruvanjeSearchResults .list-group-item small {
        color: #28a745;
        font-weight: 500;
    }
</style>