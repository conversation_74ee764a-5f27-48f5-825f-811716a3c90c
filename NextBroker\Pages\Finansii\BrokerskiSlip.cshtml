@page
@model NextBroker.Pages.Finansii.BrokerskiSlipModel
@{
    ViewData["Title"] = "Брокерски Слип";
    Layout = "_Layout";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h2>Брокерски Слип</h2>

            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    @TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            @if (TempData["ErrorMessage"] != null)
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    @TempData["ErrorMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            <!-- Collapsible Form -->
            <div class="card mb-4">
                <div class="card-header">
                    <button class="btn btn-primary" type="button" data-bs-toggle="collapse" data-bs-target="#addEntryForm" aria-expanded="false" aria-controls="addEntryForm">
                        <i class="fas fa-plus"></i> Додај нов запис
                    </button>
                </div>
                <div class="collapse" id="addEntryForm">
                    <div class="card-body">
                        <form method="post">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="Input.OsiguritelnoBrokerskoDrustvo" class="form-label"></label>
                                        <input asp-for="Input.OsiguritelnoBrokerskoDrustvo" class="form-control" />
                                        <span asp-validation-for="Input.OsiguritelnoBrokerskoDrustvo" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="Input.Osiguritel" class="form-label"></label>
                                        <select asp-for="Input.Osiguritel" asp-items="Model.Osiguriteli" class="form-select">
                                            <option value="">-- Избери осигурител --</option>
                                        </select>
                                        <span asp-validation-for="Input.Osiguritel" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="Input.Klasa" class="form-label"></label>
                                        <select asp-for="Input.Klasa" asp-items="Model.Klasi" class="form-select">
                                            <option value="">-- Избери класа --</option>
                                        </select>
                                        <span asp-validation-for="Input.Klasa" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="Input.Proizvod" class="form-label"></label>
                                        <select asp-for="Input.Proizvod" asp-items="Model.Produkti" class="form-select">
                                            <option value="">-- Избери производ --</option>
                                        </select>
                                        <span asp-validation-for="Input.Proizvod" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="Input.DogovoruvacImePrezimeNaziv" class="form-label"></label>
                                        <div class="position-relative">
                                            <div class="input-group">
                                                <button type="button" class="btn btn-outline-secondary btn-sm clear-field"
                                                        data-target="dogovoruvac"
                                                        style="width: 30px; height: 38px; padding: 0; margin-right: 5px;">
                                                    <i class="fas fa-eraser" style="font-size: 0.7rem;"></i>
                                                </button>
                                                <input type="text" id="dogovoruvacSearch" class="form-control"
                                                       autocomplete="off"
                                                       placeholder="Пребарувај по МБ, ЕДБ, ЕМБГ, име/назив..." />
                                                <input asp-for="Input.DogovoruvacImePrezimeNaziv" type="hidden" />
                                            </div>
                                            <div id="dogovoruvacSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1"
                                                 style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                                            </div>
                                        </div>
                                        <span asp-validation-for="Input.DogovoruvacImePrezimeNaziv" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="Input.EMBGMB" class="form-label"></label>
                                        <input asp-for="Input.EMBGMB" class="form-control" />
                                        <span asp-validation-for="Input.EMBGMB" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="Input.Osigurenik" class="form-label"></label>
                                        <div class="position-relative">
                                            <div class="input-group">
                                                <button type="button" class="btn btn-outline-secondary btn-sm clear-field"
                                                        data-target="osigurenik"
                                                        style="width: 30px; height: 38px; padding: 0; margin-right: 5px;">
                                                    <i class="fas fa-eraser" style="font-size: 0.7rem;"></i>
                                                </button>
                                                <input type="text" id="osigurenikSearch" class="form-control"
                                                       autocomplete="off"
                                                       placeholder="Пребарувај по МБ, ЕДБ, ЕМБГ, име/назив..." />
                                                <input asp-for="Input.Osigurenik" type="hidden" />
                                            </div>
                                            <div id="osigurenikSearchResults" class="position-absolute bg-white border rounded-2 w-100 mt-1"
                                                 style="display:none; z-index: 1000; max-height: 200px; overflow-y: auto;">
                                            </div>
                                        </div>
                                        <span asp-validation-for="Input.Osigurenik" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="Input.PredmetNaOsiguruvanje" class="form-label"></label>
                                        <input asp-for="Input.PredmetNaOsiguruvanje" class="form-control" />
                                        <span asp-validation-for="Input.PredmetNaOsiguruvanje" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="Input.OsiguritelnoPokriteOd" class="form-label"></label>
                                        <input asp-for="Input.OsiguritelnoPokriteOd" type="date" class="form-control" />
                                        <span asp-validation-for="Input.OsiguritelnoPokriteOd" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label asp-for="Input.OsiguritelnoPokriteDo" class="form-label"></label>
                                        <input asp-for="Input.OsiguritelnoPokriteDo" type="date" class="form-control" />
                                        <span asp-validation-for="Input.OsiguritelnoPokriteDo" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label asp-for="Input.Provizija" class="form-label"></label>
                                        <input asp-for="Input.Provizija" class="form-control" />
                                        <span asp-validation-for="Input.Provizija" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label asp-for="Input.PremijaGodishno" class="form-label"></label>
                                        <input asp-for="Input.PremijaGodishno" class="form-control" />
                                        <span asp-validation-for="Input.PremijaGodishno" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label asp-for="Input.PremijaVkupno" class="form-label"></label>
                                        <input asp-for="Input.PremijaVkupno" class="form-control" />
                                        <span asp-validation-for="Input.PremijaVkupno" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-12">
                                    <div class="mb-3">
                                        <label asp-for="Input.Zabeleshka" class="form-label"></label>
                                        <textarea asp-for="Input.Zabeleshka" class="form-control" rows="3"></textarea>
                                        <span asp-validation-for="Input.Zabeleshka" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-12">
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save"></i> Зачувај
                                    </button>
                                    <button type="button" class="btn btn-secondary" data-bs-toggle="collapse" data-bs-target="#addEntryForm">
                                        <i class="fas fa-times"></i> Откажи
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Data Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-table"></i> Листа на записи
                    </h5>
                </div>
                <div class="card-body">
                    @if (Model.Entries.Any())
                    {
                        <div class="table-responsive">
                            <table id="brokerskiSlipTable" class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Id</th>
                                        <th>Датум</th>
                                        <th>Корисник</th>
                                        <th>Брокерско друштво</th>
                                        <th>Осигурител</th>
                                        <th>Класа</th>
                                        <th>Производ</th>
                                        <th>Договорувач</th>
                                        <th>ЕМБ/МБ</th>
                                        <th>Осигуреник</th>
                                        <th>Предмет</th>
                                        <th>Покритие од</th>
                                        <th>Покритие до</th>
                                        <th>Провизија</th>
                                        <th>Премија годишно</th>
                                        <th>Премија вкупно</th>
                                        <th>Забелешка</th>
                                        <th>Акции</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var entry in Model.Entries)
                                    {
                                        <tr>
                                            <td>@entry.Id</td>
                                            <td>@entry.DateCreated?.ToString("dd.MM.yyyy HH:mm")</td>
                                            <td>@entry.UsernameCreated</td>
                                            <td>@entry.OsiguritelnoBrokerskoDrustvo</td>
                                            <td>@entry.Osiguritel</td>
                                            <td>@entry.Klasa</td>
                                            <td>@entry.Proizvod</td>
                                            <td>@entry.DogovoruvacImePrezimeNaziv</td>
                                            <td>@entry.EMBGMB</td>
                                            <td>@entry.Osigurenik</td>
                                            <td>@entry.PredmetNaOsiguruvanje</td>
                                            <td>@entry.OsiguritelnoPokriteOd</td>
                                            <td>@entry.OsiguritelnoPokriteDo</td>
                                            <td>@entry.Provizija</td>
                                            <td>@entry.PremijaGodishno</td>
                                            <td>@entry.PremijaVkupno</td>
                                            <td>@entry.Zabeleshka</td>
                                            <td>
                                                <button type="button"
                                                        class="btn btn-sm btn-danger generate-pdf-btn"
                                                        data-entry-id="@entry.Id"
                                                        title="Генерирај PDF документ">
                                                    <i class="fas fa-file-pdf"></i> Генерирај документ
                                                </button>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Нема записи за прикажување.</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Hidden div for PDF generation -->
<div id="pdfPreview" style="display: none; position: absolute; left: -9999px; top: -9999px;">
    <!-- PDF content will be inserted here -->
</div>

@section Styles {
    <style>
        #dogovoruvacSearchResults .list-group-item,
        #osigurenikSearchResults .list-group-item {
            cursor: pointer;
            border: none;
            border-bottom: 1px solid #dee2e6;
        }

        #dogovoruvacSearchResults .list-group-item:hover,
        #osigurenikSearchResults .list-group-item:hover {
            background-color: #f8f9fa;
        }

        #dogovoruvacSearchResults .list-group-item:last-child,
        #osigurenikSearchResults .list-group-item:last-child {
            border-bottom: none;
        }

        .client-info {
            font-size: 0.9rem;
        }

        .client-name {
            font-weight: 600;
            color: #2c3e50;
        }

        .client-details {
            color: #6c757d;
            font-size: 0.8rem;
        }
    </style>
}

@section Scripts {
    <!-- Include jsPDF and html2canvas libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script>
        // Function to create client search functionality
        function createClientSearchFunctionality(searchInputId, resultsContainerId, hiddenInputId) {
            let searchTimeout;
            const antiForgeryToken = $('input[name="__RequestVerificationToken"]').val();

            $(`#${searchInputId}`).on('input', function() {
                clearTimeout(searchTimeout);
                const searchTerm = $(this).val();
                const resultsDiv = $(`#${resultsContainerId}`);

                if (searchTerm.length < 1) {
                    resultsDiv.hide();
                    return;
                }

                searchTimeout = setTimeout(function() {
                    $.ajax({
                        url: '?handler=SearchKlienti',
                        type: 'GET',
                        data: { mb: searchTerm },
                        headers: {
                            "RequestVerificationToken": antiForgeryToken
                        },
                        success: function(data) {
                            if (data && data.length > 0) {
                                let list = '<div class="list-group">';
                                data.forEach(function(client) {
                                    const displayName = client.naziv || `${client.ime} ${client.prezime}`.trim();
                                    const details = [];
                                    if (client.mb) details.push(`МБ: ${client.mb}`);
                                    if (client.edb) details.push(`ЕДБ: ${client.edb}`);
                                    if (client.embg) details.push(`ЕМБГ: ${client.embg}`);

                                    list += `
                                        <div class="list-group-item client-search-item" data-client-name="${displayName}">
                                            <div class="client-info">
                                                <div class="client-name">${displayName}</div>
                                                <div class="client-details">${details.join(' | ')}</div>
                                            </div>
                                        </div>
                                    `;
                                });
                                list += '</div>';
                                resultsDiv.html(list);
                                resultsDiv.show();

                                // Handle client selection
                                resultsDiv.find('.client-search-item').on('click', function() {
                                    const clientName = $(this).data('client-name');
                                    $(`#${searchInputId}`).val(clientName);
                                    $(`#${hiddenInputId}`).val(clientName);
                                    resultsDiv.hide();
                                });
                            } else {
                                resultsDiv.hide();
                            }
                        },
                        error: function() {
                            resultsDiv.hide();
                        }
                    });
                }, 300);
            });

            // Hide results when clicking outside
            $(document).on('click', function(e) {
                if (!$(e.target).closest(`#${searchInputId}, #${resultsContainerId}`).length) {
                    $(`#${resultsContainerId}`).hide();
                }
            });
        }

        $(document).ready(function() {
            // Initialize DataTable with search functionality
            $('#brokerskiSlipTable').DataTable({
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.13.7/i18n/mk.json"
                },
                "pageLength": 25,
                "order": [[0, "desc"]], // Sort by date descending
                "columnDefs": [
                    { "orderable": false, "targets": [15, 16] } // Disable sorting for Zabeleshka and Akcii columns
                ],
                "scrollX": true,
                "responsive": true
            });

            // Handle PDF generation button clicks
            $('.generate-pdf-btn').on('click', function() {
                const entryId = $(this).data('entry-id');
                const button = $(this);
                generateBrokerskiSlipPDF(entryId, button);
            });

            // Initialize client search functionality
            createClientSearchFunctionality('dogovoruvacSearch', 'dogovoruvacSearchResults', 'Input_DogovoruvacImePrezimeNaziv');
            createClientSearchFunctionality('osigurenikSearch', 'osigurenikSearchResults', 'Input_Osigurenik');

            // Handle clear buttons
            $('.clear-field').on('click', function() {
                const target = $(this).data('target');
                if (target === 'dogovoruvac') {
                    $('#dogovoruvacSearch').val('');
                    $('#Input_DogovoruvacImePrezimeNaziv').val('');
                    $('#dogovoruvacSearchResults').hide();
                } else if (target === 'osigurenik') {
                    $('#osigurenikSearch').val('');
                    $('#Input_Osigurenik').val('');
                    $('#osigurenikSearchResults').hide();
                }
            });
        });

        function generateBrokerskiSlipPDF(entryId, button) {
            // Show loading state
            const originalText = button.html();
            button.html('<i class="fas fa-spinner fa-spin"></i> Генерирање...');
            button.prop('disabled', true);

            // Get the document HTML from server
            $.get('@Url.Page("/Finansii/BrokerskiSlip", "GenerateDocument")', { id: entryId })
                .done(function(response) {
                    if (response.success) {
                        createPDFFromHtml(response.html, entryId, button, originalText);
                    } else {
                        button.html(originalText);
                        button.prop('disabled', false);
                        alert('Грешка: ' + response.error);
                    }
                })
                .fail(function() {
                    button.html(originalText);
                    button.prop('disabled', false);
                    alert('Грешка при генерирање на документот.');
                });
        }

        function createPDFFromHtml(htmlContent, entryId, button, originalText) {
            // Insert HTML into hidden preview div
            const previewDiv = document.getElementById('pdfPreview');
            previewDiv.innerHTML = htmlContent;

            // Set up the preview div for proper PDF capture
            previewDiv.style.display = 'block';
            previewDiv.style.position = 'static';
            previewDiv.style.left = 'auto';
            previewDiv.style.top = 'auto';
            previewDiv.style.width = '794px'; // A4 width in pixels at 96 DPI
            previewDiv.style.padding = '20px';
            previewDiv.style.backgroundColor = '#ffffff';

            // Use html2canvas to capture the element
            html2canvas(previewDiv, {
                scale: 2, // Higher scale for better quality
                useCORS: true,
                logging: false,
                allowTaint: true,
                backgroundColor: '#ffffff',
                width: 794 + 40, // Include padding
                height: null // Let it calculate height automatically
            }).then(canvas => {
                // Hide the preview div again
                previewDiv.style.display = 'none';
                previewDiv.style.position = 'absolute';
                previewDiv.style.left = '-9999px';
                previewDiv.style.top = '-9999px';
                previewDiv.style.width = 'auto';
                previewDiv.style.padding = '0';

                // Initialize jsPDF
                const { jsPDF } = window.jspdf;
                const doc = new jsPDF('p', 'mm', 'a4', true);

                // Calculate dimensions to fill the page
                const imgData = canvas.toDataURL('image/jpeg', 0.9);
                const pageWidth = doc.internal.pageSize.getWidth();
                const pageHeight = doc.internal.pageSize.getHeight();

                // Calculate the aspect ratio of the captured image
                const imgWidth = canvas.width;
                const imgHeight = canvas.height;
                const imgAspectRatio = imgWidth / imgHeight;

                // Calculate dimensions to fit the page while maintaining aspect ratio
                let pdfWidth = pageWidth;
                let pdfHeight = pageWidth / imgAspectRatio;

                // If the calculated height exceeds page height, scale down
                if (pdfHeight > pageHeight) {
                    pdfHeight = pageHeight;
                    pdfWidth = pageHeight * imgAspectRatio;
                }

                // Center the image on the page
                const x = (pageWidth - pdfWidth) / 2;
                const y = (pageHeight - pdfHeight) / 2;

                // Add image to PDF with calculated dimensions
                doc.addImage(imgData, 'JPEG', x, y, pdfWidth, pdfHeight);

                // Generate filename and save
                const fileName = `BrokerskiSlip_${entryId}_${new Date().toISOString().slice(0, 10)}.pdf`;
                doc.save(fileName);

                // Restore button state
                button.html(originalText);
                button.prop('disabled', false);
            }).catch(error => {
                console.error('Error generating PDF:', error);
                button.html(originalText);
                button.prop('disabled', false);
                alert('Грешка при генерирање на PDF документот.');
            });
        }
    </script>
}