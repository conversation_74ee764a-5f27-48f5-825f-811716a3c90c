using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Data.SqlClient;
using System;
using Microsoft.AspNetCore.Antiforgery;
using System.Text.Json;
using ExcelDataReader;
using System.Text;
using System.IO;

namespace NextBroker.Pages.Polisi
{
    public class EditOsigureniciKolektivnoModel : SecurePageModel
    {
        private readonly IConfiguration _configuration;
        private readonly IAntiforgery _antiforgery;

        public EditOsigureniciKolektivnoModel(IConfiguration configuration, IAntiforgery antiforgery)
            : base(configuration)
        {
            _configuration = configuration;
            _antiforgery = antiforgery;
        }

        [BindProperty]
        public long PolisaId { get; set; }

        public int? KlasaId { get; set; }

        public DateTime? PolisaDatumVaziOd { get; set; }
        public DateTime? PolisaDatumVaziDo { get; set; }

        public IEnumerable<SelectListItem> Opstini { get; set; }

        public class OsigurenikUpdateModel
        {
            public long Id { get; set; }
            public long PolisaId { get; set; }
            public string Ime { get; set; }
            public string Prezime { get; set; }
            public string EMBG { get; set; }
            public string BrojNaLicnaKarta { get; set; }
            public string BrojNaPasos { get; set; }
            public string ListaOpstiniId { get; set; }
            public string Adresa { get; set; }
            public string Broj { get; set; }
            public string Telefon { get; set; }
            public string Email { get; set; }
            public DateTime? DatumOsiguruvanjeVaziOd { get; set; }
            public DateTime? DatumOsiguruvanjeVaziDo { get; set; }
        }

        public class OsigurenikKolektivno
        {
            public long Id { get; set; }
            public string Ime { get; set; }
            public string Prezime { get; set; }
            public string EMBG { get; set; }
            public string BrojNaLicnaKarta { get; set; }
            public string BrojNaPasos { get; set; }
            public string ListaOpstiniId { get; set; }
            public string OpstinaNaziv { get; set; }
            public string Adresa { get; set; }
            public string Broj { get; set; }
            public string Telefon { get; set; }
            public string Email { get; set; }
            public DateTime? DatumOsiguruvanjeVaziOd { get; set; }
            public DateTime? DatumOsiguruvanjeVaziDo { get; set; }
        }

        public List<OsigurenikKolektivno> OsigureniciKolektivno { get; set; }

        public class OsigurenikAddModel
        {
            public long PolisaId { get; set; }
            public string Ime { get; set; }
            public string Prezime { get; set; }
            public string EMBG { get; set; }
            public string BrojNaLicnaKarta { get; set; }
            public string BrojNaPasos { get; set; }
            public string ListaOpstiniId { get; set; }
            public string Adresa { get; set; }
            public string Broj { get; set; }
            public string Telefon { get; set; }
            public string Email { get; set; }
            public DateTime? DatumOsiguruvanjeVaziOd { get; set; }
            public DateTime? DatumOsiguruvanjeVaziDo { get; set; }
        }

        public class ExcelImportResult
        {
            public bool Success { get; set; }
            public string Message { get; set; }
            public List<string> InvalidRows { get; set; } = new List<string>();
        }

        public class DeleteOsigurenikModel
        {
            public long Id { get; set; }
        }

        public class MonthlyCalculationModel
        {
            public int MesecBroj { get; set; }
            public DateTime MesecPocetok { get; set; }
            public DateTime MesecKraj { get; set; }
            public int AktivniOsigureniciBroj { get; set; }
            public decimal VkupnaPremijaZaEdnoLice { get; set; }
            public decimal MesecnaPremijaZaEdnoLice { get; set; }
            public int VkupnoMesecinaPolisa { get; set; }
            public decimal MesecnaPremija { get; set; }
            public decimal VkupnaPremijaPolisa { get; set; }
        }

        public class MonthlySummaryModel
        {
            public int VkupnoMesecinaPolisa { get; set; }
            public decimal VkupnaPremijaZaEdnoLice { get; set; }
            public decimal MesecnaPremijaZaEdnoLice { get; set; }
            public int VkupnoMesecina { get; set; }
            public int VkupnoOsigureniciMesecina { get; set; }
            public decimal VkupnaPremijaPolisa { get; set; }
            public decimal ProsekOsigureniciPoMesec { get; set; }
            public decimal Razlika { get; set; }
        }

        public List<MonthlyCalculationModel> MesecnaPrestmetka { get; set; }
        public MonthlySummaryModel MesecnaPrestmetkaSumarno { get; set; }

        private async Task LoadOpstini()
        {
            var items = new List<SelectListItem>();
            
            using (SqlConnection connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Opstina 
                    FROM ListaOpstini 
                    ORDER BY Opstina", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Opstina"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                }
            }
            
            Opstini = items;
        }

        private async Task LoadOsigureniciKolektivno()
        {
            OsigureniciKolektivno = new List<OsigurenikKolektivno>();
            
            using (SqlConnection connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();

                string query = @"
                    SELECT 
                        pok.*,
                        lo.Opstina as OpstinaNaziv
                    FROM PolisiOsigureniciKolektivno pok
                    LEFT JOIN ListaOpstini lo ON lo.Id = TRY_CAST(pok.ListaOpstiniId AS INT)
                    WHERE pok.PolisaId = @PolisaId
                    ORDER BY pok.Id";

                using (SqlCommand command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@PolisaId", PolisaId);

                    using (SqlDataReader reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            OsigureniciKolektivno.Add(new OsigurenikKolektivno
                            {
                                Id = reader.GetInt64(reader.GetOrdinal("Id")),
                                Ime = reader.IsDBNull(reader.GetOrdinal("Ime")) ? null : reader.GetString(reader.GetOrdinal("Ime")),
                                Prezime = reader.IsDBNull(reader.GetOrdinal("Prezime")) ? null : reader.GetString(reader.GetOrdinal("Prezime")),
                                EMBG = reader.IsDBNull(reader.GetOrdinal("EMBG")) ? null : reader.GetString(reader.GetOrdinal("EMBG")),
                                BrojNaLicnaKarta = reader.IsDBNull(reader.GetOrdinal("BrojNaLicnaKarta")) ? null : reader.GetString(reader.GetOrdinal("BrojNaLicnaKarta")),
                                BrojNaPasos = reader.IsDBNull(reader.GetOrdinal("BrojNaPasos")) ? null : reader.GetString(reader.GetOrdinal("BrojNaPasos")),
                                ListaOpstiniId = reader.IsDBNull(reader.GetOrdinal("ListaOpstiniId")) ? null : reader.GetString(reader.GetOrdinal("ListaOpstiniId")),
                                OpstinaNaziv = reader.IsDBNull(reader.GetOrdinal("OpstinaNaziv")) ? null : reader.GetString(reader.GetOrdinal("OpstinaNaziv")),
                                Adresa = reader.IsDBNull(reader.GetOrdinal("Adresa")) ? null : reader.GetString(reader.GetOrdinal("Adresa")),
                                Broj = reader.IsDBNull(reader.GetOrdinal("Broj")) ? null : reader.GetString(reader.GetOrdinal("Broj")),
                                Telefon = reader.IsDBNull(reader.GetOrdinal("Telefon")) ? null : reader.GetString(reader.GetOrdinal("Telefon")),
                                Email = reader.IsDBNull(reader.GetOrdinal("Email")) ? null : reader.GetString(reader.GetOrdinal("Email")),
                                DatumOsiguruvanjeVaziOd = reader.IsDBNull(reader.GetOrdinal("DatumOsiguruvanjeVaziOd")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("DatumOsiguruvanjeVaziOd")),
                                DatumOsiguruvanjeVaziDo = reader.IsDBNull(reader.GetOrdinal("DatumOsiguruvanjeVaziDo")) ? (DateTime?)null : reader.GetDateTime(reader.GetOrdinal("DatumOsiguruvanjeVaziDo"))
                            });
                        }
                    }
                }
            }
        }

        public async Task<IActionResult> OnGet(long id)
        {
            PolisaId = id;

            // Get KlasaId from Polisi
            using (SqlConnection connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT KlasiOsiguruvanjeIdKlasa, DatumVaziOd, DatumVaziDo
                    FROM Polisi 
                    WHERE Id = @Id", connection))
                {
                    cmd.Parameters.AddWithValue("@Id", id);
                    using (var reader = await cmd.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                            if (!reader.IsDBNull(reader.GetOrdinal("KlasiOsiguruvanjeIdKlasa")))
                            {
                                KlasaId = reader.GetInt32(reader.GetOrdinal("KlasiOsiguruvanjeIdKlasa"));
                            }
                            
                            if (!reader.IsDBNull(reader.GetOrdinal("DatumVaziOd")))
                            {
                                PolisaDatumVaziOd = reader.GetDateTime(reader.GetOrdinal("DatumVaziOd"));
                            }
                            
                            if (!reader.IsDBNull(reader.GetOrdinal("DatumVaziDo")))
                            {
                                PolisaDatumVaziDo = reader.GetDateTime(reader.GetOrdinal("DatumVaziDo"));
                            }
                        }
                    }
                }
            }

            await LoadOpstini();
            await LoadOsigureniciKolektivno();
            await LoadMesecnaPrestmetka();
            return Page();
        }

        public async Task<IActionResult> OnPostUpdateOsigurenikAsync([FromBody] OsigurenikUpdateModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return new JsonResult(new { 
                        success = false, 
                        message = "Invalid model state",
                        errors = ModelState.Values
                            .SelectMany(v => v.Errors)
                            .Select(e => e.ErrorMessage)
                    });
                }

                // Get policy dates if needed
                if (!model.DatumOsiguruvanjeVaziOd.HasValue || !model.DatumOsiguruvanjeVaziDo.HasValue)
                {
                    using (SqlConnection connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                    {
                        await connection.OpenAsync();
                        using (SqlCommand cmd = new SqlCommand(@"
                            SELECT DatumVaziOd, DatumVaziDo 
                            FROM Polisi 
                            WHERE Id = @Id", connection))
                        {
                            cmd.Parameters.AddWithValue("@Id", model.PolisaId);
                            using (var reader = await cmd.ExecuteReaderAsync())
                            {
                                if (await reader.ReadAsync())
                                {
                                    if (!model.DatumOsiguruvanjeVaziOd.HasValue && !reader.IsDBNull(reader.GetOrdinal("DatumVaziOd")))
                                    {
                                        model.DatumOsiguruvanjeVaziOd = reader.GetDateTime(reader.GetOrdinal("DatumVaziOd"));
                                    }
                                    
                                    if (!model.DatumOsiguruvanjeVaziDo.HasValue && !reader.IsDBNull(reader.GetOrdinal("DatumVaziDo")))
                                    {
                                        model.DatumOsiguruvanjeVaziDo = reader.GetDateTime(reader.GetOrdinal("DatumVaziDo"));
                                    }
                                }
                            }
                        }
                    }
                }

                using (SqlConnection connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();
                    using (SqlCommand cmd = new SqlCommand(@"
                        UPDATE PolisiOsigureniciKolektivno 
                        SET Ime = @Ime,
                            Prezime = @Prezime,
                            EMBG = @EMBG,
                            BrojNaLicnaKarta = @BrojNaLicnaKarta,
                            BrojNaPasos = @BrojNaPasos,
                            ListaOpstiniId = @ListaOpstiniId,
                            Adresa = @Adresa,
                            Broj = @Broj,
                            Telefon = @Telefon,
                            Email = @Email,
                            DatumOsiguruvanjeVaziOd = @DatumOsiguruvanjeVaziOd,
                            DatumOsiguruvanjeVaziDo = @DatumOsiguruvanjeVaziDo
                        WHERE Id = @Id AND PolisaId = @PolisaId", connection))
                    {
                        cmd.Parameters.AddWithValue("@Id", model.Id);
                        cmd.Parameters.AddWithValue("@PolisaId", model.PolisaId);
                        cmd.Parameters.AddWithValue("@Ime", (object)model.Ime ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Prezime", (object)model.Prezime ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@EMBG", (object)model.EMBG ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@BrojNaLicnaKarta", (object)model.BrojNaLicnaKarta ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@BrojNaPasos", (object)model.BrojNaPasos ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@ListaOpstiniId", (object)model.ListaOpstiniId ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Adresa", (object)model.Adresa ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Broj", (object)model.Broj ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Telefon", (object)model.Telefon ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Email", (object)model.Email ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@DatumOsiguruvanjeVaziOd", (object)model.DatumOsiguruvanjeVaziOd ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@DatumOsiguruvanjeVaziDo", (object)model.DatumOsiguruvanjeVaziDo ?? DBNull.Value);

                        int rowsAffected = await cmd.ExecuteNonQueryAsync();
                        if (rowsAffected > 0)
                        {
                            return new JsonResult(new { success = true });
                        }
                    }
                }

                return new JsonResult(new { success = false, message = "No records were updated" });
            }
            catch (Exception ex)
            {
                return new JsonResult(new { 
                    success = false, 
                    message = ex.Message,
                    details = ex.StackTrace 
                });
            }
        }

        public async Task<IActionResult> OnPostDeleteOsigurenikAsync([FromBody] DeleteOsigurenikModel model)
        {
            try
            {
                using (SqlConnection connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();
                    using (SqlCommand cmd = new SqlCommand(@"
                        DELETE FROM PolisiOsigureniciKolektivno 
                        WHERE Id = @Id", connection))
                    {
                        cmd.Parameters.AddWithValue("@Id", model.Id);

                        int rowsAffected = await cmd.ExecuteNonQueryAsync();
                        if (rowsAffected > 0)
                        {
                            return new JsonResult(new { success = true });
                        }
                    }
                }

                return new JsonResult(new { success = false, message = "No records were deleted" });
            }
            catch (Exception ex)
            {
                return new JsonResult(new { 
                    success = false, 
                    message = ex.Message,
                    details = ex.StackTrace 
                });
            }
        }

        public async Task<IActionResult> OnPostAddOsigurenikAsync([FromBody] OsigurenikAddModel model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return new JsonResult(new { 
                        success = false, 
                        message = "Invalid model state",
                        errors = ModelState.Values
                            .SelectMany(v => v.Errors)
                            .Select(e => e.ErrorMessage)
                    });
                }

                var username = HttpContext.Session.GetString("Username");
                if (string.IsNullOrEmpty(username))
                {
                    return new JsonResult(new { 
                        success = false, 
                        message = "User session expired. Please log in again." 
                    });
                }

                // Get policy dates if needed
                if (!model.DatumOsiguruvanjeVaziOd.HasValue || !model.DatumOsiguruvanjeVaziDo.HasValue)
                {
                    using (SqlConnection connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                    {
                        await connection.OpenAsync();
                        using (SqlCommand cmd = new SqlCommand(@"
                            SELECT DatumVaziOd, DatumVaziDo 
                            FROM Polisi 
                            WHERE Id = @Id", connection))
                        {
                            cmd.Parameters.AddWithValue("@Id", model.PolisaId);
                            using (var reader = await cmd.ExecuteReaderAsync())
                            {
                                if (await reader.ReadAsync())
                                {
                                    if (!model.DatumOsiguruvanjeVaziOd.HasValue && !reader.IsDBNull(reader.GetOrdinal("DatumVaziOd")))
                                    {
                                        model.DatumOsiguruvanjeVaziOd = reader.GetDateTime(reader.GetOrdinal("DatumVaziOd"));
                                    }
                                    
                                    if (!model.DatumOsiguruvanjeVaziDo.HasValue && !reader.IsDBNull(reader.GetOrdinal("DatumVaziDo")))
                                    {
                                        model.DatumOsiguruvanjeVaziDo = reader.GetDateTime(reader.GetOrdinal("DatumVaziDo"));
                                    }
                                }
                            }
                        }
                    }
                }

                using (SqlConnection connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                {
                    await connection.OpenAsync();
                    using (SqlCommand cmd = new SqlCommand(@"
                        INSERT INTO PolisiOsigureniciKolektivno (
                            PolisaId, Ime, Prezime, EMBG, BrojNaLicnaKarta, BrojNaPasos,
                            ListaOpstiniId, Adresa, Broj, Telefon, Email, DateCreated, UsernameCreated,
                            DatumOsiguruvanjeVaziOd, DatumOsiguruvanjeVaziDo
                        ) VALUES (
                            @PolisaId, @Ime, @Prezime, @EMBG, @BrojNaLicnaKarta, @BrojNaPasos,
                            @ListaOpstiniId, @Adresa, @Broj, @Telefon, @Email, GETDATE(), @UsernameCreated,
                            @DatumOsiguruvanjeVaziOd, @DatumOsiguruvanjeVaziDo
                        )", connection))
                    {
                        cmd.Parameters.AddWithValue("@PolisaId", model.PolisaId);
                        cmd.Parameters.AddWithValue("@Ime", (object)model.Ime ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Prezime", (object)model.Prezime ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@EMBG", (object)model.EMBG ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@BrojNaLicnaKarta", (object)model.BrojNaLicnaKarta ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@BrojNaPasos", (object)model.BrojNaPasos ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@ListaOpstiniId", (object)model.ListaOpstiniId ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Adresa", (object)model.Adresa ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Broj", (object)model.Broj ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Telefon", (object)model.Telefon ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@Email", (object)model.Email ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@UsernameCreated", username);
                        cmd.Parameters.AddWithValue("@DatumOsiguruvanjeVaziOd", (object)model.DatumOsiguruvanjeVaziOd ?? DBNull.Value);
                        cmd.Parameters.AddWithValue("@DatumOsiguruvanjeVaziDo", (object)model.DatumOsiguruvanjeVaziDo ?? DBNull.Value);

                        await cmd.ExecuteNonQueryAsync();
                        return new JsonResult(new { success = true });
                    }
                }
            }
            catch (Exception ex)
            {
                return new JsonResult(new { 
                    success = false, 
                    message = ex.Message,
                    details = ex.StackTrace 
                });
            }
        }

        public async Task<IActionResult> OnPostImportExcelAsync(IFormFile excelFile)
        {
            try
            {
                // Get PolisaId from route parameter
                if (RouteData?.Values["id"] == null || !long.TryParse(RouteData.Values["id"].ToString(), out long polisaId))
                {
                    return new JsonResult(new ExcelImportResult 
                    { 
                        Success = false, 
                        Message = "Невалиден ID на полиса"
                    });
                }

                if (excelFile == null || excelFile.Length == 0)
                {
                    return new JsonResult(new ExcelImportResult 
                    { 
                        Success = false, 
                        Message = "Не е избран документ"
                    });
                }

                var invalidRows = new List<string>();
                var successCount = 0;
                var username = HttpContext.Session.GetString("Username");

                if (string.IsNullOrEmpty(username))
                {
                    return new JsonResult(new ExcelImportResult 
                    { 
                        Success = false, 
                        Message = "Корисничката сесија е истечена"
                    });
                }

                Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);

                using (var stream = excelFile.OpenReadStream())
                using (var reader = ExcelReaderFactory.CreateReader(stream))
                {
                    var result = reader.AsDataSet(new ExcelDataSetConfiguration
                    {
                        ConfigureDataTable = (_) => new ExcelDataTableConfiguration
                        {
                            UseHeaderRow = true
                        }
                    });

                    var table = result.Tables[0];
                    var opstiniKodToId = new Dictionary<int, string>();

                    // Load Opstini Kod to Id mapping
                    using (SqlConnection connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
                    {
                        await connection.OpenAsync();
                        using (SqlCommand cmd = new SqlCommand(@"
                            SELECT Id, Kod 
                            FROM ListaOpstini", connection))
                        {
                            using SqlDataReader dbReader = await cmd.ExecuteReaderAsync();
                            while (await dbReader.ReadAsync())
                            {
                                opstiniKodToId[dbReader.GetInt32(1)] = dbReader.GetInt32(0).ToString();
                            }
                        }

                        // Process each row
                        for (int row = 0; row < table.Rows.Count; row++)
                        {
                            try
                            {
                                var rowNum = row + 2; // Excel row number (1-based, accounting for header)
                                var rowData = table.Rows[row];

                                // Get OpstinaId from Kod
                                var opstinaKod = Convert.ToInt32(rowData["OpstinaKod"]);
                                if (!opstiniKodToId.TryGetValue(opstinaKod, out string opstinaId))
                                {
                                    invalidRows.Add($"Ред {rowNum}: Невалиден код на општина ({opstinaKod})");
                                    continue;
                                }

                                // Validate based on KlasaId
                                var brojNaPasos = rowData["BrojNaPasos"]?.ToString();
                                var brojNaLicnaKarta = rowData["BrojNaLicnaKarta"]?.ToString();

                                if (KlasaId == 18)
                                {
                                    if (string.IsNullOrEmpty(brojNaPasos))
                                    {
                                        invalidRows.Add($"Ред {rowNum}: За класа 18, бројот на пасош е задолжителен");
                                        continue;
                                    }
                                }
                                else
                                {
                                    /*
                                    if (string.IsNullOrEmpty(brojNaLicnaKarta))
                                    {
                                        invalidRows.Add($"Ред {rowNum}: Бројот на лична карта е задолжителен");
                                        continue;
                                    }
                                    */
                                }

                                using (SqlCommand cmd = new SqlCommand(@"
                                    INSERT INTO PolisiOsigureniciKolektivno (
                                        PolisaId, Ime, Prezime, EMBG, BrojNaLicnaKarta, BrojNaPasos,
                                        ListaOpstiniId, Adresa, Broj, Telefon, Email, DateCreated, UsernameCreated,
                                        DatumOsiguruvanjeVaziOd, DatumOsiguruvanjeVaziDo
                                    ) VALUES (
                                        @PolisaId, @Ime, @Prezime, @EMBG, @BrojNaLicnaKarta, @BrojNaPasos,
                                        @ListaOpstiniId, @Adresa, @Broj, @Telefon, @Email, GETDATE(), @UsernameCreated,
                                        @DatumOsiguruvanjeVaziOd, @DatumOsiguruvanjeVaziDo
                                    )", connection))
                                {
                                    cmd.Parameters.Clear();
                                    cmd.Parameters.AddWithValue("@PolisaId", polisaId);
                                    cmd.Parameters.AddWithValue("@Ime", (object)rowData["Ime"]?.ToString() ?? DBNull.Value);
                                    cmd.Parameters.AddWithValue("@Prezime", (object)rowData["Prezime"]?.ToString() ?? DBNull.Value);
                                    cmd.Parameters.AddWithValue("@EMBG", (object)rowData["EMBG"]?.ToString() ?? DBNull.Value);
                                    cmd.Parameters.AddWithValue("@BrojNaLicnaKarta", (object)brojNaLicnaKarta ?? DBNull.Value);
                                    cmd.Parameters.AddWithValue("@BrojNaPasos", (object)brojNaPasos ?? DBNull.Value);
                                    cmd.Parameters.AddWithValue("@ListaOpstiniId", opstinaId);
                                    cmd.Parameters.AddWithValue("@Adresa", (object)rowData["Adresa"]?.ToString() ?? DBNull.Value);
                                    cmd.Parameters.AddWithValue("@Broj", (object)rowData["Broj"]?.ToString() ?? DBNull.Value);
                                    cmd.Parameters.AddWithValue("@Telefon", (object)rowData["Telefon"]?.ToString() ?? DBNull.Value);
                                    cmd.Parameters.AddWithValue("@Email", (object)rowData["Email"]?.ToString() ?? DBNull.Value);
                                    cmd.Parameters.AddWithValue("@UsernameCreated", username);
                                    
                                    // Handle date parsing for Excel import
                                    DateTime? datumOd = null;
                                    DateTime? datumDo = null;
                                    
                                    if (rowData["DatumOsiguruvanjeVaziOd"] != null && rowData["DatumOsiguruvanjeVaziOd"] != DBNull.Value)
                                    {
                                        if (DateTime.TryParse(rowData["DatumOsiguruvanjeVaziOd"].ToString(), out DateTime parsedOd))
                                        {
                                            datumOd = parsedOd;
                                        }
                                    }
                                    else
                                    {
                                        // Use policy date as default if not provided in Excel
                                        datumOd = PolisaDatumVaziOd;
                                    }
                                    
                                    if (rowData["DatumOsiguruvanjeVaziDo"] != null && rowData["DatumOsiguruvanjeVaziDo"] != DBNull.Value)
                                    {
                                        if (DateTime.TryParse(rowData["DatumOsiguruvanjeVaziDo"].ToString(), out DateTime parsedDo))
                                        {
                                            datumDo = parsedDo;
                                        }
                                    }
                                    else
                                    {
                                        // Use policy date as default if not provided in Excel
                                        datumDo = PolisaDatumVaziDo;
                                    }
                                    
                                    cmd.Parameters.AddWithValue("@DatumOsiguruvanjeVaziOd", (object)datumOd ?? DBNull.Value);
                                    cmd.Parameters.AddWithValue("@DatumOsiguruvanjeVaziDo", (object)datumDo ?? DBNull.Value);

                                    await cmd.ExecuteNonQueryAsync();
                                    successCount++;
                                }
                            }
                            catch (Exception ex)
                            {
                                invalidRows.Add($"Ред {row + 2}: {ex.Message}");
                            }
                        }
                    }

                    var message = $"Успешно внесени {successCount} осигуреници.";
                    if (invalidRows.Any())
                    {
                        message += $" {invalidRows.Count} редови не беа внесени поради грешки.";
                    }

                    return new JsonResult(new ExcelImportResult 
                    { 
                        Success = true, 
                        Message = message,
                        InvalidRows = invalidRows
                    });
                }
            }
            catch (Exception ex)
            {
                return new JsonResult(new ExcelImportResult 
                { 
                    Success = false, 
                    Message = ex.Message
                });
            }
        }

        private async Task LoadMesecnaPrestmetka()
        {
            MesecnaPrestmetka = new List<MonthlyCalculationModel>();
            MesecnaPrestmetkaSumarno = new MonthlySummaryModel();

            using (SqlConnection connection = new SqlConnection(_configuration.GetConnectionString("DefaultConnection")))
            {
                await connection.OpenAsync();

                // First, get the policy details and calculate premium per person
                decimal premijaZaEdnoLice = 0;
                DateTime? datumPolisaVaziOd = null;
                DateTime? datumPolisaVaziDo = null;
                int? klasaOsiguruvanjeId = null;

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT KlasiOsiguruvanjeIdKlasa, DatumVaziOd, DatumVaziDo
                    FROM Polisi 
                    WHERE Id = @PolisaId", connection))
                {
                    cmd.Parameters.AddWithValue("@PolisaId", PolisaId);
                    using (var reader = await cmd.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                            if (!reader.IsDBNull(reader.GetOrdinal("KlasiOsiguruvanjeIdKlasa")))
                            {
                                klasaOsiguruvanjeId = reader.GetInt32(reader.GetOrdinal("KlasiOsiguruvanjeIdKlasa"));
                            }
                            
                            if (!reader.IsDBNull(reader.GetOrdinal("DatumVaziOd")))
                            {
                                datumPolisaVaziOd = reader.GetDateTime(reader.GetOrdinal("DatumVaziOd"));
                            }
                            
                            if (!reader.IsDBNull(reader.GetOrdinal("DatumVaziDo")))
                            {
                                datumPolisaVaziDo = reader.GetDateTime(reader.GetOrdinal("DatumVaziDo"));
                            }
                        }
                    }
                }

                if (!datumPolisaVaziOd.HasValue || !datumPolisaVaziDo.HasValue || !klasaOsiguruvanjeId.HasValue)
                {
                    return;
                }

                // Get premium per person based on class
                string tableName = $"PolisiKlasa{klasaOsiguruvanjeId}";
                using (SqlCommand cmd = new SqlCommand($@"
                    SELECT PremijaZaEdnoLice 
                    FROM {tableName} 
                    WHERE PolisaId = @PolisaId", connection))
                {
                    cmd.Parameters.AddWithValue("@PolisaId", PolisaId);
                    var result = await cmd.ExecuteScalarAsync();
                    if (result != null && result != DBNull.Value)
                    {
                        premijaZaEdnoLice = Convert.ToDecimal(result);
                    }
                }

                if (premijaZaEdnoLice == 0)
                {
                    return;
                }

                // Calculate total months and monthly premium
                int totalPolicyMonths = ((datumPolisaVaziDo.Value.Year - datumPolisaVaziOd.Value.Year) * 12) + 
                                       datumPolisaVaziDo.Value.Month - datumPolisaVaziOd.Value.Month;
                decimal monthlyPremijaZaEdnoLice = premijaZaEdnoLice / totalPolicyMonths;

                // Execute the monthly calculation query
                string monthlyQuery = @"
                    WITH MesecniPeriodi AS (
                        SELECT 
                            @DatumPolisaVaziOd AS MesecPocetok,
                            DATEADD(DAY, -1, DATEADD(MONTH, 1, @DatumPolisaVaziOd)) AS MesecKraj,
                            1 AS MesecBroj
                        
                        UNION ALL
                        
                        SELECT 
                            DATEADD(MONTH, 1, MesecPocetok) AS MesecPocetok,
                            DATEADD(DAY, -1, DATEADD(MONTH, 1, DATEADD(MONTH, 1, MesecPocetok))) AS MesecKraj,
                            MesecBroj + 1
                        FROM MesecniPeriodi
                        WHERE DATEADD(MONTH, 1, MesecPocetok) < @DatumPolisaVaziDo
                    ),
                    MesecniOsigureniciBroj AS (
                        SELECT 
                            mp.MesecPocetok,
                            mp.MesecKraj,
                            mp.MesecBroj,
                            COUNT(pok.Id) AS AktivniOsigureniciBroj
                        FROM MesecniPeriodi mp
                        LEFT JOIN PolisiOsigureniciKolektivno pok 
                            ON pok.PolisaId = @PolisaId
                            AND pok.DatumOsiguruvanjeVaziOd <= mp.MesecKraj
                            AND pok.DatumOsiguruvanjeVaziDo >= mp.MesecPocetok
                        GROUP BY mp.MesecPocetok, mp.MesecKraj, mp.MesecBroj
                    ),
                    MesecnaPremijaPrestmetka AS (
                        SELECT 
                            MesecPocetok,
                            MesecKraj,
                            MesecBroj,
                            AktivniOsigureniciBroj,
                            AktivniOsigureniciBroj * @MesecnaPremijaZaEdnoLice AS MesecnaPremija
                        FROM MesecniOsigureniciBroj
                    )
                    SELECT 
                        MesecBroj,
                        MesecPocetok,
                        MesecKraj,
                        AktivniOsigureniciBroj,
                        @VkupnaPremijaZaEdnoLice AS VkupnaPremijaZaEdnoLice,
                        @MesecnaPremijaZaEdnoLice AS MesecnaPremijaZaEdnoLice,
                        @VkupnoMesecinaPolisa AS VkupnoMesecinaPolisa,
                        MesecnaPremija,
                        ROUND(SUM(MesecnaPremija) OVER(), 0) AS VkupnaPremijaPolisa
                    FROM MesecnaPremijaPrestmetka
                    ORDER BY MesecBroj";

                using (SqlCommand cmd = new SqlCommand(monthlyQuery, connection))
                {
                    cmd.Parameters.AddWithValue("@PolisaId", PolisaId);
                    cmd.Parameters.AddWithValue("@DatumPolisaVaziOd", datumPolisaVaziOd.Value);
                    cmd.Parameters.AddWithValue("@DatumPolisaVaziDo", datumPolisaVaziDo.Value);
                    cmd.Parameters.AddWithValue("@VkupnaPremijaZaEdnoLice", premijaZaEdnoLice);
                    cmd.Parameters.AddWithValue("@MesecnaPremijaZaEdnoLice", monthlyPremijaZaEdnoLice);
                    cmd.Parameters.AddWithValue("@VkupnoMesecinaPolisa", totalPolicyMonths);

                    using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            MesecnaPrestmetka.Add(new MonthlyCalculationModel
                            {
                                MesecBroj = reader.GetInt32(reader.GetOrdinal("MesecBroj")),
                                MesecPocetok = reader.GetDateTime(reader.GetOrdinal("MesecPocetok")),
                                MesecKraj = reader.GetDateTime(reader.GetOrdinal("MesecKraj")),
                                AktivniOsigureniciBroj = reader.GetInt32(reader.GetOrdinal("AktivniOsigureniciBroj")),
                                VkupnaPremijaZaEdnoLice = reader.GetDecimal(reader.GetOrdinal("VkupnaPremijaZaEdnoLice")),
                                MesecnaPremijaZaEdnoLice = reader.GetDecimal(reader.GetOrdinal("MesecnaPremijaZaEdnoLice")),
                                VkupnoMesecinaPolisa = reader.GetInt32(reader.GetOrdinal("VkupnoMesecinaPolisa")),
                                MesecnaPremija = reader.GetDecimal(reader.GetOrdinal("MesecnaPremija")),
                                VkupnaPremijaPolisa = reader.GetDecimal(reader.GetOrdinal("VkupnaPremijaPolisa"))
                            });
                        }
                    }
                }

                // Execute the summary query
                string summaryQuery = @"
                    WITH MesecniPeriodi AS (
                        SELECT 
                            @DatumPolisaVaziOd AS MesecPocetok,
                            DATEADD(DAY, -1, DATEADD(MONTH, 1, @DatumPolisaVaziOd)) AS MesecKraj,
                            1 AS MesecBroj
                        
                        UNION ALL
                        
                        SELECT 
                            DATEADD(MONTH, 1, MesecPocetok) AS MesecPocetok,
                            DATEADD(DAY, -1, DATEADD(MONTH, 1, DATEADD(MONTH, 1, MesecPocetok))) AS MesecKraj,
                            MesecBroj + 1
                        FROM MesecniPeriodi
                        WHERE DATEADD(MONTH, 1, MesecPocetok) < @DatumPolisaVaziDo
                    ),
                    MesecniOsigureniciBroj AS (
                        SELECT 
                            mp.MesecPocetok,
                            mp.MesecKraj,
                            mp.MesecBroj,
                            COUNT(pok.Id) AS AktivniOsigureniciBroj
                        FROM MesecniPeriodi mp
                        LEFT JOIN PolisiOsigureniciKolektivno pok 
                            ON pok.PolisaId = @PolisaId
                            AND pok.DatumOsiguruvanjeVaziOd <= mp.MesecKraj
                            AND pok.DatumOsiguruvanjeVaziDo >= mp.MesecPocetok
                        GROUP BY mp.MesecPocetok, mp.MesecKraj, mp.MesecBroj
                    ),
                    MesecnaPremijaPrestmetka AS (
                        SELECT 
                            MesecPocetok,
                            MesecKraj,
                            MesecBroj,
                            AktivniOsigureniciBroj,
                            AktivniOsigureniciBroj * @MesecnaPremijaZaEdnoLice AS MesecnaPremija
                        FROM MesecniOsigureniciBroj
                    )
                    SELECT 
                        @VkupnoMesecinaPolisa AS VkupnoMesecinaPolisa,
                        @VkupnaPremijaZaEdnoLice AS VkupnaPremijaZaEdnoLice,
                        @MesecnaPremijaZaEdnoLice AS MesecnaPremijaZaEdnoLice,
                        COUNT(*) AS VkupnoMesecina,
                        SUM(AktivniOsigureniciBroj) AS VkupnoOsigureniciMesecina,
                        ROUND(SUM(MesecnaPremija), 0) AS VkupnaPremijaPolisa,
                        AVG(CAST(AktivniOsigureniciBroj AS DECIMAL(10,2))) AS ProsekOsigureniciPoMesec
                    FROM MesecnaPremijaPrestmetka";

                using (SqlCommand cmd = new SqlCommand(summaryQuery, connection))
                {
                    cmd.Parameters.AddWithValue("@PolisaId", PolisaId);
                    cmd.Parameters.AddWithValue("@DatumPolisaVaziOd", datumPolisaVaziOd.Value);
                    cmd.Parameters.AddWithValue("@DatumPolisaVaziDo", datumPolisaVaziDo.Value);
                    cmd.Parameters.AddWithValue("@VkupnaPremijaZaEdnoLice", premijaZaEdnoLice);
                    cmd.Parameters.AddWithValue("@MesecnaPremijaZaEdnoLice", monthlyPremijaZaEdnoLice);
                    cmd.Parameters.AddWithValue("@VkupnoMesecinaPolisa", totalPolicyMonths);

                    using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                            MesecnaPrestmetkaSumarno = new MonthlySummaryModel
                            {
                                VkupnoMesecinaPolisa = reader.GetInt32(reader.GetOrdinal("VkupnoMesecinaPolisa")),
                                VkupnaPremijaZaEdnoLice = reader.GetDecimal(reader.GetOrdinal("VkupnaPremijaZaEdnoLice")),
                                MesecnaPremijaZaEdnoLice = reader.GetDecimal(reader.GetOrdinal("MesecnaPremijaZaEdnoLice")),
                                VkupnoMesecina = reader.GetInt32(reader.GetOrdinal("VkupnoMesecina")),
                                VkupnoOsigureniciMesecina = reader.GetInt32(reader.GetOrdinal("VkupnoOsigureniciMesecina")),
                                VkupnaPremijaPolisa = reader.GetDecimal(reader.GetOrdinal("VkupnaPremijaPolisa")),
                                ProsekOsigureniciPoMesec = reader.GetDecimal(reader.GetOrdinal("ProsekOsigureniciPoMesec"))
                            };
                        }
                    }
                }

                // Get the value from the function
                decimal vkupnaPremijaZaPlakanje = 0;
                using (SqlCommand cmd = new SqlCommand("SELECT dbo.VratiPolisaVkupnaPremijaZaPlakanjePoPolisaId(@PolisaId)", connection))
                {
                    cmd.Parameters.AddWithValue("@PolisaId", PolisaId);
                    var result = await cmd.ExecuteScalarAsync();
                    if (result != null && result != DBNull.Value)
                    {
                        vkupnaPremijaZaPlakanje = Convert.ToDecimal(result);
                    }
                }

                // Calculate the difference
                if (MesecnaPrestmetkaSumarno != null)
                {
                    MesecnaPrestmetkaSumarno.Razlika = vkupnaPremijaZaPlakanje - MesecnaPrestmetkaSumarno.VkupnaPremijaPolisa;
                }
            }
        }

        public IActionResult OnGetDownloadTemplate()
        {
            // Define the path to the Excel template
            var filePath = Path.Combine(Directory.GetCurrentDirectory(), 
                "Pages", "Polisi", "Templates", "ListaOsigureniciKolektivnoTemplate.xlsx");
            
            // Return 404 if file doesn't exist
            if (!System.IO.File.Exists(filePath))
            {
                return NotFound("Template file not found");
            }

            // Read and return the file
            var fileBytes = System.IO.File.ReadAllBytes(filePath);
            return File(fileBytes, 
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", 
                "ListaOsigureniciKolektivnoTemplate.xlsx");
        }
    }
} 