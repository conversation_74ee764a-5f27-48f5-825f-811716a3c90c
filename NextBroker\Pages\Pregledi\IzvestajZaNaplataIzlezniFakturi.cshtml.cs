using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Data.SqlClient;
using RazorPortal.Services;
using System.ComponentModel.DataAnnotations;
using System.Data;
using OfficeOpenXml;
using OfficeOpenXml.Style;

namespace NextBroker.Pages.Pregledi
{
    public class IzvestajZaNaplataIzlezniFakturiModel : SecurePageModel
    {
        public IzvestajZaNaplataIzlezniFakturiModel(IConfiguration configuration)
            : base(configuration)
        {
        }

        [BindProperty]
        public FilterModel Filter { get; set; } = new FilterModel();

        public List<IzvestajResult> Results { get; set; } = new List<IzvestajResult>();
        public bool HasData { get; set; } = false;

        public class FilterModel
        {
            [Display(Name = "Датум од")]
            [DataType(DataType.Date)]
            public DateTime DatumOd { get; set; } = new DateTime(DateTime.Now.Year, 1, 1);

            [Display(Name = "Датум до")]
            [DataType(DataType.Date)]
            public DateTime DatumDo { get; set; } = DateTime.Now.Date;
        }

        public class IzvestajResult
        {
            public DateTime? DatumNaFaktura { get; set; }
            public string BrojNaFaktura { get; set; } = string.Empty;
            public string BrojNaPolisa { get; set; } = string.Empty;
            public DateTime? RokNaPlakanje { get; set; }
            public string Dogovoruvac { get; set; } = string.Empty;
            public string TelefonDogovoruvac { get; set; } = string.Empty;
            public string MaticenBrojDogovoruvac { get; set; } = string.Empty;
            public string AdresaDogovoruvac { get; set; } = string.Empty;
            public string Osigurenik { get; set; } = string.Empty;
            public string TelefonOsigurenik { get; set; } = string.Empty;
            public string MaticenBrojOsigurenik { get; set; } = string.Empty;
            public string AdresaOsigurenik { get; set; } = string.Empty;
            public string Osiguritel { get; set; } = string.Empty;
            public string Klasa { get; set; } = string.Empty;
            public string Produkt { get; set; } = string.Empty;
            public string VrabotenSorabotnik { get; set; } = string.Empty;
            public string MaticenBrojSorabotnik { get; set; } = string.Empty;
            public decimal? IznosPremijaZaNaplata { get; set; }
            public decimal? IznosPremijaZaNaplataVoRok { get; set; }
            public string PlatenoVoRok { get; set; } = string.Empty;
            public decimal? VkupniUplatiOdDogovoruvac { get; set; }
            public decimal? DolgPoDospeanRati { get; set; }
            public decimal? VkupenDolg { get; set; }
        }

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("IzvestajZaNaplataIzlezniFakturi"))
            {
                return RedirectToAccessDenied();
            }

            return Page();
        }

        public async Task<IActionResult> OnPost()
        {
            if (!await HasPageAccess("IzvestajZaNaplataIzlezniFakturi"))
            {
                return RedirectToAccessDenied();
            }

            if (!ModelState.IsValid)
            {
                return Page();
            }

            await LoadData();
            return Page();
        }

        public async Task<IActionResult> OnPostExportToExcel()
        {
            if (!await HasPageAccess("IzvestajZaNaplataIzlezniFakturi"))
            {
                return RedirectToAccessDenied();
            }

            await LoadData();

            // Set EPPlus license context
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("Извештај за наплата излезни фактури");

            // Add headers
            var headers = new[]
            {
                "Датум на фактура", "Број на фактура", "Број на полиса", "Рок на плаќање",
                "Договорувач", "Телефон договорувач", "Матичен број договорувач", "Адреса договорувач",
                "Осигуреник", "Телефон осигуреник", "Матичен број осигуреник", "Адреса осигуреник",
                "Осигурител", "Класа", "Продукт", "Вработен соработник", "Матичен број на соработник",
                "Износ премија за наплата", "Износ премија за наплата во рок", "Платено во рок",
                "Вкупни уплати од договорувач", "Долг по доспеани рати", "Вкупен долг"
            };

            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cells[1, i + 1].Value = headers[i];
                worksheet.Cells[1, i + 1].Style.Font.Bold = true;
                worksheet.Cells[1, i + 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
                worksheet.Cells[1, i + 1].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
            }

            // Add data
            for (int i = 0; i < Results.Count; i++)
            {
                var result = Results[i];
                var row = i + 2;

                worksheet.Cells[row, 1].Value = result.DatumNaFaktura?.ToString("dd.MM.yyyy") ?? "";
                worksheet.Cells[row, 2].Value = result.BrojNaFaktura;
                worksheet.Cells[row, 3].Value = result.BrojNaPolisa;
                worksheet.Cells[row, 4].Value = result.RokNaPlakanje?.ToString("dd.MM.yyyy") ?? "";
                worksheet.Cells[row, 5].Value = result.Dogovoruvac;
                worksheet.Cells[row, 6].Value = result.TelefonDogovoruvac;
                worksheet.Cells[row, 7].Value = result.MaticenBrojDogovoruvac;
                worksheet.Cells[row, 8].Value = result.AdresaDogovoruvac;
                worksheet.Cells[row, 9].Value = result.Osigurenik;
                worksheet.Cells[row, 10].Value = result.TelefonOsigurenik;
                worksheet.Cells[row, 11].Value = result.MaticenBrojOsigurenik;
                worksheet.Cells[row, 12].Value = result.AdresaOsigurenik;
                worksheet.Cells[row, 13].Value = result.Osiguritel;
                worksheet.Cells[row, 14].Value = result.Klasa;
                worksheet.Cells[row, 15].Value = result.Produkt;
                worksheet.Cells[row, 16].Value = result.VrabotenSorabotnik;
                worksheet.Cells[row, 17].Value = result.MaticenBrojSorabotnik;
                worksheet.Cells[row, 18].Value = result.IznosPremijaZaNaplata ?? 0;
                worksheet.Cells[row, 19].Value = result.IznosPremijaZaNaplataVoRok ?? 0;
                worksheet.Cells[row, 20].Value = result.PlatenoVoRok;
                worksheet.Cells[row, 21].Value = result.VkupniUplatiOdDogovoruvac ?? 0;
                worksheet.Cells[row, 22].Value = result.DolgPoDospeanRati ?? 0;
                worksheet.Cells[row, 23].Value = result.VkupenDolg ?? 0;
            }

            // Auto-fit columns
            worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();

            // Add borders
            using (var range = worksheet.Cells[1, 1, Results.Count + 1, headers.Length])
            {
                range.Style.Border.Top.Style = ExcelBorderStyle.Thin;
                range.Style.Border.Left.Style = ExcelBorderStyle.Thin;
                range.Style.Border.Right.Style = ExcelBorderStyle.Thin;
                range.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
            }

            var fileName = $"Izvestaj_Za_Naplata_Izlezni_Fakturi_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
            return File(package.GetAsByteArray(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
        }

        private async Task LoadData()
        {
            Results.Clear();
            string connectionString = _configuration.GetConnectionString("DefaultConnection");

            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                string query = @"
                    SELECT
                        pifkk.DatumNaFaktura as [DatumNaFaktura],
                        pifkk.BrojNaFaktura as [BrojNaFaktura],
                        pifkk.BrojNaPolisa as [BrojNaPolisa],
                        pifkk.RokNaPlakanjeFakturaIzlezna as [RokNaPlakanje],
                        -- Concatenate Dogovoruvac fields with NULL handling
                        ISNULL(CAST(pifkk.dogovoruvacime AS VARCHAR(100)), '') + ' ' +
                        ISNULL(CAST(pifkk.dogovoruvacprezime AS VARCHAR(100)), '') + ' ' +
                        ISNULL(CAST(pifkk.dogovoruvacnaziv AS VARCHAR(100)), '')  as [Dogovoruvac],

                        dog.Tel as [TelefonDogovoruvac],
                        Coalesce(dog.MB, '') + Coalesce(dog.EMBG,'') as [MaticenBrojDogovoruvac],
                        dbo.VratiAdresaDogovoruvacPoPolisaId(p.id) as [AdresaDogovoruvac],

                        -- Concatenate Osigurenik fields with NULL handling
                        ISNULL(CAST(pifkk.osigurenikime AS VARCHAR(100)), '') + ' ' +
                        ISNULL(CAST(pifkk.osigurenikprezime AS VARCHAR(100)), '') + ' ' +
                        ISNULL(CAST(pifkk.osigureniknaziv AS VARCHAR(100)), '')  as [Osigurenik],

                        osig.Tel as [TelefonOsigurenik],
                        Coalesce(osig.MB, '') + Coalesce(osig.EMBG,'') as [MaticenBrojOsigurenik],
                        dbo.VratiAdresaOsigurenikPoPolisaId(p.id) as [AdresaOsigurenik],

                        pifkk.Osiguritel  as [Osiguritel],
                        klas.KlasaIme as [Klasa],
                        pifkk.Produkt as [Produkt],

                        case
                        when p.KlientiIdSorabotnik is not null then coalesce(sorab.Ime, '') + ' ' + coalesce(sorab.Prezime, '')
                        else dbo.VratiImePrezimePoUsername(p.UsernameCreated)
                        end as [VrabotenSorabotnik],

                        case
                        when p.KlientiIdSorabotnik is not null then coalesce(sorab.EMBG, '') + ' ' + coalesce(sorab.MB, '')
                        else (select EMB from users where Username = p.UsernameCreated)
                        end as [MaticenBrojSorabotnik],

                        pifkk.VkupnaPremija as [IznosPremijaZaNaplata],
                        pifkk.PremijaZaNaplata as [IznosPremijaZaNaplataVoRok],

                        case
                        when    dbo.VratiPolisaPosledenDatumNaUplata(p.Id) <= pifkk.RokNaPlakanjeFakturaIzlezna then 'Да'
                        else 'Не'
                        end as [PlatenoVoRok],
                        dbo.VratiPolisaUplatenIznos(p.Id) as [VkupniUplatiOdDogovoruvac],
                        dbo.VratiPolisaDospeanDolg(p.Id) as [DolgPoDospeanRati],

                        case
                        when pifkk.VkupnaPremija = pifkk.PremijaZaNaplata then pifkk.VkupnaPremija - dbo.VratiPolisaUplatenIznos(p.Id)
                        else pifkk.PremijaZaNaplata - dbo.VratiPolisaUplatenIznos(p.Id)
                        end as [VkupenDolg]

                    FROM dbo.PolisiIzlezniFakturiKonKlient pifkk
                    left join polisi p on p.BrojNaFakturaIzlezna = pifkk.BrojNaFaktura
                    left join klienti dog on p.KlientiIdDogovoruvac = dog.id
                    left join klienti osig on p.KlientiIdOsigurenik = osig.id
                    left join KlasiOsiguruvanje klas on klas.id = p.KlasiOsiguruvanjeIdKlasa
                    left join Klienti sorab on sorab.id = p.KlientiIdSorabotnik
                    WHERE pifkk.DatumNaFaktura between @DatumOd and @DatumDo
                    AND p.storno !=1";

                using (SqlCommand cmd = new SqlCommand(query, connection))
                {
                    cmd.Parameters.AddWithValue("@DatumOd", Filter.DatumOd);
                    cmd.Parameters.AddWithValue("@DatumDo", Filter.DatumDo);

                    using (SqlDataReader reader = await cmd.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            Results.Add(new IzvestajResult
                            {
                                DatumNaFaktura = reader["DatumNaFaktura"] as DateTime?,
                                BrojNaFaktura = reader["BrojNaFaktura"]?.ToString() ?? "",
                                BrojNaPolisa = reader["BrojNaPolisa"]?.ToString() ?? "",
                                RokNaPlakanje = reader["RokNaPlakanje"] as DateTime?,
                                Dogovoruvac = reader["Dogovoruvac"]?.ToString()?.Trim() ?? "",
                                TelefonDogovoruvac = reader["TelefonDogovoruvac"]?.ToString() ?? "",
                                MaticenBrojDogovoruvac = reader["MaticenBrojDogovoruvac"]?.ToString() ?? "",
                                AdresaDogovoruvac = reader["AdresaDogovoruvac"]?.ToString() ?? "",
                                Osigurenik = reader["Osigurenik"]?.ToString()?.Trim() ?? "",
                                TelefonOsigurenik = reader["TelefonOsigurenik"]?.ToString() ?? "",
                                MaticenBrojOsigurenik = reader["MaticenBrojOsigurenik"]?.ToString() ?? "",
                                AdresaOsigurenik = reader["AdresaOsigurenik"]?.ToString() ?? "",
                                Osiguritel = reader["Osiguritel"]?.ToString() ?? "",
                                Klasa = reader["Klasa"]?.ToString() ?? "",
                                Produkt = reader["Produkt"]?.ToString() ?? "",
                                VrabotenSorabotnik = reader["VrabotenSorabotnik"]?.ToString() ?? "",
                                MaticenBrojSorabotnik = reader["MaticenBrojSorabotnik"]?.ToString() ?? "",
                                IznosPremijaZaNaplata = reader["IznosPremijaZaNaplata"] as decimal?,
                                IznosPremijaZaNaplataVoRok = reader["IznosPremijaZaNaplataVoRok"] as decimal?,
                                PlatenoVoRok = reader["PlatenoVoRok"]?.ToString() ?? "",
                                VkupniUplatiOdDogovoruvac = reader["VkupniUplatiOdDogovoruvac"] as decimal?,
                                DolgPoDospeanRati = reader["DolgPoDospeanRati"] as decimal?,
                                VkupenDolg = reader["VkupenDolg"] as decimal?
                            });
                        }
                    }
                }
            }

            HasData = Results.Count > 0;
        }
    }
}