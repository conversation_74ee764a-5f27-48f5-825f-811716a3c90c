@page
@model NextBroker.Pages.Pregledi.IzvestajPolisiNivoNaVrabotenSorabotnikModel
@{
    ViewData["Title"] = "Извештај полиси ниво на вработен соработник";
    Layout = "_Layout";
}

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-user-chart"></i> @ViewData["Title"]
                    </h4>
                </div>
                <div class="card-body">
                    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
                    {
                        <div class="alert alert-danger" role="alert">
                            <i class="fas fa-exclamation-triangle"></i> @Model.ErrorMessage
                        </div>
                    }

                    <form method="post">
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label asp-for="Input.DatumNaIzdavanjeOd" class="form-label">@Html.DisplayNameFor(m => m.Input.DatumNaIzdavanjeOd)</label>
                                <input asp-for="Input.DatumNaIzdavanjeOd" class="form-control" type="date" />
                                <span asp-validation-for="Input.DatumNaIzdavanjeOd" class="text-danger"></span>
                            </div>
                            <div class="col-md-3">
                                <label asp-for="Input.DatumNaIzdavanjeDo" class="form-label">@Html.DisplayNameFor(m => m.Input.DatumNaIzdavanjeDo)</label>
                                <input asp-for="Input.DatumNaIzdavanjeDo" class="form-control" type="date" />
                                <span asp-validation-for="Input.DatumNaIzdavanjeDo" class="text-danger"></span>
                            </div>
                            <div class="col-md-3">
                                <label asp-for="Input.VrabotenSorabotnik" class="form-label">@Html.DisplayNameFor(m => m.Input.VrabotenSorabotnik)</label>
                                <select asp-for="Input.VrabotenSorabotnik" asp-items="Model.VraboteniSorabotnici" class="form-select">
                                </select>
                                <span asp-validation-for="Input.VrabotenSorabotnik" class="text-danger"></span>
                            </div>
                            <div class="col-md-3">
                                <label asp-for="Input.KlientiIdOsiguritel" class="form-label">@Html.DisplayNameFor(m => m.Input.KlientiIdOsiguritel)</label>
                                <select asp-for="Input.KlientiIdOsiguritel" class="form-control">
                                    <option value="">-- Сите осигурители --</option>
                                    @foreach (var osig in Model.OsiguriteliOptions)
                                    {
                                        <option value="@osig.Id">@osig.Naziv</option>
                                    }
                                </select>
                                <span asp-validation-for="Input.KlientiIdOsiguritel" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label asp-for="Input.KlasiOsiguruvanjeIdKlasa" class="form-label">@Html.DisplayNameFor(m => m.Input.KlasiOsiguruvanjeIdKlasa)</label>
                                <select asp-for="Input.KlasiOsiguruvanjeIdKlasa" class="form-control">
                                    <option value="">-- Сите класи --</option>
                                    @foreach (var klasa in Model.KlasiOsiguruvanjeOptions)
                                    {
                                        <option value="@klasa.Id">@klasa.KlasaIme</option>
                                    }
                                </select>
                                <span asp-validation-for="Input.KlasiOsiguruvanjeIdKlasa" class="text-danger"></span>
                            </div>
                            <div class="col-md-3">
                                <label asp-for="Input.ProduktiIdProizvod" class="form-label">@Html.DisplayNameFor(m => m.Input.ProduktiIdProizvod)</label>
                                <select asp-for="Input.ProduktiIdProizvod" class="form-control">
                                    <option value="">-- Сите продукти --</option>
                                    @foreach (var produkt in Model.ProduktiOptions)
                                    {
                                        <option value="@produkt.Id">@produkt.Ime</option>
                                    }
                                </select>
                                <span asp-validation-for="Input.ProduktiIdProizvod" class="text-danger"></span>
                            </div>
                            <div class="col-md-3">
                                <label asp-for="Input.EkspozituraIme" class="form-label">@Html.DisplayNameFor(m => m.Input.EkspozituraIme)</label>
                                <select asp-for="Input.EkspozituraIme" class="form-control">
                                    <option value="">-- Сите експозитури --</option>
                                    @foreach (var ekspozitura in Model.EkspozituriOptions)
                                    {
                                        <option value="@ekspozitura.Ime">@ekspozitura.Ime</option>
                                    }
                                </select>
                                <span asp-validation-for="Input.EkspozituraIme" class="text-danger"></span>
                            </div>
                            <div class="col-md-3">
                                <label asp-for="Input.Storno" class="form-label">@Html.DisplayNameFor(m => m.Input.Storno)</label>
                                <select asp-for="Input.Storno" class="form-control">
                                    <option value="">Сите</option>
                                    <option value="Да">Да</option>
                                    <option value="Не">Не</option>
                                </select>
                                <span asp-validation-for="Input.Storno" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-12 d-flex justify-content-start">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search"></i> Прикажи извештај
                                </button>
                                @if (Model.IsDataLoaded && Model.Results.Any())
                                {
                                    <button type="submit" asp-page-handler="ExportExcel" class="btn btn-success">
                                        <i class="fas fa-file-excel"></i> Извези во Excel
                                    </button>
                                }
                            </div>
                        </div>
                    </form>

                    @if (Model.IsDataLoaded)
                    {
                        @if (Model.Results.Any())
                        {
                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle"></i>
                                        Пронајдени се <strong>@Model.Results.Count</strong> записи за <strong>@Model.Input.VrabotenSorabotnik</strong> во периодот од
                                        <strong>@Model.Input.DatumNaIzdavanjeOd?.ToString("dd.MM.yyyy")</strong> до
                                        <strong>@Model.Input.DatumNaIzdavanjeDo?.ToString("dd.MM.yyyy")</strong>
                                    </div>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <input type="text" id="searchInput" class="form-control" placeholder="Пребарај во табелата..." />
                                </div>
                                <div class="col-md-6 text-end">
                                    <button id="clearSearch" class="btn btn-outline-secondary">
                                        <i class="fas fa-times"></i> Исчисти пребарување
                                    </button>
                                </div>
                            </div>

                            <div class="table-responsive">
                                <table id="reportTable" class="table table-striped table-hover table-sm">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>ID</th>
                                            <th>Број на полиса</th>
                                            <th>Важи од</th>
                                            <th>Важи до</th>
                                            <th>Осигурител</th>
                                            <th>Класа</th>
                                            <th>Продукт</th>
                                            <th>Договорувач</th>
                                            <th>Адреса договорувач</th>
                                            <th>Телефон договорувач</th>
                                            <th>Осигуреник</th>
                                            <th>Адреса осигуреник</th>
                                            <th>Телефон осигуреник</th>
                                            <th>Начин на плаќање</th>
                                            <th>Премија полисирана</th>
                                            <th>Премија наплатена</th>
                                            <th>Долг премија</th>
                                            <th>Долг по денови</th>
                                            <th>Тип клиент</th>
                                            <th>Експозитура</th>
                                            <th>Креирано од</th>
                                            <th>Тип соработник</th>
                                            <th>Име соработник</th>
                                            <th>Име надреден</th>
                                            <th>Провизија наплатено</th>
                                            <th>Пренесена премија</th>
                                            <th>Сторно</th>
                                            <th>Забелешка</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var item in Model.Results)
                                        {
                                            <tr>
                                                <td>@item.Id</td>
                                                <td>@item.BrojNaPolisa</td>
                                                <td>@item.DatumVaziOd</td>
                                                <td>@item.DatumVaziDo</td>
                                                <td>@item.Osiguritel</td>
                                                <td>@item.Klasa</td>
                                                <td>@item.Produkt</td>
                                                <td>@item.Dogovoruvac</td>
                                                <td>@item.DogovoruvacAdresa</td>
                                                <td>@item.DogovoruvacTelefon</td>
                                                <td>@item.Osigurenik</td>
                                                <td>@item.OsigurenikAdresa</td>
                                                <td>@item.OsigurenikTelefon</td>
                                                <td>@item.NacinNaPlakanje</td>
                                                <td class="text-end">@(item.PremijaPolisirana?.ToString("N2") ?? "")</td>
                                                <td class="text-end">@(item.PremijaNaplatenaOdDogovoruvac?.ToString("N2") ?? "")</td>
                                                <td class="text-end">@(item.DolgPremijaOdDogovoruvac?.ToString("N2") ?? "")</td>
                                                <td>@item.DolgPoDenovi</td>
                                                <td>@item.KlientFizickoPravnoLice</td>
                                                <td>@item.Ekspozitura</td>
                                                <td>@item.KreiranoOd</td>
                                                <td>@item.TipNaSorabotnik</td>
                                                <td>@item.ImePrezimeSorabotnik</td>
                                                <td>@item.ImePrezimeNadreden</td>
                                                <td class="text-end">@(item.ProvizijaNaplateno?.ToString("N2") ?? "")</td>
                                                <td class="text-end">@(item.PrenesenaPremijaKonOsiguritel?.ToString("N2") ?? "")</td>
                                                <td>
                                                    @if (item.Storno == "Да")
                                                    {
                                                        <span class="badge bg-danger">@item.Storno</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="badge bg-success">@item.Storno</span>
                                                    }
                                                </td>
                                                <td>@item.Zabeleska</td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        }
                        else
                        {
                            <div class="alert alert-warning" role="alert">
                                <i class="fas fa-exclamation-triangle"></i>
                                Нема пронајдени записи за корисникот <strong>@Model.Input.VrabotenSorabotnik</strong> во избраниот период.
                            </div>
                        }
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Search functionality
            $('#searchInput').on('keyup', function() {
                var value = $(this).val().toLowerCase();
                $('#reportTable tbody tr').filter(function() {
                    $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
                });
                updateRowCount();
            });

            // Clear search
            $('#clearSearch').on('click', function() {
                $('#searchInput').val('');
                $('#reportTable tbody tr').show();
                updateRowCount();
            });

            function updateRowCount() {
                var visibleRows = $('#reportTable tbody tr:visible').length;
                var totalRows = $('#reportTable tbody tr').length;

                if (visibleRows !== totalRows) {
                    $('.alert-info').html('<i class="fas fa-info-circle"></i> Прикажани се <strong>' + visibleRows + '</strong> од вкупно <strong>' + totalRows + '</strong> записи за <strong>@Model.Input.VrabotenSorabotnik</strong>');
                } else {
                    $('.alert-info').html('<i class="fas fa-info-circle"></i> Пронајдени се <strong>' + totalRows + '</strong> записи за <strong>@Model.Input.VrabotenSorabotnik</strong> во периодот од <strong>@Model.Input.DatumNaIzdavanjeOd?.ToString("dd.MM.yyyy")</strong> до <strong>@Model.Input.DatumNaIzdavanjeDo?.ToString("dd.MM.yyyy")</strong>');
                }
            }

            // Initialize table styling
            $('#reportTable').addClass('table-striped table-hover');

            // Add hover effect for better UX
            $('#reportTable tbody tr').hover(
                function() {
                    $(this).addClass('table-active');
                },
                function() {
                    $(this).removeClass('table-active');
                }
            );

            // Add dropdown change event
            $('#Input_VrabotenSorabotnik').on('change', function() {
                var selectedValue = $(this).val();
                if (selectedValue) {
                    $(this).removeClass('is-invalid').addClass('is-valid');
                } else {
                    $(this).removeClass('is-valid').addClass('is-invalid');
                }
            });

            // Initialize dropdown styling
            $('#Input_VrabotenSorabotnik').addClass('form-select');
        });
    </script>

    <style>
        .table-responsive {
            max-height: 600px;
            overflow-y: auto;
        }

        .table thead th {
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .table td, .table th {
            white-space: nowrap;
            font-size: 0.875rem;
        }

        .table td:last-child, .table th:last-child {
            white-space: normal;
            max-width: 200px;
            word-wrap: break-word;
        }

        .btn-group-sm > .btn, .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }

        .alert {
            margin-bottom: 1rem;
        }

        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .table-active {
            background-color: rgba(0, 123, 255, 0.1) !important;
        }

        /* Enhanced styling for the employee dropdown field */
        #Input_VrabotenSorabotnik {
            border-left: 4px solid #007bff;
            background-color: #f8f9fa;
        }

        #Input_VrabotenSorabotnik:focus {
            border-left: 4px solid #0056b3;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
            background-color: #ffffff;
        }

        #Input_VrabotenSorabotnik.is-valid {
            border-left: 4px solid #28a745;
        }

        #Input_VrabotenSorabotnik.is-invalid {
            border-left: 4px solid #dc3545;
        }

        /* Highlight the employee parameter */
        .col-md-3:nth-child(3) {
            background-color: rgba(0, 123, 255, 0.05);
            border-radius: 8px;
            padding: 15px;
            margin: 0 5px;
        }
    </style>
}