using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Runtime.Serialization;

namespace NextBroker.Pages.Polisi
{
    public class DodajPolisaKlasa21Model : SecurePageModel
    {
        private readonly IConfiguration _configuration;

        public enum TipNaFakturaEnum
        {
            [Display(Name = "Влезна фактура кон клиент")]
            [EnumMember(Value = "Влезна фактура кон клиент")]
            VleznaFakturaKonKlient,
            [Display(Name = "Влезна фактура кон брокер")]
            [EnumMember(Value = "Влезна фактура кон брокер")]
            VleznaFakturaKonBroker
        }

        public DodajPolisaKlasa21Model(IConfiguration configuration)
            : base(configuration)
        {
            _configuration = configuration;
        }

        [BindProperty]
        public PolisaKlasa21InputModel Input { get; set; } = new();

        // SelectList properties for dropdowns
        public IEnumerable<SelectListItem> Osiguriteli { get; set; }
        public IEnumerable<SelectListItem> KlasiOsiguruvanje { get; set; }
        public IEnumerable<SelectListItem> Produkti { get; set; }
        public IEnumerable<SelectListItem> Valuti { get; set; }
        public IEnumerable<SelectListItem> NaciniNaPlakanje { get; set; }
        public IEnumerable<SelectListItem> TipoviNaFaktura { get; set; }
        public IEnumerable<SelectListItem> InvesticiskiFondoviSelectList { get; set; }

        // Lists for management
        public List<InvesticiskoDrushtvo> InvesticiskaDrushtva { get; set; } = new List<InvesticiskoDrushtvo>();
        public List<InvesticiskiFond> InvesticiskiFondovi { get; set; } = new List<InvesticiskiFond>();

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("DodajPolisaKlasa21"))
            {
                return RedirectToAccessDenied();
            }

            await LoadOsiguriteli();
            await LoadKlasiOsiguruvanje();
            await LoadProdukti();
            await LoadValuti();
            await LoadNaciniNaPlakanje();
            LoadTipoviNaFaktura();
            await LoadInvesticiskiFondovi();
            await LoadInvesticiskaDrushtva();

            return Page();
        }

        public async Task<IActionResult> OnPost()
        {
            if (!await HasPageAccess("DodajPolisaKlasa21"))
            {
                return RedirectToAccessDenied();
            }

            if (!ModelState.IsValid)
            {
                await LoadOsiguriteli();
                await LoadKlasiOsiguruvanje();
                await LoadProdukti();
                await LoadValuti();
                await LoadNaciniNaPlakanje();
                LoadTipoviNaFaktura();
                await LoadInvesticiskiFondovi();
                await LoadInvesticiskaDrushtva();
                return Page();
            }

            // Check for duplicate policy number
            if (!string.IsNullOrEmpty(Input.BrojNaPolisa))
            {
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    using (SqlCommand cmd = new SqlCommand(@"
                        SELECT COUNT(*)
                        FROM Polisi
                        WHERE BrojNaPolisa = @BrojNaPolisa
                        AND (Storno = 0 OR Storno IS NULL)", connection))
                    {
                        cmd.Parameters.AddWithValue("@BrojNaPolisa", Input.BrojNaPolisa);
                        int count = Convert.ToInt32(await cmd.ExecuteScalarAsync());
                        if (count > 0)
                        {
                            ModelState.AddModelError("", "Веќе постои полиса со овој број која не е сторнирана!");
                            await LoadOsiguriteli();
                            await LoadKlasiOsiguruvanje();
                            await LoadProdukti();
                            await LoadValuti();
                            await LoadNaciniNaPlakanje();
                            LoadTipoviNaFaktura();
                            await LoadInvesticiskiFondovi();
                            await LoadInvesticiskaDrushtva();
                            return Page();
                        }
                    }
                }
            }

            string username = HttpContext.Session.GetString("Username");
            if (string.IsNullOrEmpty(username))
            {
                TempData["ErrorMessage"] = "Вашата сесија е истечена. Најавете се повторно.";
                return RedirectToPage("/Account/Login");
            }

            try
            {
                string connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // First insert into Polisi table
                            long polisaId = await InsertPolisi(connection, transaction, username);

                            // Then insert into PolisiKlasa21 table and get its ID
                            long polisiKlasa21Id = await InsertPolisiKlasa21(connection, transaction, polisaId, username);

                            // Insert supplementary insurance entries if any
                            if (Input.Klasa1Entries?.Any() == true)
                            {
                                await InsertKlasa1Entries(connection, transaction, polisiKlasa21Id);
                            }

                            if (Input.Klasa2Entries?.Any() == true)
                            {
                                await InsertKlasa2Entries(connection, transaction, polisiKlasa21Id);
                            }

                            transaction.Commit();
                            TempData["SuccessMessage"] = "Полисата е успешно зачувана.";
                            return RedirectToPage("ListaPolisi");
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            ModelState.AddModelError("", "Настана грешка при зачувување на полисата. Ве молиме обидете се повторно.");
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "Настана грешка при зачувување на полисата.";
                await LoadOsiguriteli();
                await LoadKlasiOsiguruvanje();
                await LoadProdukti();
                await LoadValuti();
                await LoadNaciniNaPlakanje();
                LoadTipoviNaFaktura();
                await LoadInvesticiskiFondovi();
                await LoadInvesticiskaDrushtva();
                return Page();
            }
        }

        private async Task LoadOsiguriteli()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Naziv
                    FROM Klienti
                    WHERE Osiguritel = 1
                    AND ZivotNezivot = N'Живот'
                    AND DogovorVaziDo > Convert(date,GETDATE())
                    ORDER BY Naziv", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Naziv"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Osiguriteli = items;
                }
            }
        }

        private async Task LoadKlasiOsiguruvanje()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, KlasaIme
                    FROM KlasiOsiguruvanje
                    WHERE Id = 21
                    ORDER BY KlasaIme", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["KlasaIme"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    KlasiOsiguruvanje = items;
                }
            }
        }

        private async Task LoadProdukti()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, Ime
                    FROM Produkti
                    WHERE KlasaOsiguruvanjeId = 21
                    ORDER BY Ime", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["Ime"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    Produkti = items;
                }
            }
        }

        private async Task LoadValuti()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT id, CONCAT(Valuta, ' - ', ImeValuta) as DisplayName
                    FROM SifrarnikValuti
                    ORDER BY Valuta", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["DisplayName"].ToString(),
                            reader["id"].ToString()
                        ));
                    }
                    Valuti = items;
                }
            }
        }

        private async Task LoadNaciniNaPlakanje()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, NacinNaPlakanje
                    FROM SifrarnikNacinNaPlakanje
                    ORDER BY NacinNaPlakanje", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var items = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        items.Add(new SelectListItem(
                            reader["NacinNaPlakanje"].ToString(),
                            reader["Id"].ToString()
                        ));
                    }
                    NaciniNaPlakanje = items;
                }
            }
        }

        private void LoadTipoviNaFaktura()
        {
            var items = new List<SelectListItem>();
            foreach (TipNaFakturaEnum tip in Enum.GetValues<TipNaFakturaEnum>())
            {
                var displayAttribute = tip.GetType()
                    .GetField(tip.ToString())
                    ?.GetCustomAttributes(typeof(DisplayAttribute), false)
                    .FirstOrDefault() as DisplayAttribute;

                items.Add(new SelectListItem(
                    displayAttribute?.Name ?? tip.ToString(),
                    displayAttribute?.Name ?? tip.ToString()
                ));
            }
            TipoviNaFaktura = items;
        }

        private async Task LoadInvesticiskaDrushtva()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand command = new SqlCommand("SELECT Id, Naziv FROM ShifrarnikInvesticiskoDrushtvo ORDER BY Naziv", connection))
                {
                    using (SqlDataReader reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            InvesticiskaDrushtva.Add(new InvesticiskoDrushtvo
                            {
                                Id = reader.GetInt64(0),
                                Naziv = reader.GetString(1)
                            });
                        }
                    }
                }
            }
        }

        private async Task LoadInvesticiskiFondovi()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand command = new SqlCommand(@"
                    SELECT f.Id, f.Naziv, f.IdentifikaciskiBrojaNaInvesticiskiFond, d.Naziv as DrushtvoNaziv
                    FROM ShifrarnikInvesticiskiFond f
                    INNER JOIN ShifrarnikInvesticiskoDrushtvo d ON f.ShifrarnikInvesticiskoDrushtvoIdDrushtvo = d.Id
                    ORDER BY f.Naziv", connection))
                {
                    using (SqlDataReader reader = await command.ExecuteReaderAsync())
                    {
                        var items = new List<SelectListItem>();
                        while (await reader.ReadAsync())
                        {
                            InvesticiskiFondovi.Add(new InvesticiskiFond
                            {
                                Id = reader.GetInt64(0),
                                Naziv = reader.GetString(1),
                                IdentifikaciskiBrojaNaInvesticiskiFond = reader.GetString(2),
                                DrushtvoNaziv = reader.GetString(3)
                            });

                            items.Add(new SelectListItem(
                                reader.GetString(1),
                                reader.GetInt64(0).ToString()
                            ));
                        }
                        InvesticiskiFondoviSelectList = items;
                    }
                }
            }
        }

        private async Task SavePolisaKlasa21()
        {
            string username = HttpContext.Session.GetString("Username");
            if (string.IsNullOrEmpty(username))
            {
                throw new InvalidOperationException("User session expired");
            }

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlTransaction transaction = connection.BeginTransaction())
                {
                    try
                    {
                        // Insert into Polisi table first
                        long polisaId = await InsertPolisi(connection, transaction, username);

                        // Insert into PolisiKlasa21 table and get its ID
                        long polisiKlasa21Id = await InsertPolisiKlasa21(connection, transaction, polisaId, username);

                        // Insert supplementary insurance entries if any
                        if (Input.Klasa1Entries?.Any() == true)
                        {
                            await InsertKlasa1Entries(connection, transaction, polisiKlasa21Id);
                        }

                        if (Input.Klasa2Entries?.Any() == true)
                        {
                            await InsertKlasa2Entries(connection, transaction, polisiKlasa21Id);
                        }

                        await transaction.CommitAsync();
                    }
                    catch
                    {
                        await transaction.RollbackAsync();
                        throw;
                    }
                }
            }
        }

        private async Task<long> InsertPolisi(SqlConnection connection, SqlTransaction transaction, string username)
        {
            string insertPolisiSql = @"
                INSERT INTO Polisi (
                    DateCreated, UsernameCreated,
                    KlientiIdOsiguritel, KlasiOsiguruvanjeIdKlasa, ProduktiIdProizvod,
                    BrojNaPolisa, BrojNaPonuda, KlientiIdDogovoruvac, KlientiIdOsigurenik,
                    Kolektivna, KolektivnaNeodredenBrOsigurenici, NeodredenBrOsigureniciZabeleska,
                    DatumVaziOd, DatumVaziDo, DatumNaIzdavanje, SifrarnikValutiIdValuta, KlientiIdSorabotnik,
                    Faktoring, SifrarnikValutiIdFranshizaValuta,
                    ProcentFranshiza, FranshizaIznos, KoregiranaStapkaNaProvizija,
                    ProcentNaPopustZaFakturaVoRok, IznosZaPlakjanjeVoRok,
                    ProcentKomercijalenPopust, ProcentFinansiski, PremijaZaNaplata,
                    Uplateno, DolznaPremija, SifrarnikNacinNaPlakjanjeId, TipNaFaktura,
                    BrojNaFakturaVlezna, DatumNaFakturaVlezna, RokNaPlakjanjeFakturaVlezna,
                    SifrarnikTipNaPlakanjeId, SifrarnikBankiIdBanka,
                    GeneriranaFakturaIzlezna, BrojNaFakturaIzlezna,
                    DatumNaIzleznaFaktura, RokNaPlakjanjeFakturaIzlezna,
                    Storno, PricinaZaStorno, Zabeleska)
                VALUES (
                    GETDATE(), @UsernameCreated,
                    @KlientiIdOsiguritel, @KlasiOsiguruvanjeIdKlasa, @ProduktiIdProizvod,
                    @BrojNaPolisa, @BrojNaPonuda, @KlientiIdDogovoruvac, @KlientiIdOsigurenik,
                    @Kolektivna, @KolektivnaNeodredenBrOsigurenici, @NeodredenBrOsigureniciZabeleska,
                    @DatumVaziOd, @DatumVaziDo, @DatumNaIzdavanje, @SifrarnikValutiIdValuta, @KlientiIdSorabotnik,
                    @Faktoring, @SifrarnikValutiIdFranshizaValuta,
                    @ProcentFranshiza, @FranshizaIznos, @KoregiranaStapkaNaProvizija,
                    @ProcentNaPopustZaFakturaVoRok, @IznosZaPlakjanjeVoRok,
                    @ProcentKomercijalenPopust, @ProcentFinansiski, @PremijaZaNaplata,
                    @Uplateno, @DolznaPremija, @SifrarnikNacinNaPlakjanjeId, @TipNaFaktura,
                    @BrojNaFakturaVlezna, @DatumNaFakturaVlezna, @RokNaPlakjanjeFakturaVlezna,
                    @SifrarnikTipNaPlakanjeId, @SifrarnikBankiIdBanka,
                    @GeneriranaFakturaIzlezna, @BrojNaFakturaIzlezna,
                    @DatumNaIzleznaFaktura, @RokNaPlakjanjeFakturaIzlezna,
                    @Storno,
                    (SELECT PricinaZaStorno FROM SifrarnikPricinaZaStorno WHERE Id = @SifrarnikPricinaZaStornoId),
                    @Zabeleska);

                SELECT CAST(SCOPE_IDENTITY() as bigint)";

            using (SqlCommand cmd = new SqlCommand(insertPolisiSql, connection, transaction))
            {
                cmd.Parameters.AddWithValue("@UsernameCreated", username);
                cmd.Parameters.AddWithValue("@KlientiIdOsiguritel", Input.KlientiIdOsiguritel);
                cmd.Parameters.AddWithValue("@KlasiOsiguruvanjeIdKlasa", Input.KlasiOsiguruvanjeIdKlasa);
                cmd.Parameters.AddWithValue("@ProduktiIdProizvod", Input.ProduktiIdProizvod ?? (object)DBNull.Value);
                cmd.Parameters.AddWithValue("@BrojNaPolisa", Input.BrojNaPolisa ?? (object)DBNull.Value);
                cmd.Parameters.AddWithValue("@BrojNaPonuda", Input.BrojNaPonuda ?? (object)DBNull.Value);
                cmd.Parameters.AddWithValue("@KlientiIdDogovoruvac", Input.KlientiIdDogovoruvac);
                cmd.Parameters.AddWithValue("@KlientiIdOsigurenik", Input.KlientiIdOsigurenik ?? (object)DBNull.Value);
                cmd.Parameters.AddWithValue("@Kolektivna", Input.Kolektivna);
                cmd.Parameters.AddWithValue("@KolektivnaNeodredenBrOsigurenici", Input.KolektivnaNeodredenBrOsigurenici);
                cmd.Parameters.AddWithValue("@NeodredenBrOsigureniciZabeleska",
                    (object)Input.NeodredenBrOsigureniciZabeleska ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@DatumVaziOd", (object)Input.DatumVaziOd ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@DatumVaziDo", (object)Input.DatumVaziDo ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@DatumNaIzdavanje", (object)Input.DatumNaIzdavanje ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@SifrarnikValutiIdValuta", Input.SifrarnikValutiIdValuta);
                cmd.Parameters.AddWithValue("@KlientiIdSorabotnik", (object)Input.KlientiIdSorabotnik ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@Faktoring", Input.Faktoring);
                cmd.Parameters.AddWithValue("@SifrarnikValutiIdFranshizaValuta",
                    (object)Input.SifrarnikValutiIdFranshizaValuta ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@ProcentFranshiza", Input.ProcentFranshiza.HasValue ? (object)Input.ProcentFranshiza.Value : DBNull.Value);
                cmd.Parameters.AddWithValue("@FranshizaIznos", Input.FranshizaIznos.HasValue ? (object)Input.FranshizaIznos.Value : DBNull.Value);
                cmd.Parameters.AddWithValue("@KoregiranaStapkaNaProvizija", Input.KoregiranaStapkaNaProvizija.HasValue ? (object)Input.KoregiranaStapkaNaProvizija.Value : DBNull.Value);
                cmd.Parameters.AddWithValue("@ProcentNaPopustZaFakturaVoRok",
                    (object)Input.ProcentNaPopustZaFakturaVoRok ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@IznosZaPlakjanjeVoRok",
                    (object)Input.IznosZaPlakjanjeVoRok ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@ProcentKomercijalenPopust",
                    (object)Input.ProcentKomercijalenPopust ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@ProcentFinansiski",
                    (object)Input.ProcentFinansiski ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@PremijaZaNaplata",
                    (object)Input.PremijaZaNaplata ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@Uplateno", (object)Input.Uplateno ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@DolznaPremija", (object)Input.DolznaPremija ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@SifrarnikNacinNaPlakjanjeId", Input.SifrarnikNacinNaPlakjanjeId);
                cmd.Parameters.AddWithValue("@TipNaFaktura", (object)Input.TipNaFaktura ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@BrojNaFakturaVlezna",
                    (object)Input.BrojNaFakturaVlezna ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@DatumNaFakturaVlezna",
                    (object)Input.DatumNaFakturaVlezna ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@RokNaPlakjanjeFakturaVlezna",
                    (object)Input.RokNaPlakjanjeFakturaVlezna ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@SifrarnikTipNaPlakanjeId",
                    (object)Input.SifrarnikTipNaPlakanjeId ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@SifrarnikBankiIdBanka",
                    (object)Input.SifrarnikBankiIdBanka ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@GeneriranaFakturaIzlezna", Input.GeneriranaFakturaIzlezna);
                cmd.Parameters.AddWithValue("@BrojNaFakturaIzlezna",
                    (object)Input.BrojNaFakturaIzlezna ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@DatumNaIzleznaFaktura",
                    (object)Input.DatumNaIzleznaFaktura ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@RokNaPlakjanjeFakturaIzlezna",
                    (object)Input.RokNaPlakjanjeFakturaIzlezna ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@Storno", Input.Storno);
                cmd.Parameters.AddWithValue("@SifrarnikPricinaZaStornoId",
                    (object)Input.SifrarnikPricinaZaStornoId ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@Zabeleska", (object)Input.Zabeleska ?? DBNull.Value);

                var result = await cmd.ExecuteScalarAsync();
                return Convert.ToInt64(result);
            }
        }

        private async Task<long> InsertPolisiKlasa21(SqlConnection connection, SqlTransaction transaction, long polisaId, string username)
        {
            string insertKlasa21Sql = @"
                INSERT INTO PolisiKlasa21 (
                    UsernameCreated, PolisaId, ProcentNaPopustZaFakturaVoRok, IznosZaPlakjanjeVoRok,
                    ProcentKomercijalenPopust, ProcentFinansiski, PremijaZaNaplata,
                    ShifrarnikInvesticiskiFondIdInvesticiskiFond, NazivNaInvesticiskoDrushtvo,
                    NazivNaInvesticiskiFond, IdentifikaciskiBrojaNaInvesticiskiFond,
                    ProcentOdPremijaPoIFGodishnoEdnokratno, ProcentOdPremijaVoOsiguruvanjeGodishnoEdnokratno,
                    IznosOdPremijaPoIFGodishnoEdnokratno, IznosOdPremijaVoOsiguruvanjeGodishnoEdnokratno,
                    VkupenIznosNaPremijaZaOsiguruvanje, VkupenIznosNaPremijaZaIF, VkupenIznosNaTroshociOdPremija,
                    OsigurenaSumaZaDozhivuvanje, OsigurenaSumaZaSmrtOdBolest, OsigurenaSumaZaSmrtOdNezgoda,
                    PrivremenaOsiguritelnaZastita, PremijaGodishnaEdnokratna, BrojNaUdeliPoIF,
                    VkupnaPremijaGodishna, VkupnaPremijaEdnokratna, DoplatociZaPodgodishnoPlakjanje
                ) VALUES (
                    @UsernameCreated, @PolisaId, @ProcentNaPopustZaFakturaVoRok, @IznosZaPlakjanjeVoRok,
                    @ProcentKomercijalenPopust, @ProcentFinansiski, @PremijaZaNaplata,
                    @ShifrarnikInvesticiskiFondIdInvesticiskiFond, @NazivNaInvesticiskoDrushtvo,
                    (SELECT Naziv FROM ShifrarnikInvesticiskiFond WHERE Id = @ShifrarnikInvesticiskiFondIdInvesticiskiFond),
                    (SELECT IdentifikaciskiBrojaNaInvesticiskiFond FROM ShifrarnikInvesticiskiFond WHERE Id = @ShifrarnikInvesticiskiFondIdInvesticiskiFond),
                    @ProcentOdPremijaPoIFGodishnoEdnokratno, @ProcentOdPremijaVoOsiguruvanjeGodishnoEdnokratno,
                    @IznosOdPremijaPoIFGodishnoEdnokratno, @IznosOdPremijaVoOsiguruvanjeGodishnoEdnokratno,
                    @VkupenIznosNaPremijaZaOsiguruvanje, @VkupenIznosNaPremijaZaIF, @VkupenIznosNaTroshociOdPremija,
                    @OsigurenaSumaZaDozhivuvanje, @OsigurenaSumaZaSmrtOdBolest, @OsigurenaSumaZaSmrtOdNezgoda,
                    @PrivremenaOsiguritelnaZastita, @PremijaGodishnaEdnokratna, @BrojNaUdeliPoIF,
                    @VkupnaPremijaGodishna, @VkupnaPremijaEdnokratna, @DoplatociZaPodgodishnoPlakjanje
                );

                SELECT CAST(SCOPE_IDENTITY() as bigint)";

            using (SqlCommand cmd = new SqlCommand(insertKlasa21Sql, connection, transaction))
            {
                cmd.Parameters.AddWithValue("@UsernameCreated", username);
                cmd.Parameters.AddWithValue("@PolisaId", polisaId);
                cmd.Parameters.AddWithValue("@ProcentNaPopustZaFakturaVoRok", (object)Input.ProcentNaPopustZaFakturaVoRok ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@IznosZaPlakjanjeVoRok", (object)Input.IznosZaPlakjanjeVoRok ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@ProcentKomercijalenPopust", (object)Input.ProcentKomercijalenPopust ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@ProcentFinansiski", (object)Input.ProcentFinansiski ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@PremijaZaNaplata", (object)Input.PremijaZaNaplata ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@ShifrarnikInvesticiskiFondIdInvesticiskiFond", Input.ShifrarnikInvesticiskiFondIdInvesticiskiFond);
                cmd.Parameters.AddWithValue("@NazivNaInvesticiskoDrushtvo", Input.NazivNaInvesticiskoDrushtvo);
                cmd.Parameters.AddWithValue("@ProcentOdPremijaPoIFGodishnoEdnokratno", (object)Input.ProcentOdPremijaPoIFGodishnoEdnokratno ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@ProcentOdPremijaVoOsiguruvanjeGodishnoEdnokratno", (object)Input.ProcentOdPremijaVoOsiguruvanjeGodishnoEdnokratno ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@IznosOdPremijaPoIFGodishnoEdnokratno", (object)Input.IznosOdPremijaPoIFGodishnoEdnokratno ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@IznosOdPremijaVoOsiguruvanjeGodishnoEdnokratno", (object)Input.IznosOdPremijaVoOsiguruvanjeGodishnoEdnokratno ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@VkupenIznosNaPremijaZaOsiguruvanje", (object)Input.VkupenIznosNaPremijaZaOsiguruvanje ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@VkupenIznosNaPremijaZaIF", (object)Input.VkupenIznosNaPremijaZaIF ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@VkupenIznosNaTroshociOdPremija", (object)Input.VkupenIznosNaTroshociOdPremija ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@OsigurenaSumaZaDozhivuvanje", Input.OsigurenaSumaZaDozhivuvanje ?? (object)DBNull.Value);
                cmd.Parameters.AddWithValue("@OsigurenaSumaZaSmrtOdBolest", Input.OsigurenaSumaZaSmrtOdBolest ?? (object)DBNull.Value);
                cmd.Parameters.AddWithValue("@OsigurenaSumaZaSmrtOdNezgoda", Input.OsigurenaSumaZaSmrtOdNezgoda ?? (object)DBNull.Value);
                cmd.Parameters.AddWithValue("@PrivremenaOsiguritelnaZastita", Input.PrivremenaOsiguritelnaZastita ?? (object)DBNull.Value);
                cmd.Parameters.AddWithValue("@PremijaGodishnaEdnokratna", Input.PremijaGodishnaEdnokratna ?? (object)DBNull.Value);
                cmd.Parameters.AddWithValue("@BrojNaUdeliPoIF", (object)Input.BrojNaUdeliPoIF ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@VkupnaPremijaGodishna", (object)Input.VkupnaPremijaGodishna ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@VkupnaPremijaEdnokratna", (object)Input.VkupnaPremijaEdnokratna ?? DBNull.Value);
                cmd.Parameters.AddWithValue("@DoplatociZaPodgodishnoPlakjanje", (object)Input.DoplatociZaPodgodishnoPlakjanje ?? DBNull.Value);

                var result = await cmd.ExecuteScalarAsync();
                return Convert.ToInt64(result);
            }
        }

        private async Task InsertKlasa1Entries(SqlConnection connection, SqlTransaction transaction, long polisiKlasa21Id)
        {

            string insertSql = @"
                INSERT INTO PolisiKlasa21DopolnitelnoKlasa1 (
                    PolisiKlasa21IdPolisa, OsigurenaSumaZaSmrtOdNezgoda, OsigurenaSumaZa100ProcTraenInvaliditet,
                    OsigurenaSumaZaTraenInvaliditet, DnevenNadomest, PremijaNezgodaGodishna
                ) VALUES (
                    @PolisiKlasa21IdPolisa, @OsigurenaSumaZaSmrtOdNezgoda, @OsigurenaSumaZa100ProcTraenInvaliditet,
                    @OsigurenaSumaZaTraenInvaliditet, @DnevenNadomest, @PremijaNezgodaGodishna
                )";

            foreach (var entry in Input.Klasa1Entries)
            {
                using (SqlCommand cmd = new SqlCommand(insertSql, connection, transaction))
                {
                    cmd.Parameters.AddWithValue("@PolisiKlasa21IdPolisa", polisiKlasa21Id);
                    cmd.Parameters.AddWithValue("@OsigurenaSumaZaSmrtOdNezgoda", (object)entry.OsigurenaSumaZaSmrtOdNezgoda ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsigurenaSumaZa100ProcTraenInvaliditet", (object)entry.OsigurenaSumaZa100ProcTraenInvaliditet ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsigurenaSumaZaTraenInvaliditet", (object)entry.OsigurenaSumaZaTraenInvaliditet ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@DnevenNadomest", (object)entry.DnevenNadomest ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaNezgodaGodishna", (object)entry.PremijaNezgodaGodishna ?? DBNull.Value);

                    await cmd.ExecuteNonQueryAsync();
                }
            }
        }

        private async Task InsertKlasa2Entries(SqlConnection connection, SqlTransaction transaction, long polisiKlasa21Id)
        {

            string insertSql = @"
                INSERT INTO PolisiKlasa21DopolnitelnoKlasa2 (
                    PolisiKlasa21IdPolisa, OsigurenaSumaZaTeskoBolniSostojbi, PremijaTeshkoBolniSostojbiGodishna,
                    OsigurenaSumaZaOperacii, PremijaZaOperaciiGodishna, OsigurenaSumaZaTrajnaNesposobnost,
                    PremijaZaTrajnaNesposobnostGodishna, OsigurenaSumaZaHirushkiIntervencii, PremijaZaHirushkiIntervenciiGodishna
                ) VALUES (
                    @PolisiKlasa21IdPolisa, @OsigurenaSumaZaTeskoBolniSostojbi, @PremijaTeshkoBolniSostojbiGodishna,
                    @OsigurenaSumaZaOperacii, @PremijaZaOperaciiGodishna, @OsigurenaSumaZaTrajnaNesposobnost,
                    @PremijaZaTrajnaNesposobnostGodishna, @OsigurenaSumaZaHirushkiIntervencii, @PremijaZaHirushkiIntervenciiGodishna
                )";

            foreach (var entry in Input.Klasa2Entries)
            {
                using (SqlCommand cmd = new SqlCommand(insertSql, connection, transaction))
                {
                    cmd.Parameters.AddWithValue("@PolisiKlasa21IdPolisa", polisiKlasa21Id);
                    cmd.Parameters.AddWithValue("@OsigurenaSumaZaTeskoBolniSostojbi", (object)entry.OsigurenaSumaZaTeskoBolniSostojbi ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaTeshkoBolniSostojbiGodishna", (object)entry.PremijaTeshkoBolniSostojbiGodishna ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsigurenaSumaZaOperacii", (object)entry.OsigurenaSumaZaOperacii ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaZaOperaciiGodishna", (object)entry.PremijaZaOperaciiGodishna ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsigurenaSumaZaTrajnaNesposobnost", (object)entry.OsigurenaSumaZaTrajnaNesposobnost ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaZaTrajnaNesposobnostGodishna", (object)entry.PremijaZaTrajnaNesposobnostGodishna ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@OsigurenaSumaZaHirushkiIntervencii", (object)entry.OsigurenaSumaZaHirushkiIntervencii ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@PremijaZaHirushkiIntervenciiGodishna", (object)entry.PremijaZaHirushkiIntervenciiGodishna ?? DBNull.Value);

                    await cmd.ExecuteNonQueryAsync();
                }
            }
        }

        public async Task<JsonResult> OnGetCheckPolicyNumber(string brojNaPolisa)
        {
            if (string.IsNullOrEmpty(brojNaPolisa))
            {
                return new JsonResult(new { exists = false });
            }

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT COUNT(*)
                    FROM Polisi
                    WHERE BrojNaPolisa = @BrojNaPolisa
                    AND (Storno = 0 OR Storno IS NULL)", connection))
                {
                    cmd.Parameters.AddWithValue("@BrojNaPolisa", brojNaPolisa);
                    int count = Convert.ToInt32(await cmd.ExecuteScalarAsync());
                    return new JsonResult(new { exists = count > 0 });
                }
            }
        }

        public async Task<IActionResult> OnGetSearchKlientiAsync(string mb)
        {
            if (string.IsNullOrWhiteSpace(mb))
                return new JsonResult(new List<object>());

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT TOP 10
                        Id,
                        ISNULL(MB, '') as MB,
                        ISNULL(EDB, '') as EDB,
                        ISNULL(EMBG, '') as EMBG,
                        CASE
                            WHEN KlientFizickoPravnoLice = 'P' THEN Naziv
                            ELSE CONCAT(ISNULL(Ime, ''), ' ', ISNULL(Prezime, ''))
                        END as Naziv,
                        KlientFizickoPravnoLice,
                        ISNULL(Ime, '') as Ime,
                        ISNULL(Prezime, '') as Prezime
                    FROM Klienti
                    WHERE MB LIKE @Search + '%'
                       OR EDB LIKE @Search + '%'
                       OR EMBG LIKE @Search + '%'
                       OR Naziv LIKE '%' + @Search + '%'
                       OR Ime LIKE '%' + @Search + '%'
                       OR Prezime LIKE '%' + @Search + '%'
                    ORDER BY
                        CASE
                            WHEN MB LIKE @Search + '%' THEN 1
                            WHEN EDB LIKE @Search + '%' THEN 2
                            WHEN EMBG LIKE @Search + '%' THEN 3
                            ELSE 4
                        END,
                        MB, Naziv", connection))
                {
                    cmd.Parameters.AddWithValue("@Search", mb);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var results = new List<object>();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            id = reader["Id"],
                            mb = reader["MB"],
                            edb = reader["EDB"],
                            embg = reader["EMBG"],
                            naziv = reader["Naziv"],
                            ime = reader["Ime"],
                            prezime = reader["Prezime"],
                            tip = reader["KlientFizickoPravnoLice"].ToString()
                        });
                    }
                    return new JsonResult(results);
                }
            }
        }

        public async Task<IActionResult> OnGetSearchSorabotniciAsync(string mb)
        {
            if (string.IsNullOrWhiteSpace(mb))
                return new JsonResult(new List<object>());

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT TOP 10
                        Id,
                        ISNULL(MB, '') as MB,
                        ISNULL(EDB, '') as EDB,
                        ISNULL(EMBG, '') as EMBG,
                        CASE
                            WHEN KlientFizickoPravnoLice = 'P' THEN Naziv
                            ELSE CONCAT(ISNULL(Ime, ''), ' ', ISNULL(Prezime, ''))
                        END as Naziv,
                        KlientFizickoPravnoLice,
                        ISNULL(Ime, '') as Ime,
                        ISNULL(Prezime, '') as Prezime
                    FROM Klienti
                    WHERE (KlientSorabotnik = 1 or KlientVraboten = 1)
                        AND (MB LIKE @Search + '%'
                        OR EDB LIKE @Search + '%'
                        OR EMBG LIKE @Search + '%'
                        OR Naziv LIKE '%' + @Search + '%'
                        OR Ime LIKE '%' + @Search + '%'
                        OR Prezime LIKE '%' + @Search + '%')
                    ORDER BY
                        CASE
                            WHEN MB LIKE @Search + '%' THEN 1
                            WHEN EDB LIKE @Search + '%' THEN 2
                            WHEN EMBG LIKE @Search + '%' THEN 3
                            ELSE 4
                        END,
                        MB, Naziv", connection))
                {
                    cmd.Parameters.AddWithValue("@Search", mb);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var results = new List<object>();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            id = reader["Id"],
                            mb = reader["MB"],
                            edb = reader["EDB"],
                            embg = reader["EMBG"],
                            naziv = reader["Naziv"],
                            ime = reader["Ime"],
                            prezime = reader["Prezime"],
                            tip = reader["KlientFizickoPravnoLice"].ToString()
                        });
                    }
                    return new JsonResult(results);
                }
            }
        }

        public IActionResult OnPostAddInvesticiskoDrushtvo(string naziv)
        {
            if (string.IsNullOrWhiteSpace(naziv))
            {
                TempData["ErrorMessage"] = "Називот на друштвото е задолжителен.";
                return RedirectToPage();
            }

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();

                // Check if company with same name already exists
                using (SqlCommand checkCommand = new SqlCommand(
                    "SELECT COUNT(*) FROM ShifrarnikInvesticiskoDrushtvo WHERE LOWER(Naziv) = LOWER(@Naziv)",
                    connection))
                {
                    checkCommand.Parameters.AddWithValue("@Naziv", naziv);
                    int existingCount = (int)checkCommand.ExecuteScalar();

                    if (existingCount > 0)
                    {
                        TempData["ErrorMessage"] = $"Друштво со назив '{naziv}' веќе постои.";
                        return RedirectToPage();
                    }
                }

                // Insert new company
                using (SqlCommand command = new SqlCommand(
                    "INSERT INTO ShifrarnikInvesticiskoDrushtvo (Naziv) VALUES (@Naziv)",
                    connection))
                {
                    command.Parameters.AddWithValue("@Naziv", naziv);
                    command.ExecuteNonQuery();
                }
            }

            TempData["SuccessMessage"] = $"Друштвото '{naziv}' е успешно додадено.";
            return RedirectToPage();
        }

        public IActionResult OnPostAddInvesticiskiFond(string naziv, string identifikaciskiBroj, long drushtvoId)
        {
            if (string.IsNullOrWhiteSpace(naziv))
            {
                TempData["ErrorMessage"] = "Називот на фондот е задолжителен.";
                return RedirectToPage();
            }

            if (string.IsNullOrWhiteSpace(identifikaciskiBroj))
            {
                TempData["ErrorMessage"] = "Идентификацискиот број е задолжителен.";
                return RedirectToPage();
            }

            if (drushtvoId <= 0)
            {
                TempData["ErrorMessage"] = "Изберете друштво.";
                return RedirectToPage();
            }

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();

                // Check if fund with same name and identification number already exists
                using (SqlCommand checkCommand = new SqlCommand(
                    "SELECT COUNT(*) FROM ShifrarnikInvesticiskiFond WHERE LOWER(Naziv) = LOWER(@Naziv) AND LOWER(IdentifikaciskiBrojaNaInvesticiskiFond) = LOWER(@IdentifikaciskiBroj)",
                    connection))
                {
                    checkCommand.Parameters.AddWithValue("@Naziv", naziv);
                    checkCommand.Parameters.AddWithValue("@IdentifikaciskiBroj", identifikaciskiBroj);
                    int existingCount = (int)checkCommand.ExecuteScalar();

                    if (existingCount > 0)
                    {
                        TempData["ErrorMessage"] = $"Инвестициски фонд со назив '{naziv}' и идентификациски број '{identifikaciskiBroj}' веќе постои.";
                        return RedirectToPage();
                    }
                }

                // Also check if identification number already exists (regardless of name)
                using (SqlCommand checkIdCommand = new SqlCommand(
                    "SELECT COUNT(*) FROM ShifrarnikInvesticiskiFond WHERE LOWER(IdentifikaciskiBrojaNaInvesticiskiFond) = LOWER(@IdentifikaciskiBroj)",
                    connection))
                {
                    checkIdCommand.Parameters.AddWithValue("@IdentifikaciskiBroj", identifikaciskiBroj);
                    int existingIdCount = (int)checkIdCommand.ExecuteScalar();

                    if (existingIdCount > 0)
                    {
                        TempData["ErrorMessage"] = $"Инвестициски фонд со идентификациски број '{identifikaciskiBroj}' веќе постои.";
                        return RedirectToPage();
                    }
                }

                // Insert new fund
                using (SqlCommand command = new SqlCommand(
                    "INSERT INTO ShifrarnikInvesticiskiFond (Naziv, IdentifikaciskiBrojaNaInvesticiskiFond, ShifrarnikInvesticiskoDrushtvoIdDrushtvo) VALUES (@Naziv, @IdentifikaciskiBroj, @DrushtvoId)",
                    connection))
                {
                    command.Parameters.AddWithValue("@Naziv", naziv);
                    command.Parameters.AddWithValue("@IdentifikaciskiBroj", identifikaciskiBroj);
                    command.Parameters.AddWithValue("@DrushtvoId", drushtvoId);
                    command.ExecuteNonQuery();
                }
            }

            TempData["SuccessMessage"] = $"Инвестицискиот фонд '{naziv}' е успешно додаден.";
            return RedirectToPage();
        }

        public async Task<JsonResult> OnGetGetInvestmentFundDetails(long fondId)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT
                        f.Naziv,
                        f.IdentifikaciskiBrojaNaInvesticiskiFond,
                        d.Naziv as DrushtvoNaziv
                    FROM ShifrarnikInvesticiskiFond f
                    INNER JOIN ShifrarnikInvesticiskoDrushtvo d ON f.ShifrarnikInvesticiskoDrushtvoIdDrushtvo = d.Id
                    WHERE f.Id = @FondId", connection))
                {
                    cmd.Parameters.AddWithValue("@FondId", fondId);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    if (await reader.ReadAsync())
                    {
                        return new JsonResult(new
                        {
                            naziv = reader["Naziv"].ToString(),
                            identifikaciskiBroj = reader["IdentifikaciskiBrojaNaInvesticiskiFond"].ToString(),
                            drushtvoNaziv = reader["DrushtvoNaziv"].ToString()
                        });
                    }
                }
            }

            return new JsonResult(new { naziv = "", identifikaciskiBroj = "", drushtvoNaziv = "" });
        }
    }

    public class InvesticiskoDrushtvo
    {
        public long Id { get; set; }
        public string Naziv { get; set; }
    }

    public class InvesticiskiFond
    {
        public long Id { get; set; }
        public string Naziv { get; set; }
        public string IdentifikaciskiBrojaNaInvesticiskiFond { get; set; }
        public string DrushtvoNaziv { get; set; }
    }

    public class PolisaKlasa21InputModel
    {
        // Basic Polisi fields
        [Required(ErrorMessage = "Осигурителот е задолжителен")]
        public long KlientiIdOsiguritel { get; set; }

        [Required(ErrorMessage = "Класата на осигурување е задолжителна")]
        public int KlasiOsiguruvanjeIdKlasa { get; set; }

        public int? ProduktiIdProizvod { get; set; }

        [Required(ErrorMessage = "Бројот на полиса е задолжителен")]
        public string BrojNaPolisa { get; set; }

        public long? BrojNaPonuda { get; set; }

        [Required(ErrorMessage = "Договорувачот е задолжителен")]
        public long KlientiIdDogovoruvac { get; set; }

        public long? KlientiIdOsigurenik { get; set; }

        public long? KlientiIdSorabotnik { get; set; }

        public DateTime? DatumVaziOd { get; set; }

        public DateTime? DatumVaziDo { get; set; }

        public DateTime? DatumNaIzdavanje { get; set; }

        [Required(ErrorMessage = "Валутата е задолжителна")]
        public long SifrarnikValutiIdValuta { get; set; }

        [Required(ErrorMessage = "Начинот на плаќање е задолжителен")]
        public long SifrarnikNacinNaPlakjanjeId { get; set; }

        public string? TipNaFaktura { get; set; }

        public bool Faktoring { get; set; }

        public long? SifrarnikValutiIdFranshizaValuta { get; set; }

        public decimal? ProcentFranshiza { get; set; }

        public decimal? FranshizaIznos { get; set; }

        public decimal? KoregiranaStapkaNaProvizija { get; set; }

        public bool GeneriranaFakturaIzlezna { get; set; }

        public bool Storno { get; set; }

        // Additional Polisi fields
        public bool Kolektivna { get; set; }
        public bool KolektivnaNeodredenBrOsigurenici { get; set; }
        public string? NeodredenBrOsigureniciZabeleska { get; set; }
        public string? BrojNaFakturaVlezna { get; set; }
        public DateTime? DatumNaFakturaVlezna { get; set; }
        public DateTime? RokNaPlakjanjeFakturaVlezna { get; set; }
        public long? SifrarnikTipNaPlakanjeId { get; set; }
        public long? SifrarnikBankiIdBanka { get; set; }
        public string? BrojNaFakturaIzlezna { get; set; }
        public DateTime? DatumNaIzleznaFaktura { get; set; }
        public DateTime? RokNaPlakjanjeFakturaIzlezna { get; set; }
        public long? SifrarnikPricinaZaStornoId { get; set; }
        public string? Zabeleska { get; set; }
        public decimal? Uplateno { get; set; }
        public decimal? DolznaPremija { get; set; }

        // Klasa 21 specific fields
        public decimal? ProcentNaPopustZaFakturaVoRok { get; set; }

        public decimal? IznosZaPlakjanjeVoRok { get; set; }

        public decimal? ProcentKomercijalenPopust { get; set; }

        public decimal? ProcentFinansiski { get; set; }

        public decimal? PremijaZaNaplata { get; set; }

        [Required(ErrorMessage = "Инвестицискиот фонд е задолжителен")]
        public long ShifrarnikInvesticiskiFondIdInvesticiskiFond { get; set; }

        public string? NazivNaInvesticiskoDrushtvo { get; set; }

        public string? IdentifikaciskiBrojaNaInvesticiskiFond { get; set; }

        public decimal? ProcentOdPremijaPoIFGodishnoEdnokratno { get; set; }

        public decimal? ProcentOdPremijaVoOsiguruvanjeGodishnoEdnokratno { get; set; }

        public decimal? IznosOdPremijaPoIFGodishnoEdnokratno { get; set; }

        public decimal? IznosOdPremijaVoOsiguruvanjeGodishnoEdnokratno { get; set; }

        public decimal? VkupenIznosNaPremijaZaOsiguruvanje { get; set; }

        public decimal? VkupenIznosNaPremijaZaIF { get; set; }

        public decimal? VkupenIznosNaTroshociOdPremija { get; set; }

        public string? OsigurenaSumaZaDozhivuvanje { get; set; }

        public string? OsigurenaSumaZaSmrtOdBolest { get; set; }

        public string? OsigurenaSumaZaSmrtOdNezgoda { get; set; }

        public string? PrivremenaOsiguritelnaZastita { get; set; }

        public string? PremijaGodishnaEdnokratna { get; set; }

        public long? BrojNaUdeliPoIF { get; set; }

        public decimal? VkupnaPremijaGodishna { get; set; }

        public decimal? VkupnaPremijaEdnokratna { get; set; }

        public decimal? DoplatociZaPodgodishnoPlakjanje { get; set; }

        // Supplementary insurance entries
        public List<Klasa1Entry> Klasa1Entries { get; set; } = new List<Klasa1Entry>();

        public List<Klasa2Entry> Klasa2Entries { get; set; } = new List<Klasa2Entry>();
    }

    public class Klasa1Entry
    {
        public decimal? OsigurenaSumaZaSmrtOdNezgoda { get; set; }
        public decimal? OsigurenaSumaZa100ProcTraenInvaliditet { get; set; }
        public decimal? OsigurenaSumaZaTraenInvaliditet { get; set; }
        public decimal? DnevenNadomest { get; set; }
        public decimal? PremijaNezgodaGodishna { get; set; }
    }

    public class Klasa2Entry
    {
        public decimal? OsigurenaSumaZaTeskoBolniSostojbi { get; set; }
        public decimal? PremijaTeshkoBolniSostojbiGodishna { get; set; }
        public decimal? OsigurenaSumaZaOperacii { get; set; }
        public decimal? PremijaZaOperaciiGodishna { get; set; }
        public decimal? OsigurenaSumaZaTrajnaNesposobnost { get; set; }
        public decimal? PremijaZaTrajnaNesposobnostGodishna { get; set; }
        public decimal? OsigurenaSumaZaHirushkiIntervencii { get; set; }
        public decimal? PremijaZaHirushkiIntervenciiGodishna { get; set; }
    }
}
