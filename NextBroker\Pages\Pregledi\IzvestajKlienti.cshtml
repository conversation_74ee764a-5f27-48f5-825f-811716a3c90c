@page
@model NextBroker.Pages.Pregledi.IzvestajKlientiModel
@{
    ViewData["Title"] = "Извештај Клиенти";
    Layout = "_Layout";
}

<style>
    #klientiTable {
        font-size: 0.85rem;
    }

    #klientiTable th {
        white-space: nowrap;
        min-width: 120px;
        padding: 8px 12px;
        text-align: center;
        vertical-align: middle;
        background-color: #343a40 !important;
        color: white !important;
        border: 1px solid #495057;
    }

    #klientiTable td {
        padding: 6px 8px;
        vertical-align: middle;
        border: 1px solid #dee2e6;
        max-width: 200px;
        word-wrap: break-word;
    }

    /* Specific widths for important columns */
    #klientiTable th:nth-child(1), #klientiTable td:nth-child(1) { min-width: 100px; } /* Име */
    #klientiTable th:nth-child(2), #klientiTable td:nth-child(2) { min-width: 120px; } /* Презиме */
    #klientiTable th:nth-child(3), #klientiTable td:nth-child(3) { min-width: 200px; } /* Назив */
    #klientiTable th:nth-child(4), #klientiTable td:nth-child(4) { min-width: 120px; } /* ЕДБ */
    #klientiTable th:nth-child(5), #klientiTable td:nth-child(5) { min-width: 120px; } /* МБ */
    #klientiTable th:nth-child(6), #klientiTable td:nth-child(6) { min-width: 120px; } /* ЕМБГ */
    #klientiTable th:nth-child(18), #klientiTable td:nth-child(18) { min-width: 180px; } /* Е-маил */
    #klientiTable th:nth-child(19), #klientiTable td:nth-child(19) { min-width: 120px; } /* Телефон */

    /* Date columns */
    #klientiTable th:nth-child(13), #klientiTable td:nth-child(13),
    #klientiTable th:nth-child(15), #klientiTable td:nth-child(15),
    #klientiTable th:nth-child(16), #klientiTable td:nth-child(16),
    #klientiTable th:nth-child(32), #klientiTable td:nth-child(32),
    #klientiTable th:nth-child(33), #klientiTable td:nth-child(33) {
        min-width: 110px;
    }

    /* Boolean columns */
    #klientiTable th:nth-child(21), #klientiTable td:nth-child(21),
    #klientiTable th:nth-child(22), #klientiTable td:nth-child(22),
    #klientiTable th:nth-child(23), #klientiTable td:nth-child(23),
    #klientiTable th:nth-child(25), #klientiTable td:nth-child(25),
    #klientiTable th:nth-child(28), #klientiTable td:nth-child(28),
    #klientiTable th:nth-child(30), #klientiTable td:nth-child(30),
    #klientiTable th:nth-child(41), #klientiTable td:nth-child(41) {
        min-width: 80px;
        text-align: center;
    }

    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .sortable-header {
        cursor: pointer;
        user-select: none;
    }

    .sortable-header:hover {
        background-color: #495057 !important;
    }
</style>

@functions {
    private string GetSortableHeader(string columnName, string displayName)
    {
        var currentSort = Model.SortColumn;
        var currentDirection = Model.SortDirection;
        var newDirection = "asc";
        var icon = "";

        if (currentSort == columnName)
        {
            newDirection = currentDirection == "asc" ? "desc" : "asc";
            icon = currentDirection == "asc" ? "<i class='fas fa-sort-up ml-1'></i>" : "<i class='fas fa-sort-down ml-1'></i>";
        }
        else
        {
            icon = "<i class='fas fa-sort ml-1 text-muted'></i>";
        }

        var url = $"?sortColumn={columnName}&sortDirection={newDirection}&searchTerm={Model.SearchTerm}";
        return $"<a href='{url}' class='text-white text-decoration-none sortable-header d-block w-100 h-100 p-2'>{displayName}{icon}</a>";
    }
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Извештај Клиенти</h3>
                    <div class="card-tools">
                        <a href="?handler=ExportExcel&searchTerm=@Model.SearchTerm&sortColumn=@Model.SortColumn&sortDirection=@Model.SortDirection"
                           class="btn btn-success btn-sm">
                            <i class="fas fa-file-excel"></i> Извези во Excel
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Search Form -->
                    <form method="get" class="mb-3">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <input type="text" name="searchTerm" value="@Model.SearchTerm"
                                           class="form-control" placeholder="Пребарај по име, презиме, назив, ЕДБ, МБ, ЕМБГ, е-маил или телефон...">
                                    <input type="hidden" name="sortColumn" value="@Model.SortColumn" />
                                    <input type="hidden" name="sortDirection" value="@Model.SortDirection" />
                                    <div class="input-group-append">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search"></i> Пребарај
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                @if (!string.IsNullOrEmpty(Model.SearchTerm))
                                {
                                    <a href="?" class="btn btn-secondary">
                                        <i class="fas fa-times"></i> Исчисти филтер
                                    </a>
                                    <span class="ml-2 text-muted">
                                        Прикажани се @Model.Klienti.Count резултати за "@Model.SearchTerm"
                                    </span>
                                }
                                else
                                {
                                    <span class="text-muted">Вкупно @Model.Klienti.Count клиенти</span>
                                }
                            </div>
                        </div>
                    </form>

                    <!-- Results Table -->
                    @if (Model.Klienti.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped table-hover" id="klientiTable">
                                <thead class="thead-dark">
                                    <tr>
                                        <th>@Html.Raw(GetSortableHeader("Ime", "Име"))</th>
                                        <th>@Html.Raw(GetSortableHeader("Prezime", "Презиме"))</th>
                                        <th>@Html.Raw(GetSortableHeader("Naziv", "Назив"))</th>
                                        <th>@Html.Raw(GetSortableHeader("EDB", "ЕДБ"))</th>
                                        <th>@Html.Raw(GetSortableHeader("MB", "МБ"))</th>
                                        <th>@Html.Raw(GetSortableHeader("EMBG", "ЕМБГ"))</th>
                                        <th>Општина (документ)</th>
                                        <th>Улица (документ)</th>
                                        <th>Број (документ)</th>
                                        <th>Општина (комуникација)</th>
                                        <th>Улица (комуникација)</th>
                                        <th>Број (комуникација)</th>
                                        <th>@Html.Raw(GetSortableHeader("DatumNaTekovnaSostojba", "Датум тековна состојба"))</th>
                                        <th>Број пасош/лична карта</th>
                                        <th>Важи од</th>
                                        <th>Важи до</th>
                                        <th>Дејност</th>
                                        <th>@Html.Raw(GetSortableHeader("Email", "Е-маил"))</th>
                                        <th>@Html.Raw(GetSortableHeader("Tel", "Телефон"))</th>
                                        <th>Веб страна</th>
                                        <th>Согласност директен маркетинг</th>
                                        <th>Согласност е-маил</th>
                                        <th>Согласност телефон</th>
                                        <th>Датум повлечена согласност</th>
                                        <th>Вистински сопственик</th>
                                        <th>Вистински сопственик име</th>
                                        <th>Вистински сопственик презиме</th>
                                        <th>Носител јавна функција</th>
                                        <th>Основ за носител ЈФ</th>
                                        <th>Засилена анализа</th>
                                        <th>Ниво на ризик</th>
                                        <th>@Html.Raw(GetSortableHeader("DatumNaDogovor", "Датум договор"))</th>
                                        <th>@Html.Raw(GetSortableHeader("DogovorVaziDo", "Договор важи до"))</th>
                                        <th>Број договор</th>
                                        <th>Договор определено/неопределено</th>
                                        <th>Платежна сметка</th>
                                        <th>Банка</th>
                                        <th>Работна позиција</th>
                                        <th>Организациона единица</th>
                                        <th>Експозитура</th>
                                        <th>Задолжително лиценца</th>
                                        <th>Број решение АСО лиценца</th>
                                        <th>Датум решение АСО лиценца</th>
                                        <th>Датум одземена лиценца</th>
                                        <th>Број дозвола брокерски работи</th>
                                        <th>Датум дозвола брокерски работи</th>
                                        <th>Живот/Неживот</th>
                                        <th>Надреден</th>
                                        <th>Надреден важи од</th>
                                        <th>Надреден важи до</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var klient in Model.Klienti)
                                    {
                                        <tr>
                                            <td>@klient.Ime</td>
                                            <td>@klient.Prezime</td>
                                            <td>@klient.Naziv</td>
                                            <td>@klient.EDB</td>
                                            <td>@klient.MB</td>
                                            <td>@klient.EMBG</td>
                                            <td>@klient.OpstinaOdDokument</td>
                                            <td>@klient.UlicaOdDokument</td>
                                            <td>@klient.BrojOdDokument</td>
                                            <td>@klient.OpstinaZaKomunikacija</td>
                                            <td>@klient.UlicaZaKomunikacija</td>
                                            <td>@klient.BrojZaKomunikacija</td>
                                            <td>@klient.DatumNaTekovnaSostojba?.ToString("dd.MM.yyyy")</td>
                                            <td>@klient.BrojPasosLicnaKarta</td>
                                            <td>@klient.DatumVaziOdPasosLicnaKarta?.ToString("dd.MM.yyyy")</td>
                                            <td>@klient.DatumVaziDoPasosLicnaKarta?.ToString("dd.MM.yyyy")</td>
                                            <td>@klient.Dejnost</td>
                                            <td>@klient.Email</td>
                                            <td>@klient.Tel</td>
                                            <td>@klient.WebStrana</td>
                                            <td>@(klient.SoglasnostZaDirektenMarketing ? "Да" : "Не")</td>
                                            <td>@(klient.SoglasnostZaEmailKomunikacija ? "Да" : "Не")</td>
                                            <td>@(klient.SoglasnostZaTelKomunikacija ? "Да" : "Не")</td>
                                            <td>@klient.DatumNaPovlecenaSoglasnostZaDirektenMarketing?.ToString("dd.MM.yyyy")</td>
                                            <td>@(klient.VistinskiSopstvenik ? "Да" : "Не")</td>
                                            <td>@klient.VistinskiSopstvenikIme</td>
                                            <td>@klient.VistinskiSopstvenikPrezime</td>
                                            <td>@(klient.NositelNaJF ? "Да" : "Не")</td>
                                            <td>@klient.OsnovZaNositelNaJF</td>
                                            <td>@(klient.ZasilenaAnaliza ? "Да" : "Не")</td>
                                            <td>@klient.NivoNaRizik</td>
                                            <td>@klient.DatumNaDogovor?.ToString("dd.MM.yyyy")</td>
                                            <td>@klient.DogovorVaziDo?.ToString("dd.MM.yyyy")</td>
                                            <td>@klient.BrojNaDogovor</td>
                                            <td>@klient.DogovorOpredelenoNeopredeleno</td>
                                            <td>@klient.PlateznaSmetka</td>
                                            <td>@klient.Banka</td>
                                            <td>@klient.RabotnaPozicija</td>
                                            <td>@klient.OrganizacionaEdinica</td>
                                            <td>@klient.Ekspozitura</td>
                                            <td>@(klient.ZadolzitelnoLicenca ? "Да" : "Не")</td>
                                            <td>@klient.BrojNaResenieOdASOZaLicenca</td>
                                            <td>@klient.DatumNaResenieOdASOZaLicenca?.ToString("dd.MM.yyyy")</td>
                                            <td>@klient.DatumNaOdzemenaLicenca?.ToString("dd.MM.yyyy")</td>
                                            <td>@klient.BrojNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti</td>
                                            <td>@klient.DatumNaDozvolaZaVrsenjeOsiguritelnoBrokerskiRaboti?.ToString("dd.MM.yyyy")</td>
                                            <td>@klient.ZivotNezivot</td>
                                            <td>@klient.Nadreden</td>
                                            <td>@klient.NadredenVaziOd?.ToString("dd.MM.yyyy")</td>
                                            <td>@klient.NadredenDo?.ToString("dd.MM.yyyy")</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            @if (!string.IsNullOrEmpty(Model.SearchTerm))
                            {
                                <span>Не се пронајдени резултати за пребарувањето "@Model.SearchTerm".</span>
                            }
                            else
                            {
                                <span>Нема податоци за прикажување.</span>
                            }
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>